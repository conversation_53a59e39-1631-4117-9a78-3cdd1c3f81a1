#!/usr/bin/env python3
"""
修复DeprecationWarning的脚本
处理websockets相关的过时警告
"""

import warnings
import sys
from pathlib import Path

def suppress_websockets_warnings():
    """抑制websockets相关的DeprecationWarning"""
    # 抑制websockets.legacy的警告
    warnings.filterwarnings("ignore", 
                          message="websockets.legacy is deprecated.*", 
                          category=DeprecationWarning)
    
    # 抑制websockets.server.WebSocketServerProtocol的警告
    warnings.filterwarnings("ignore", 
                          message="websockets.server.WebSocketServerProtocol is deprecated.*", 
                          category=DeprecationWarning)
    
    print("✅ 已抑制websockets相关的DeprecationWarning")

def check_websockets_version():
    """检查websockets版本"""
    try:
        import websockets
        version = websockets.__version__
        print(f"📦 websockets版本: {version}")
        
        # 检查是否需要升级
        major, minor = map(int, version.split('.')[:2])
        if major < 12:
            print("⚠️ 建议升级websockets到12.0+版本以避免警告")
            print("   升级命令: pip install --upgrade websockets")
        else:
            print("✅ websockets版本较新")
            
    except ImportError:
        print("❌ 未安装websockets库")
    except Exception as e:
        print(f"❌ 检查websockets版本失败: {e}")

def update_requirements():
    """更新requirements.txt中的websockets版本"""
    requirements_file = Path(__file__).parent.parent / "requirements.txt"
    
    if requirements_file.exists():
        print(f"📝 更新 {requirements_file}")
        
        # 读取现有内容
        with open(requirements_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 更新websockets版本
        updated_lines = []
        websockets_updated = False
        
        for line in lines:
            if line.strip().startswith('websockets'):
                updated_lines.append('websockets>=12.0\n')
                websockets_updated = True
                print("✅ 更新websockets版本要求到>=12.0")
            else:
                updated_lines.append(line)
        
        # 如果没有找到websockets，添加它
        if not websockets_updated:
            updated_lines.append('websockets>=12.0\n')
            print("✅ 添加websockets>=12.0到requirements.txt")
        
        # 写回文件
        with open(requirements_file, 'w', encoding='utf-8') as f:
            f.writelines(updated_lines)
            
        print(f"✅ requirements.txt已更新")
    else:
        print("⚠️ 未找到requirements.txt文件")

if __name__ == "__main__":
    print("🔧 修复DeprecationWarning...")
    print("=" * 50)
    
    # 抑制警告
    suppress_websockets_warnings()
    
    # 检查版本
    check_websockets_version()
    
    # 更新requirements
    update_requirements()
    
    print("\n💡 建议:")
    print("1. 运行 'pip install --upgrade websockets' 升级websockets库")
    print("2. 在Agent启动脚本开头添加警告抑制代码")
    print("3. 考虑升级到更新的A2A SDK版本")