#!/usr/bin/env python3
"""
Host Agent A2A服务器
基于A2A SDK的标准实现
"""

# 抑制websockets相关的DeprecationWarning
import warnings
warnings.filterwarnings("ignore", message="websockets.legacy is deprecated.*", category=DeprecationWarning)
warnings.filterwarnings("ignore", message="websockets.server.WebSocketServerProtocol is deprecated.*", category=DeprecationWarning)

import argparse
import os
import sys
from pathlib import Path
import uvicorn

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入配置管理器
try:
    from config.config_manager import get_config_manager
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False

from a2a.server.apps import A2AStarletteApplication
from a2a.server.request_handlers import DefaultRequestHandler
from a2a.server.tasks import InMemoryTaskStore
from a2a.types import (
    Agent<PERSON>apabilities,
    AgentCard,
    AgentSkill,
)
from .google_adk_integration import HostAgentExecutor


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="Host Agent A2A服务器")
    parser.add_argument('--host', type=str, default='localhost', help='服务器主机地址')
    parser.add_argument('--port', type=int, default=10001, help='服务器端口号')
    parser.add_argument('--model', type=str, help='AI模型名称')
    parser.add_argument('--writing-mode', type=str, default='ai', help='写作模式')
    parser.add_argument('--config', type=str, help='配置文件路径')
    return parser.parse_args()


if __name__ == '__main__':
    # 解析命令行参数
    args = parse_arguments()
    
    # 加载配置
    config = None
    if CONFIG_AVAILABLE:
        config_manager = get_config_manager()
        config = config_manager.get_agent_config('host_agent')
        
        # 应用命令行覆盖
        if config:
            if args.host != 'localhost':
                config.host = args.host
            if args.port != 10001:
                config.port = args.port
            if args.model:
                config.model_name = args.model
            if args.writing_mode != 'ai':
                config.writing_mode = args.writing_mode
    
    # 确定最终配置
    host = config.host if config else args.host
    port = config.port if config else args.port
    model = config.model_name if config else (args.model or 'gemini-2.5-pro-preview-06-05')
    
    # 定义Host Agent的技能
    writing_coordination_skill = AgentSkill(
        id='writing_coordination',
        name='写作协调',
        description='智能协调多个专业Agent完成复杂写作任务',
        tags=['写作协调', '多Agent协作', '智能路由'],
        examples=[
            '帮我设计一个现代都市爱情故事的大纲',
            '为我的小说创建一个霸道总裁男主角',
            '写一个咖啡厅初次相遇的浪漫场景'
        ],
    )
    
    project_management_skill = AgentSkill(
        id='project_management',
        name='项目管理',
        description='创建和管理写作项目，跟踪写作进度和状态',
        tags=['项目管理', '进度追踪', '状态报告'],
        examples=[
            '创建一个现代都市爱情小说项目',
            '查看当前项目的状态和进度',
            '开始一个玄幻修仙小说项目'
        ],
    )
    
    workflow_management_skill = AgentSkill(
        id='workflow_management',
        name='工作流程管理',
        description='管理复杂的多步骤写作工作流程，从大纲到成文的完整流程',
        tags=['工作流程', '多步骤', '流程管理'],
        examples=[
            '执行完整的小说创作工作流程',
            '设计一个三幕结构的故事大纲',
            '规划从大纲到成文的写作步骤'
        ],
    )
    
    ai_writing_skill = AgentSkill(
        id='ai_writing',
        name='AI写作助手',
        description='基于AI的智能写作助手，提供专业的写作建议和指导',
        tags=['AI写作', '内容生成', '写作指导'],
        examples=[
            '帮我优化这段对话，使其更自然',
            '为这个场景添加更多细节描写',
            '给我一些写作技巧和建议'
        ],
    )
    
    multi_agent_skill = AgentSkill(
        id='multi_agent_coordination',
        name='多Agent协调',
        description='协调多个专业Agent完成复杂任务，包括情节规划、角色设定和内容生成',
        tags=['多Agent', '协调', '任务分发'],
        examples=[
            '使用Plot Agent设计故事大纲',
            '让Character Agent创建角色设定',
            '请Content Agent写一个场景描写'
        ],
    )

    # 创建Agent卡片
    agent_card = AgentCard(
        name='Host Agent - 智能写作助手',
        description='基于AI的多Agent写作协调服务，智能理解用户需求，自动选择合适的专业Agent，提供从创意到成文的完整写作支持',
        url=f'http://{host}:{port}/',
        version='1.0.0',
        defaultInputModes=['text'],
        defaultOutputModes=['text'],
        capabilities=AgentCapabilities(streaming=False),
        skills=[
            writing_coordination_skill,
            project_management_skill,
            workflow_management_skill,
            ai_writing_skill,
            multi_agent_skill
        ],
    )

    # 创建请求处理器
    request_handler = DefaultRequestHandler(
        agent_executor=HostAgentExecutor(),
        task_store=InMemoryTaskStore(),
    )

    # 创建A2A服务器应用
    server = A2AStarletteApplication(
        agent_card=agent_card,
        http_handler=request_handler
    )

    # 启动服务器
    print("启动Host Agent A2A服务器...")
    print(f"服务器地址: http://{host}:{port}")
    print(f"AI模型: {model}")
    print(f"写作模式: {config.writing_mode if config else args.writing_mode}")
    print("Agent技能:")
    for skill in agent_card.skills:
        print(f"   - {skill.name}: {skill.description}")
    
    uvicorn.run(server.build(), host=host, port=port)