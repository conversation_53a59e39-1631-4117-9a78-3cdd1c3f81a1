#!/usr/bin/env python3
"""
安装项目依赖的脚本
"""
import subprocess
import sys
import os
from pathlib import Path


def run_command(cmd_list, description):
    """运行命令并显示结果"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(cmd_list, check=True, capture_output=True, text=True)
        print(f"✅ {description} 完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败:")
        print(f"   错误: {e.stderr}")
        return False


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 10):
        print("❌ 需要Python 3.10或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True


def install_requirements():
    """安装requirements.txt中的依赖"""
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ requirements.txt 文件不存在")
        return False
    
    # 升级pip
    if not run_command([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], "升级pip"):
        return False
    
    # 安装依赖
    if not run_command([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], "安装项目依赖"):
        return False
    
    return True


def verify_key_packages():
    """验证关键包是否安装成功"""
    key_packages = [
        "a2a",
        "google.adk", 
        "langgraph",
        "httpx",
        "pydantic",
        "python_dotenv"
    ]
    
    print("\n🔍 验证关键包安装状态:")
    all_installed = True
    
    for package in key_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 未安装或导入失败")
            all_installed = False
    
    return all_installed


def main():
    """主函数"""
    print("📦 A2A网文写作助手 - 依赖安装器")
    print("=" * 40)
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    # 检查是否在项目根目录
    if not Path("requirements.txt").exists():
        print("❌ 请在项目根目录运行此脚本")
        return 1
    
    # 安装依赖
    if not install_requirements():
        print("\n❌ 依赖安装失败，请检查错误信息")
        return 1
    
    # 验证安装
    print("\n" + "=" * 40)
    if verify_key_packages():
        print("\n🎉 所有依赖安装成功！")
        print("\n📋 下一步:")
        print("   1. 检查 .env 文件配置")
        print("   2. 运行: python scripts/start_all.py")
        print("   3. 测试: python scripts/test_writing_system.py")
        return 0
    else:
        print("\n⚠️  部分依赖可能未正确安装，请检查上述错误")
        return 1


if __name__ == "__main__":
    sys.exit(main())