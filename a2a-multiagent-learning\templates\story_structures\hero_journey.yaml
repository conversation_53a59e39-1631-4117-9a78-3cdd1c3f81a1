# 英雄之旅故事结构模板
name: "英雄之旅"
description: "约瑟夫·坎贝尔的英雄之旅模式，适用于成长类和冒险类小说"

structure:
  ordinary_world:
    name: "平凡世界"
    description: "英雄的日常生活，建立角色背景"
    
  call_to_adventure:
    name: "冒险召唤"
    description: "出现问题或挑战，需要英雄行动"
    
  refusal_of_call:
    name: "拒绝召唤"
    description: "英雄犹豫或拒绝，显示恐惧和不确定"
    
  meeting_mentor:
    name: "遇见导师"
    description: "智者出现，给予建议、魔法物品或训练"
    
  crossing_threshold:
    name: "跨越门槛"
    description: "英雄承诺冒险，进入特殊世界"
    
  tests_allies_enemies:
    name: "试炼、盟友与敌人"
    description: "英雄面对挑战，结识朋友和敌人"
    
  approach_inmost_cave:
    name: "深入最深的洞穴"
    description: "准备面对最大的恐惧或困难"
    
  ordeal:
    name: "磨难"
    description: "英雄面对死亡恐惧或最大的恐惧"
    
  reward:
    name: "奖赏"
    description: "英雄在磨难中幸存，获得奖赏"
    
  road_back:
    name: "归途"
    description: "英雄开始返回平凡世界的旅程"
    
  resurrection:
    name: "复活"
    description: "最后的试炼，英雄获得重生"
    
  return_elixir:
    name: "携药而归"
    description: "英雄带着能造福平凡世界的东西回归"

genres:
  - "玄幻修仙"
  - "科幻冒险"
  - "奇幻魔法"
  - "武侠江湖"