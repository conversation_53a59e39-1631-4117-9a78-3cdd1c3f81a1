#!/usr/bin/env python3
"""
Plot Agent A2A集成测试
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_plot_agent_a2a():
    """测试Plot Agent A2A服务器"""
    print("🧪 开始测试Plot Agent A2A服务器...")
    print("=" * 50)
    
    try:
        # 测试导入
        print("📦 测试模块导入...")
        from plot_agent.plot_agent_core import PlotAgent
        from plot_agent.plot_agent_executor import PlotAgentExecutor
        print("✅ 模块导入成功")
        
        # 测试Plot Agent核心功能
        print("\n🎭 测试Plot Agent核心功能...")
        agent = PlotAgent()
        
        test_query = "帮我生成一个现代都市爱情故事的大纲"
        print(f"📝 测试查询: {test_query}")
        
        result_parts = []
        async for event in agent.stream(test_query):
            if event['content']:
                result_parts.append(event['content'])
            if event['done']:
                break
        
        result = ''.join(result_parts)
        print(f"✅ 获得响应 ({len(result)} 字符)")
        print(f"📄 响应预览: {result[:200]}...")
        
        # 测试AgentExecutor
        print("\n🔧 测试AgentExecutor...")
        executor = PlotAgentExecutor()
        print("✅ AgentExecutor创建成功")
        
        print("\n🎉 所有测试通过！")
        print("\n💡 启动A2A服务器命令:")
        print("   python plot_agent/__main_a2a__.py")
        print("\n🌐 服务器地址: http://localhost:10001")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = asyncio.run(test_plot_agent_a2a())
    if not success:
        sys.exit(1)