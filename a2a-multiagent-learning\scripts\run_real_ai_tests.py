#!/usr/bin/env python3
"""
真实AI API测试启动脚本
自动检查环境、启动Agent服务器并运行测试
"""

import asyncio
import subprocess
import sys
import time
import os
import signal
from pathlib import Path
from typing import List, Dict, Any
import aiohttp

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class RealAITestRunner:
    """真实AI API测试运行器"""
    
    def __init__(self):
        self.agent_processes: List[subprocess.Popen] = []
        self.agent_urls = {
            'plot_agent': 'http://127.0.0.1:10002',
            'character_agent': 'http://127.0.0.1:10003', 
            'content_agent': 'http://127.0.0.1:10004',
            'host_agent': 'http://127.0.0.1:10001'
        }
        self.startup_timeout = 60  # 60秒启动超时
        
    async def run_complete_test_suite(self):
        """运行完整的测试套件"""
        print("[START] A2A多Agent系统真实AI API测试套件")
        print("=" * 60)
        
        try:
            # 步骤1: 检查环境
            if not await self.check_environment():
                return False
            
            # 步骤2: 启动Agent服务器
            if not await self.start_agent_servers():
                return False
            
            # 步骤3: 等待服务器就绪
            if not await self.wait_for_agents_ready():
                return False
            
            # 步骤4: 运行测试
            await self.run_tests()
            
            return True
            
        except KeyboardInterrupt:
            print("\n[INTERRUPT] 用户中断测试")
            return False
        except Exception as e:
            print(f"\n[ERROR] 测试运行失败: {e}")
            return False
        finally:
            # 清理资源
            await self.cleanup()
    
    async def check_environment(self) -> bool:
        """检查环境配置"""
        print("\n[CHECK] 检查环境配置...")
        print("-" * 40)
        
        # 检查Python依赖
        required_packages = [
            ('aiohttp', 'aiohttp'),
            ('openai', 'openai'), 
            ('python-dotenv', 'dotenv')
        ]
        missing_packages = []
        
        for package_name, import_name in required_packages:
            try:
                __import__(import_name)
                print(f"✅ {package_name}: 已安装")
            except ImportError:
                missing_packages.append(package_name)
                print(f"❌ {package_name}: 未安装")
        
        if missing_packages:
            print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
            print("请运行: pip install " + " ".join(missing_packages))
            return False
        
        # 检查环境变量
        env_file = project_root / '.env'
        if not env_file.exists():
            print(f"⚠️ 环境变量文件不存在: {env_file}")
            print("请复制 .env.example 到 .env 并配置API密钥")
            return False
        
        # 检查API密钥
        from dotenv import load_dotenv
        load_dotenv()
        
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            print("⚠️ 未找到OPENAI_API_KEY环境变量")
            print("请在.env文件中配置API密钥")
            return False
        else:
            print(f"✅ API密钥: {api_key[:20]}...")
        
        print("✅ 环境检查通过")
        return True
    
    async def start_agent_servers(self) -> bool:
        """启动Agent服务器"""
        print("\n🚀 启动Agent服务器...")
        print("-" * 40)
        
        agents = [
            ('plot_agent', 10002),
            ('character_agent', 10003),
            ('content_agent', 10004),
            ('host_agent', 10001)
        ]
        
        for agent_name, port in agents:
            try:
                # 检查端口是否已被占用
                if await self.is_port_in_use(port):
                    print(f"⚠️ {agent_name}: 端口{port}已被占用，跳过启动")
                    continue
                
                # 启动Agent服务器
                cmd = [
                    sys.executable, '-m', f'{agent_name}.__main__',
                    '--port', str(port)
                ]
                
                process = subprocess.Popen(
                    cmd,
                    cwd=project_root,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    encoding='utf-8',
                    errors='ignore'
                )
                
                self.agent_processes.append(process)
                print(f"🚀 {agent_name}: 启动中... (PID: {process.pid})")
                
                # 等待Agent启动，避免端口冲突和资源竞争
                await asyncio.sleep(5)
                
            except Exception as e:
                print(f"❌ {agent_name}: 启动失败 - {e}")
                return False
        
        print(f"✅ 已启动 {len(self.agent_processes)} 个Agent服务器")
        return True
    
    async def is_port_in_use(self, port: int) -> bool:
        """检查端口是否被占用"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f'http://localhost:{port}/', timeout=aiohttp.ClientTimeout(total=1)) as response:
                    return True
        except:
            return False
    
    async def wait_for_agents_ready(self) -> bool:
        """等待Agent服务器就绪"""
        print("\n⏳ 等待Agent服务器就绪...")
        print("-" * 40)
        
        start_time = time.time()
        ready_agents = set()
        
        while time.time() - start_time < self.startup_timeout:
            for agent_name, url in self.agent_urls.items():
                if agent_name in ready_agents:
                    continue
                
                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.get(f"{url}/", timeout=aiohttp.ClientTimeout(total=2)) as response:
                            print(f"🔍 {agent_name}: HTTP {response.status}")
                            # A2A servers return 405 for GET requests to root, which means they're running
                            if response.status in [200, 405]:
                                if response.status == 200:
                                    try:
                                        agent_card = await response.json()
                                        ready_agents.add(agent_name)
                                        print(f"✅ {agent_name}: 就绪 ({agent_card.get('name', 'Unknown')})")
                                    except:
                                        ready_agents.add(agent_name)
                                        print(f"✅ {agent_name}: 就绪")
                                else:
                                    ready_agents.add(agent_name)
                                    print(f"✅ {agent_name}: 就绪 (A2A服务器)")
                except Exception as e:
                    print(f"🔍 {agent_name}: 连接失败 - {e}")
                    pass
            
            if len(ready_agents) == len(self.agent_urls):
                print(f"🎉 所有Agent服务器已就绪 ({len(ready_agents)}/{len(self.agent_urls)})")
                return True
            
            await asyncio.sleep(2)
        
        # 超时处理
        not_ready = set(self.agent_urls.keys()) - ready_agents
        print(f"⚠️ 超时: {len(not_ready)} 个Agent未就绪: {', '.join(not_ready)}")
        
        if len(ready_agents) >= 2:  # 至少2个Agent就绪就继续
            print(f"🔄 继续测试 ({len(ready_agents)} 个Agent可用)")
            return True
        else:
            print("❌ 可用Agent数量不足，无法进行测试")
            return False
    
    async def run_tests(self):
        """运行测试"""
        print("\n🧪 开始运行真实AI API测试...")
        print("-" * 40)
        
        # 运行真实AI集成测试
        try:
            from tests.test_real_ai_integration import RealAIAgentTester
            
            ai_tester = RealAIAgentTester()
            await ai_tester.run_all_tests()
            
        except ImportError as e:
            print(f"❌ 无法导入AI测试模块: {e}")
        except Exception as e:
            print(f"❌ AI测试执行失败: {e}")
        
        # 运行端到端测试
        print("\n" + "=" * 60)
        print("🔄 运行端到端写作系统测试...")
        print("-" * 40)
        
        try:
            from scripts.test_writing_system import WritingSystemTester
            
            system_tester = WritingSystemTester()
            await system_tester.run_all_tests()
            
        except ImportError as e:
            print(f"❌ 无法导入系统测试模块: {e}")
        except Exception as e:
            print(f"❌ 系统测试执行失败: {e}")
    
    async def cleanup(self):
        """清理资源"""
        print("\n🧹 清理资源...")
        print("-" * 40)
        
        # 终止Agent进程
        for i, process in enumerate(self.agent_processes):
            try:
                if process.poll() is None:  # 进程仍在运行
                    print(f"🛑 终止Agent进程 {i+1} (PID: {process.pid})")
                    process.terminate()
                    
                    # 等待进程优雅退出
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        print(f"⚠️ 强制终止进程 {process.pid}")
                        process.kill()
                        process.wait()
                        
            except Exception as e:
                print(f"⚠️ 清理进程时出错: {e}")
        
        self.agent_processes.clear()
        print("✅ 资源清理完成")

def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n⚠️ 收到信号 {signum}，正在清理...")
    sys.exit(0)

async def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    runner = RealAITestRunner()
    
    print("🤖 A2A多Agent系统真实AI API测试")
    print("=" * 60)
    print("此测试将:")
    print("1. 检查环境配置和API密钥")
    print("2. 自动启动所有Agent服务器")
    print("3. 运行真实AI API集成测试")
    print("4. 运行端到端写作系统测试")
    print("5. 自动清理资源")
    print("=" * 60)
    
    # 询问用户确认
    try:
        confirm = input("\n是否继续? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("❌ 用户取消测试")
            return
    except KeyboardInterrupt:
        print("\n❌ 用户中断")
        return
    
    # 运行测试套件
    success = await runner.run_complete_test_suite()
    
    if success:
        print("\n🎉 测试套件执行完成！")
    else:
        print("\n❌ 测试套件执行失败")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())