#!/usr/bin/env python3
"""
测试Remote Agent连接管理器
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_remote_agent_connection():
    """测试Remote Agent连接管理器"""
    print("🔗 测试Remote Agent连接管理器...")
    print("=" * 60)
    
    try:
        # 导入连接管理器
        from host_agent.remote_agent_connection import (
            RemoteAgentConnections, 
            AgentInfo, 
            AgentStatus,
            get_connection_manager,
            shutdown_connection_manager
        )
        print("✅ 连接管理器模块导入成功")
        
        # 测试AgentInfo数据类
        print("\n📋 测试AgentInfo数据类...")
        agent_info = AgentInfo(
            name="Test Agent",
            url="http://localhost:10002/",
            port=10002
        )
        print(f"✅ Agent名称: {agent_info.name}")
        print(f"✅ Agent URL: {agent_info.url}")
        print(f"✅ Agent端口: {agent_info.port}")
        print(f"✅ Agent状态: {agent_info.status.value}")
        
        # 测试连接管理器初始化
        print("\n🚀 测试连接管理器初始化...")
        manager = RemoteAgentConnections()
        await manager.initialize()
        print("✅ 连接管理器初始化成功")
        
        # 测试默认Agent注册
        print("\n📝 测试默认Agent注册...")
        agent_status = await manager.get_all_agent_status()
        print(f"✅ 注册的Agent数量: {len(agent_status)}")
        for agent_id, status in agent_status.items():
            print(f"   - {agent_id}: {status['name']} ({status['status']})")
        
        # 测试Agent发现功能
        print("\n🔍 测试Agent发现功能...")
        # 注意：这些URL在实际环境中可能不可用，这是正常的
        test_urls = [
            "http://localhost:10002/",
            "http://localhost:10003/", 
            "http://localhost:10004/"
        ]
        discovered = await manager.discover_agents(test_urls)
        print(f"✅ 发现的Agent数量: {len(discovered)}")
        for agent_id in discovered:
            print(f"   - 发现: {agent_id}")
        
        # 测试可用Agent列表
        print("\n📊 测试可用Agent列表...")
        available = await manager.get_available_agents()
        print(f"✅ 可用Agent数量: {len(available)}")
        for agent_id in available:
            capabilities = await manager.get_agent_capabilities(agent_id)
            skills = await manager.get_agent_skills(agent_id)
            print(f"   - {agent_id}: {len(capabilities)}个能力, {len(skills)}个技能")
        
        # 测试写作会话创建
        print("\n📝 测试写作会话创建...")
        session_data = await manager.create_writing_session(
            "test_session_001",
            {"project": "测试小说", "genre": "现代都市"}
        )
        print(f"✅ 会话ID: {session_data['session_id']}")
        print(f"✅ 会话状态: {session_data['status']}")
        print(f"✅ 关联Agent: {session_data['agents']}")
        
        # 测试写作上下文获取
        print("\n📖 测试写作上下文获取...")
        context = await manager.get_writing_context("test_session_001")
        if context:
            print(f"✅ 会话ID: {context['session_id']}")
            print(f"✅ 可用Agent: {context['available_agents']}")
        
        # 测试全局连接管理器
        print("\n🌐 测试全局连接管理器...")
        global_manager = await get_connection_manager()
        print(f"✅ 全局管理器获取成功")
        
        global_status = await global_manager.get_all_agent_status()
        print(f"✅ 全局管理器Agent数量: {len(global_status)}")
        
        # 清理资源
        print("\n🧹 清理测试资源...")
        await manager.shutdown()
        await shutdown_connection_manager()
        print("✅ 资源清理完成")
        
        print("\n🎉 Remote Agent连接管理器测试完成！")
        print("\n💡 注意事项:")
        print("   - Agent连接失败是正常的，因为实际的Agent服务器可能未启动")
        print("   - 连接管理器的核心功能（注册、发现、状态管理）都正常工作")
        print("   - 要测试实际连接，请先启动对应的Agent服务器")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_connection_scenarios():
    """测试各种连接场景"""
    print("\n🔄 测试连接场景...")
    print("=" * 40)
    
    try:
        from host_agent.remote_agent_connection import (
            RemoteAgentConnection,
            AgentInfo,
            AgentStatus
        )
        
        # 测试连接到不存在的服务器
        print("📡 测试连接到不存在的服务器...")
        fake_agent = AgentInfo(
            name="Fake Agent",
            url="http://localhost:99999/",
            port=99999
        )
        
        connection = RemoteAgentConnection(fake_agent, max_retries=1, timeout=5)
        success = await connection.connect()
        print(f"✅ 连接结果: {success} (预期为False)")
        print(f"✅ Agent状态: {fake_agent.status.value}")
        print(f"✅ 错误信息: {fake_agent.last_error}")
        
        await connection.disconnect()
        
        # 测试健康检查
        print("\n🏥 测试健康检查...")
        health = await connection.health_check()
        print(f"✅ 健康检查结果: {health} (预期为False)")
        
        print("✅ 连接场景测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 连接场景测试失败: {e}")
        return False

if __name__ == "__main__":
    async def main():
        success1 = await test_remote_agent_connection()
        success2 = await test_connection_scenarios()
        
        if success1 and success2:
            print("\n🎉 所有测试通过！")
        else:
            print("\n❌ 部分测试失败")
            sys.exit(1)
    
    asyncio.run(main())