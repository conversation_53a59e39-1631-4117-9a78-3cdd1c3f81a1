#!/usr/bin/env python3
"""
测试A2A通信监控系统
"""

import asyncio
import sys
import time
import random
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from monitoring.communication_monitor import get_communication_monitor
from monitoring.quality_assessor import get_quality_assessor
from monitoring.agent_monitor_integration import MonitoredAgentClient, create_monitoring_report

class MonitoringSystemTester:
    """监控系统测试器"""
    
    def __init__(self):
        self.monitor = get_communication_monitor()
        self.quality_assessor = get_quality_assessor()
        
    async def test_basic_monitoring(self):
        """测试基础监控功能"""
        print("🧪 测试基础监控功能...")
        
        # 模拟Agent通信
        agents = ["plot_agent", "character_agent", "content_agent", "host_agent"]
        
        for i in range(10):
            source = random.choice(agents)
            target = random.choice([a for a in agents if a != source])
            task_id = f"test_task_{i}"
            context_id = f"test_context_{i}"
            
            # 记录请求
            self.monitor.log_request(
                source_agent=source,
                target_agent=target,
                task_id=task_id,
                context_id=context_id,
                request_data={"type": "test_request", "content": f"测试请求 {i}"}
            )
            
            # 模拟处理时间
            await asyncio.sleep(random.uniform(0.1, 0.5))
            
            # 记录响应
            success = random.choice([True, True, True, False])  # 75%成功率
            duration_ms = random.uniform(100, 2000)
            
            if success:
                self.monitor.log_response(
                    source_agent=source,
                    target_agent=target,
                    task_id=task_id,
                    context_id=context_id,
                    response_data={"status": "success", "result": f"测试结果 {i}"},
                    duration_ms=duration_ms,
                    success=True
                )
            else:
                self.monitor.log_error(
                    source_agent=source,
                    target_agent=target,
                    task_id=task_id,
                    context_id=context_id,
                    error_message=f"测试错误 {i}"
                )
        
        print("✅ 基础监控测试完成")
    
    async def test_task_monitoring(self):
        """测试任务监控功能"""
        print("🧪 测试任务监控功能...")
        
        # 模拟复杂任务
        task_types = ["plot_generation", "character_creation", "content_writing", "story_outline"]
        
        for i in range(5):
            task_id = f"complex_task_{i}"
            context_id = f"complex_context_{i}"
            task_type = random.choice(task_types)
            total_steps = random.randint(3, 8)
            
            # 开始任务
            self.monitor.log_task_start(
                task_id=task_id,
                context_id=context_id,
                task_type=task_type,
                involved_agents=["host_agent", random.choice(["plot_agent", "character_agent", "content_agent"])],
                total_steps=total_steps
            )
            
            # 模拟任务进度
            for step in range(1, total_steps + 1):
                await asyncio.sleep(random.uniform(0.2, 0.8))
                
                status = "working" if step < total_steps else "completed"
                quality_score = random.uniform(70, 95) if step == total_steps else None
                
                self.monitor.log_task_progress(
                    task_id=task_id,
                    steps_completed=step,
                    status=status,
                    quality_score=quality_score
                )
            
            # 完成任务
            success = random.choice([True, True, True, False])  # 75%成功率
            quality_score = random.uniform(60, 95) if success else None
            
            self.monitor.log_task_complete(
                task_id=task_id,
                success=success,
                quality_score=quality_score,
                user_feedback="测试反馈" if success else None
            )
        
        print("✅ 任务监控测试完成")
    
    def test_quality_assessment(self):
        """测试质量评估功能"""
        print("🧪 测试质量评估功能...")
        
        # 测试内容样本
        test_contents = [
            {
                "content": """阳光透过落地窗洒在咖啡厅里，空气中弥漫着淡淡的咖啡香。林雨萱坐在靠窗的位置，专注地看着手中的建筑设计图纸。
                
突然，一个高大的身影挡住了她的光线。她抬起头，看到一个穿着深色西装的男人正站在她面前，眉头微皱。

"不好意思，这里有人吗？"男人的声音低沉磁性。

林雨萱看了看周围，咖啡厅里确实已经座无虚席。她点点头，示意他可以坐下。""",
                "type": "scene",
                "expected_score_range": (70, 90)
            },
            {
                "content": """陈浩然，32岁，集团总裁。他有着高大英俊的外表，深邃的眼神中透露着商人的精明。性格霸道但内心温柔，对工作极其专注，但在感情方面却显得笨拙。出身商业世家，年轻有为，是商界的传奇人物。""",
                "type": "character",
                "expected_score_range": (60, 85)
            },
            {
                "content": """这是一个现代都市爱情故事。男主角是成功的企业家，女主角是独立的建筑设计师。他们在咖啡厅初次相遇，经历误会、了解、相爱的过程，最终找到事业与爱情的平衡。""",
                "type": "plot",
                "expected_score_range": (50, 75)
            }
        ]
        
        for i, test_case in enumerate(test_contents):
            print(f"  测试内容 {i+1}: {test_case['type']}")
            
            # 进行质量评估
            metrics = self.quality_assessor.assess_content(
                content=test_case["content"],
                content_type=test_case["type"]
            )
            
            print(f"    总体评分: {metrics.overall_score:.1f}")
            print(f"    可读性: {metrics.readability_score:.1f}")
            print(f"    创意性: {metrics.creativity_score:.1f}")
            print(f"    连贯性: {metrics.coherence_score:.1f}")
            
            if metrics.strengths:
                print(f"    优点: {metrics.strengths[0]}")
            if metrics.suggestions:
                print(f"    建议: {metrics.suggestions[0]}")
            
            # 验证评分范围
            expected_min, expected_max = test_case["expected_score_range"]
            if expected_min <= metrics.overall_score <= expected_max:
                print(f"    ✅ 评分在预期范围内")
            else:
                print(f"    ⚠️ 评分超出预期范围 ({expected_min}-{expected_max})")
            
            print()
        
        print("✅ 质量评估测试完成")
    
    def test_system_stats(self):
        """测试系统统计功能"""
        print("🧪 测试系统统计功能...")
        
        # 获取系统统计
        stats = self.monitor.get_system_stats()
        
        print("📊 系统统计信息:")
        print(f"  Agent总数: {stats['agents']['total']}")
        print(f"  在线Agent: {stats['agents']['online']}")
        print(f"  任务总数: {stats['tasks']['total']}")
        print(f"  完成任务: {stats['tasks']['completed']}")
        print(f"  任务成功率: {stats['tasks']['success_rate']:.1f}%")
        print(f"  总请求数: {stats['communication']['total_requests']}")
        print(f"  错误率: {stats['communication']['error_rate']:.1f}%")
        print(f"  平均响应时间: {stats['communication']['avg_response_time']:.1f}ms")
        
        # 获取Agent指标
        agent_metrics = self.monitor.get_agent_metrics()
        print(f"\n🤖 Agent详细指标 ({len(agent_metrics)}个):")
        for name, metrics in agent_metrics.items():
            print(f"  {name}:")
            print(f"    状态: {metrics.status}")
            print(f"    请求数: {metrics.total_requests}")
            print(f"    成功率: {(metrics.successful_requests/max(1, metrics.total_requests)*100):.1f}%")
            print(f"    平均响应时间: {metrics.avg_response_time:.1f}ms")
        
        # 获取最近事件
        recent_events = self.monitor.get_recent_events(limit=5)
        print(f"\n📝 最近事件 ({len(recent_events)}条):")
        for event in recent_events:
            timestamp = event.timestamp.strftime("%H:%M:%S")
            print(f"  [{timestamp}] {event.event_type}: {event.source_agent} -> {event.target_agent}")
        
        print("✅ 系统统计测试完成")
    
    def test_report_generation(self):
        """测试报告生成功能"""
        print("🧪 测试报告生成功能...")
        
        # 生成监控报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"test_monitoring_report_{timestamp}.json"
        
        try:
            report = create_monitoring_report(report_file)
            
            print(f"📄 报告已生成: {report_file}")
            print(f"  报告时间: {report['timestamp']}")
            print(f"  Agent数量: {len(report['agent_metrics'])}")
            print(f"  事件数量: {len(report['recent_events'])}")
            
            # 验证报告结构
            required_keys = ["timestamp", "system_stats", "agent_metrics", "recent_events"]
            missing_keys = [key for key in required_keys if key not in report]
            
            if not missing_keys:
                print("  ✅ 报告结构完整")
            else:
                print(f"  ❌ 报告缺少字段: {missing_keys}")
            
        except Exception as e:
            print(f"  ❌ 报告生成失败: {e}")
        
        print("✅ 报告生成测试完成")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始监控系统测试")
        print("=" * 60)
        
        start_time = time.time()
        
        try:
            # 运行各项测试
            await self.test_basic_monitoring()
            print()
            
            await self.test_task_monitoring()
            print()
            
            self.test_quality_assessment()
            print()
            
            self.test_system_stats()
            print()
            
            self.test_report_generation()
            print()
            
            # 测试总结
            duration = time.time() - start_time
            print("=" * 60)
            print(f"🎉 监控系统测试完成! (耗时: {duration:.1f}秒)")
            
            # 最终统计
            final_stats = self.monitor.get_system_stats()
            print(f"📊 最终统计:")
            print(f"  总事件数: {final_stats['events']['total']}")
            print(f"  Agent数: {final_stats['agents']['total']}")
            print(f"  任务数: {final_stats['tasks']['total']}")
            print(f"  成功率: {final_stats['tasks']['success_rate']:.1f}%")
            
        except Exception as e:
            print(f"❌ 测试过程中出现异常: {e}")
            import traceback
            traceback.print_exc()

async def main():
    """主函数"""
    tester = MonitoringSystemTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())