#!/usr/bin/env python3
"""
测试写作协调器的智能路由逻辑 - 任务9专项测试
重点测试智能分析、路由选择、协调能力等核心功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_intelligent_request_analysis():
    """测试智能请求分析功能"""
    print("🧠 测试智能请求分析功能...")
    print("=" * 60)
    
    try:
        from host_agent.writing_coordinator import WritingCoordinator, WritingTaskType
        
        coordinator = WritingCoordinator()
        
        # 测试各种复杂的写作请求
        test_cases = [
            {
                "request": "帮我设计一个现代都市爱情故事的大纲，包含三幕结构和主要冲突点",
                "expected": WritingTaskType.PLOT_PLANNING,
                "description": "复合情节规划请求"
            },
            {
                "request": "为我的小说创建一个霸道总裁男主角，需要详细的外貌、性格和背景设定",
                "expected": WritingTaskType.CHARACTER_CREATION,
                "description": "详细角色创建请求"
            },
            {
                "request": "写一个咖啡厅初次相遇的浪漫场景，要有环境描写和人物对话",
                "expected": WritingTaskType.CONTENT_GENERATION,
                "description": "场景内容生成请求"
            },
            {
                "request": "优化这段对话，让角色的性格更加鲜明，语言更符合人物身份",
                "expected": WritingTaskType.CONTENT_REFINEMENT,
                "description": "内容优化请求"
            },
            {
                "request": "我想写一个关于时间旅行的科幻小说，需要构思整体框架",
                "expected": WritingTaskType.PLOT_PLANNING,
                "description": "科幻题材规划请求"
            },
            {
                "request": "设计男女主角之间的情感发展线，包括相遇、误会、和解的过程",
                "expected": WritingTaskType.CHARACTER_CREATION,
                "description": "角色关系设计请求"
            },
            {
                "request": "根据已有大纲写第一章的开头，要吸引读者注意力",
                "expected": WritingTaskType.CONTENT_GENERATION,
                "description": "章节写作请求"
            },
            {
                "request": "分析这个故事的情节冲突是否足够，如何增强戏剧张力",
                "expected": WritingTaskType.PLOT_PLANNING,
                "description": "情节分析请求"
            }
        ]
        
        print(f"📋 测试 {len(test_cases)} 个智能分析案例:")
        
        correct_predictions = 0
        for i, case in enumerate(test_cases, 1):
            request = case["request"]
            expected = case["expected"]
            description = case["description"]
            
            # 执行智能分析
            predicted = coordinator.analyze_writing_request(request)
            
            # 检查预测结果
            is_correct = predicted == expected
            if is_correct:
                correct_predictions += 1
            
            status_icon = "✅" if is_correct else "❌"
            print(f"\n{status_icon} 案例 {i}: {description}")
            print(f"   请求: {request[:50]}...")
            print(f"   预期: {expected.value}")
            print(f"   预测: {predicted.value}")
            
            if not is_correct:
                print(f"   ⚠️ 分析偏差，需要优化算法")
        
        # 计算准确率
        accuracy = (correct_predictions / len(test_cases)) * 100
        print(f"\n📊 智能分析准确率: {correct_predictions}/{len(test_cases)} ({accuracy:.1f}%)")
        
        if accuracy >= 80:
            print("✅ 智能分析功能表现优秀")
        elif accuracy >= 60:
            print("⚠️ 智能分析功能表现一般，建议优化")
        else:
            print("❌ 智能分析功能需要改进")
        
        return accuracy >= 60
        
    except Exception as e:
        print(f"❌ 智能分析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_intelligent_agent_routing():
    """测试智能Agent路由功能"""
    print("\n🎯 测试智能Agent路由功能...")
    print("=" * 60)
    
    try:
        from host_agent.writing_coordinator import WritingCoordinator, WritingTaskType
        
        coordinator = WritingCoordinator()
        
        # 模拟不同的Agent可用性场景
        test_scenarios = [
            {
                "name": "所有Agent可用",
                "available_agents": ["plot_agent", "character_agent", "content_agent"],
                "description": "理想情况下的路由选择"
            },
            {
                "name": "部分Agent可用",
                "available_agents": ["plot_agent", "content_agent"],
                "description": "Character Agent不可用时的路由策略"
            },
            {
                "name": "单一Agent可用",
                "available_agents": ["content_agent"],
                "description": "只有Content Agent可用时的降级处理"
            },
            {
                "name": "无Agent可用",
                "available_agents": [],
                "description": "所有Agent都不可用的异常情况"
            }
        ]
        
        # 测试任务类型
        test_tasks = [
            (WritingTaskType.PLOT_PLANNING, "情节规划任务"),
            (WritingTaskType.CHARACTER_CREATION, "角色创建任务"),
            (WritingTaskType.CONTENT_GENERATION, "内容生成任务"),
            (WritingTaskType.MIXED_TASK, "混合任务")
        ]
        
        print(f"📋 测试 {len(test_scenarios)} 个路由场景:")
        
        successful_routes = 0
        total_routes = 0
        
        for scenario in test_scenarios:
            print(f"\n🎭 场景: {scenario['name']}")
            print(f"   可用Agent: {scenario['available_agents']}")
            
            for task_type, task_desc in test_tasks:
                total_routes += 1
                
                # 执行路由选择
                selected_agent = coordinator.select_best_agent(task_type, scenario['available_agents'])
                
                # 分析路由结果
                if selected_agent:
                    # 检查选择是否合理
                    recommended_agents = coordinator.routing_rules.get(task_type, [])
                    is_optimal = selected_agent in recommended_agents
                    is_available = selected_agent in scenario['available_agents']
                    
                    if is_available:
                        successful_routes += 1
                        status_icon = "✅" if is_optimal else "⚠️"
                        optimality = "最优" if is_optimal else "可用"
                        print(f"   {status_icon} {task_desc} → {selected_agent} ({optimality})")
                    else:
                        print(f"   ❌ {task_desc} → {selected_agent} (不可用!)")
                else:
                    if scenario['available_agents']:
                        print(f"   ❌ {task_desc} → 无选择 (路由失败)")
                    else:
                        print(f"   ⚠️ {task_desc} → 无选择 (无可用Agent)")
                        successful_routes += 1  # 这种情况下返回None是正确的
        
        # 计算路由成功率
        success_rate = (successful_routes / total_routes) * 100
        print(f"\n📊 路由成功率: {successful_routes}/{total_routes} ({success_rate:.1f}%)")
        
        if success_rate >= 90:
            print("✅ 智能路由功能表现优秀")
        elif success_rate >= 70:
            print("⚠️ 智能路由功能表现一般")
        else:
            print("❌ 智能路由功能需要改进")
        
        return success_rate >= 70
        
    except Exception as e:
        print(f"❌ 智能路由测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_session_management():
    """测试会话管理功能"""
    print("\n📝 测试会话管理功能...")
    print("=" * 60)
    
    try:
        from host_agent.writing_coordinator import get_writing_coordinator, shutdown_writing_coordinator
        
        # 获取协调器
        coordinator = await get_writing_coordinator()
        
        # 测试会话创建
        print("📋 测试会话创建...")
        sessions = []
        
        for i in range(3):
            session_id = f"test_session_{i+1}"
            project_name = f"测试项目_{i+1}"
            genre = ["现代都市", "古代言情", "玄幻修仙"][i]
            
            session = await coordinator.create_writing_session(
                session_id=session_id,
                project_name=project_name,
                genre=genre,
                context={"test_id": i+1, "priority": "high"}
            )
            
            sessions.append(session)
            print(f"✅ 会话 {i+1} 创建成功: {session.session_id} ({session.genre})")
        
        # 测试任务添加
        print(f"\n📋 测试任务添加...")
        total_tasks = 0
        
        for i, session in enumerate(sessions):
            tasks = [
                f"为{session.genre}小说设计故事大纲",
                f"创建{session.genre}风格的主要角色",
                f"写一个{session.genre}的开头场景"
            ]
            
            for j, task_content in enumerate(tasks):
                task = await coordinator.add_writing_task(
                    session_id=session.session_id,
                    content=task_content,
                    context={"task_order": j+1}
                )
                total_tasks += 1
                print(f"✅ 任务添加: {task.task_id} ({task.task_type.value})")
        
        # 测试会话状态查询
        print(f"\n📊 测试会话状态查询...")
        for session in sessions:
            status = await coordinator.get_session_status(session.session_id)
            if status:
                print(f"✅ 会话状态: {status['session_id']}")
                print(f"   项目: {status['project_name']}")
                print(f"   任务: {status['total_tasks']}个")
                print(f"   状态: {status['status']}")
            else:
                print(f"❌ 无法获取会话状态: {session.session_id}")
        
        # 测试全局状态
        print(f"\n🌐 测试全局状态...")
        print(f"✅ 活动会话数: {len(coordinator.active_sessions)}")
        print(f"✅ 任务队列长度: {len(coordinator.task_queue)}")
        print(f"✅ 总任务数: {total_tasks}")
        
        # 清理资源
        await shutdown_writing_coordinator()
        
        print(f"\n🎉 会话管理功能测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 会话管理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_workflow_design():
    """测试工作流程设计功能"""
    print("\n🔄 测试工作流程设计功能...")
    print("=" * 60)
    
    try:
        from host_agent.writing_coordinator import WritingCoordinator
        
        coordinator = WritingCoordinator()
        
        # 定义不同类型的写作工作流程
        workflows = [
            {
                "name": "现代都市爱情小说",
                "steps": [
                    "确定现代都市爱情小说的主题和风格定位",
                    "设计男女主角的基本人设和背景故事",
                    "规划三幕式故事结构和主要情节点",
                    "创建支撑角色和次要情节线",
                    "写作第一章的开头场景",
                    "生成几个关键场景的详细描写",
                    "创作主要角色间的重要对话",
                    "优化整体内容的连贯性和可读性"
                ]
            },
            {
                "name": "玄幻修仙小说",
                "steps": [
                    "构建玄幻修仙世界的基本设定和规则",
                    "设计主角的修炼体系和成长路线",
                    "规划修仙世界的势力分布和冲突",
                    "创建各个境界的修炼者角色",
                    "写作主角初入修仙世界的情节",
                    "描写修炼场景和战斗场面",
                    "设计宗门、秘境等关键场所",
                    "完善修仙体系的逻辑性"
                ]
            },
            {
                "name": "悬疑推理小说",
                "steps": [
                    "设计核心悬疑案件和谜题",
                    "创建侦探主角和相关角色",
                    "规划线索布局和推理过程",
                    "设计红鲱鱼和误导信息",
                    "写作案件发生的开场情节",
                    "描写调查和推理的关键场景",
                    "创作角色间的审讯对话",
                    "完善逻辑链条和真相揭示"
                ]
            }
        ]
        
        print(f"📋 测试 {len(workflows)} 个工作流程设计:")
        
        successful_workflows = 0
        
        for workflow in workflows:
            print(f"\n📚 工作流程: {workflow['name']}")
            print(f"   步骤数: {len(workflow['steps'])}")
            
            # 分析每个步骤的任务类型和Agent分配
            step_analysis = []
            agent_distribution = {"plot_agent": 0, "character_agent": 0, "content_agent": 0}
            
            for i, step in enumerate(workflow['steps'], 1):
                # 分析步骤类型
                task_type = coordinator.analyze_writing_request(step)
                
                # 选择Agent
                available_agents = ["plot_agent", "character_agent", "content_agent"]
                selected_agent = coordinator.select_best_agent(task_type, available_agents)
                
                if selected_agent:
                    agent_distribution[selected_agent] += 1
                
                step_analysis.append({
                    "step": i,
                    "task_type": task_type.value,
                    "agent": selected_agent,
                    "content": step[:50] + "..."
                })
            
            # 显示步骤分析
            print(f"   步骤分析:")
            for analysis in step_analysis[:3]:  # 只显示前3个步骤
                print(f"     {analysis['step']}. {analysis['task_type']} → {analysis['agent']}")
                print(f"        {analysis['content']}")
            
            if len(step_analysis) > 3:
                print(f"     ... 还有 {len(step_analysis) - 3} 个步骤")
            
            # 显示Agent工作负载分布
            total_steps = len(workflow['steps'])
            print(f"   Agent负载分布:")
            for agent, count in agent_distribution.items():
                percentage = (count / total_steps) * 100 if total_steps > 0 else 0
                print(f"     {agent}: {count}个步骤 ({percentage:.1f}%)")
            
            # 评估工作流程合理性
            # 检查是否有合理的Agent分布
            used_agents = sum(1 for count in agent_distribution.values() if count > 0)
            if used_agents >= 2:  # 至少使用2个不同的Agent
                print(f"   ✅ 工作流程设计合理 (使用了{used_agents}个Agent)")
                successful_workflows += 1
            else:
                print(f"   ⚠️ 工作流程可能过于单一 (只使用了{used_agents}个Agent)")
        
        # 计算成功率
        success_rate = (successful_workflows / len(workflows)) * 100
        print(f"\n📊 工作流程设计成功率: {successful_workflows}/{len(workflows)} ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            print("✅ 工作流程设计功能优秀")
        elif success_rate >= 60:
            print("⚠️ 工作流程设计功能一般")
        else:
            print("❌ 工作流程设计功能需要改进")
        
        print(f"\n🎉 工作流程设计功能测试完成!")
        return success_rate >= 60
        
    except Exception as e:
        print(f"❌ 工作流程设计测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_coordinator_intelligence_summary():
    """测试协调器智能化程度总结"""
    print("\n🧠 写作协调器智能化程度评估...")
    print("=" * 60)
    
    try:
        from host_agent.writing_coordinator import WritingCoordinator
        
        coordinator = WritingCoordinator()
        
        # 评估各项智能化指标
        intelligence_metrics = {
            "请求理解能力": {
                "description": "能否准确理解用户的写作需求",
                "test_cases": [
                    "帮我写一个霸道总裁的小说大纲",
                    "优化这段对话让它更自然",
                    "创建一个古代言情的女主角",
                    "分析这个情节的冲突点"
                ],
                "score": 0
            },
            "任务分类精度": {
                "description": "能否准确分类不同类型的写作任务",
                "categories": ["plot_planning", "character_creation", "content_generation", "content_refinement"],
                "score": 0
            },
            "路由决策智能": {
                "description": "能否根据任务特点选择最合适的Agent",
                "scenarios": ["理想情况", "部分可用", "降级处理", "异常情况"],
                "score": 0
            },
            "上下文感知": {
                "description": "能否维护和利用写作会话的上下文信息",
                "aspects": ["会话管理", "任务关联", "状态跟踪", "历史记录"],
                "score": 0
            },
            "工作流程设计": {
                "description": "能否设计合理的多步骤写作工作流程",
                "complexity": ["简单流程", "复杂流程", "跨Agent协作", "错误处理"],
                "score": 0
            }
        }
        
        print("📊 智能化指标评估:")
        
        # 模拟评估各项指标
        for metric_name, metric_info in intelligence_metrics.items():
            print(f"\n🔍 {metric_name}: {metric_info['description']}")
            
            # 根据不同指标进行模拟评估
            if metric_name == "请求理解能力":
                # 测试请求理解
                correct = 0
                for test_case in metric_info['test_cases']:
                    task_type = coordinator.analyze_writing_request(test_case)
                    if task_type:  # 能够分析出任务类型就算正确
                        correct += 1
                score = (correct / len(metric_info['test_cases'])) * 100
                
            elif metric_name == "任务分类精度":
                # 基于路由规则的完整性评估
                score = (len(coordinator.routing_rules) / 9) * 100  # 9是预期的任务类型数
                
            elif metric_name == "路由决策智能":
                # 基于路由规则的覆盖度评估
                covered_agents = set()
                for agents in coordinator.routing_rules.values():
                    covered_agents.update(agents)
                score = (len(covered_agents) / 3) * 100  # 3是预期的Agent数
                
            elif metric_name == "上下文感知":
                # 基于数据结构的完整性评估
                has_session_mgmt = hasattr(coordinator, 'active_sessions')
                has_task_queue = hasattr(coordinator, 'task_queue')
                has_context_methods = hasattr(coordinator, 'create_writing_session')
                score = sum([has_session_mgmt, has_task_queue, has_context_methods]) / 3 * 100
                
            elif metric_name == "工作流程设计":
                # 基于工作流程方法的存在性评估
                has_workflow_method = hasattr(coordinator, 'writing_workflow')
                has_task_analysis = hasattr(coordinator, 'analyze_writing_request')
                has_agent_selection = hasattr(coordinator, 'select_best_agent')
                score = sum([has_workflow_method, has_task_analysis, has_agent_selection]) / 3 * 100
            
            metric_info['score'] = score
            
            # 显示评估结果
            if score >= 90:
                level = "优秀"
                icon = "🌟"
            elif score >= 75:
                level = "良好"
                icon = "✅"
            elif score >= 60:
                level = "一般"
                icon = "⚠️"
            else:
                level = "需改进"
                icon = "❌"
            
            print(f"   {icon} 评分: {score:.1f}% ({level})")
        
        # 计算总体智能化程度
        total_score = sum(metric['score'] for metric in intelligence_metrics.values())
        average_score = total_score / len(intelligence_metrics)
        
        print(f"\n🎯 总体智能化程度: {average_score:.1f}%")
        
        if average_score >= 85:
            overall_level = "优秀"
            print("🌟 写作协调器具有优秀的智能化水平")
        elif average_score >= 70:
            overall_level = "良好"
            print("✅ 写作协调器具有良好的智能化水平")
        elif average_score >= 55:
            overall_level = "一般"
            print("⚠️ 写作协调器智能化水平一般，有改进空间")
        else:
            overall_level = "需改进"
            print("❌ 写作协调器智能化水平需要显著改进")
        
        # 提供改进建议
        print(f"\n💡 改进建议:")
        for metric_name, metric_info in intelligence_metrics.items():
            if metric_info['score'] < 70:
                print(f"   - 提升{metric_name}: {metric_info['description']}")
        
        print(f"\n🎉 智能化程度评估完成!")
        return average_score >= 60
        
    except Exception as e:
        print(f"❌ 智能化评估失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    async def main():
        print("🚀 开始写作协调器智能路由逻辑测试 (任务9)")
        print("📋 重点测试智能分析、路由选择、协调能力等核心功能")
        print()
        
        # 执行各项智能化测试
        test_results = {}
        
        test_results["智能请求分析"] = await test_intelligent_request_analysis()
        test_results["智能Agent路由"] = await test_intelligent_agent_routing()
        test_results["会话管理"] = await test_session_management()
        test_results["工作流程设计"] = await test_workflow_design()
        test_results["智能化程度评估"] = await test_coordinator_intelligence_summary()
        
        # 显示测试总结
        print("\n" + "="*60)
        print("📊 任务9 - 写作协调器智能路由逻辑测试总结:")
        
        passed_tests = 0
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed_tests += 1
        
        success_rate = (passed_tests / len(test_results)) * 100
        print(f"\n📈 总体通过率: {passed_tests}/{len(test_results)} ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            print("\n🎉 任务9 - 写作协调器智能路由逻辑实现优秀！")
            print("💡 系统具备:")
            print("   - 准确的写作需求理解能力")
            print("   - 智能的Agent选择和路由能力")
            print("   - 完善的会话和工作流程管理")
            print("   - 高度的智能化协调水平")
        elif success_rate >= 60:
            print("\n✅ 任务9 - 写作协调器智能路由逻辑基本达标")
            print("💡 系统基本具备智能协调能力，部分功能需要优化")
        else:
            print("\n⚠️ 任务9 - 写作协调器智能路由逻辑需要改进")
            print("💡 建议重点优化智能分析和路由算法")
    
    asyncio.run(main())