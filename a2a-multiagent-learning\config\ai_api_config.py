#!/usr/bin/env python3
"""
AI API配置管理器
为每个Agent配置专属的AI API密钥和配置
"""

import os
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import random

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
    logger.info("成功加载.env文件")
except ImportError:
    logger.warning("python-dotenv未安装，无法加载.env文件")


class AIProvider(Enum):
    """AI服务提供商枚举"""
    OPENAI_COMPATIBLE = "openai_compatible"
    GOOGLE_AI = "google_ai"
    ANTHROPIC = "anthropic"
    CUSTOM = "custom"


@dataclass
class AIAPIConfig:
    """AI API配置数据类"""
    agent_id: str
    provider: AIProvider
    api_key: str
    base_url: Optional[str] = None
    model: str = "gpt-3.5-turbo"
    max_tokens: int = 2048
    temperature: float = 0.7
    timeout: int = 30
    max_retries: int = 3
    backup_keys: List[str] = None
    
    def __post_init__(self):
        if self.backup_keys is None:
            self.backup_keys = []


class AIAPIManager:
    """AI API配置管理器"""
    
    def __init__(self):
        self.configs: Dict[str, AIAPIConfig] = {}
        self.key_pools: Dict[str, List[str]] = {}
        self._load_configurations()
    
    def _load_configurations(self):
        """加载AI API配置"""
        logger.info("加载AI API配置...")
        
        # 加载密钥池
        self._load_key_pools()
        
        # 为每个Agent配置专属API
        self._configure_agent_apis()
        
        logger.info(f"已配置 {len(self.configs)} 个Agent的AI API")
    
    def _load_key_pools(self):
        """加载密钥池"""
        # 主密钥池 - OpenAI兼容API
        openai_keys = []
        
        # 从环境变量加载主密钥
        main_key = os.getenv('OPENAI_API_KEY')
        if main_key:
            openai_keys.append(main_key)
        
        # 从环境变量加载备用密钥
        for i in range(1, 21):  # 支持最多20个备用密钥
            backup_key = os.getenv(f'OPENAI_API_KEY_{i}')
            if backup_key:
                openai_keys.append(backup_key)
        
        self.key_pools['openai_compatible'] = openai_keys
        
        # Google AI密钥池
        google_keys = []
        main_google_key = os.getenv('GOOGLE_API_KEY')
        if main_google_key:
            google_keys.append(main_google_key)
        
        for i in range(1, 11):  # 支持最多10个Google密钥
            google_key = os.getenv(f'GOOGLE_API_KEY_{i}')
            if google_key:
                google_keys.append(google_key)
        
        self.key_pools['google_ai'] = google_keys
        
        logger.info(f" 密钥池统计:")
        logger.info(f"   OpenAI兼容: {len(self.key_pools['openai_compatible'])}个密钥")
        logger.info(f"   Google AI: {len(self.key_pools['google_ai'])}个密钥")
    
    def _configure_agent_apis(self):
        """为每个Agent配置专属API"""
        # Agent配置映射
        agent_configs = {
            'plot_agent': {
                'provider': AIProvider.OPENAI_COMPATIBLE,
                'model': os.getenv('PLOT_AGENT_MODEL', 'gemini-2.5-pro-preview-06-05'),
                'base_url': os.getenv('PLOT_AGENT_BASE_URL', os.getenv('OPENAI_BASE_URL')),
                'temperature': 0.7,
                'max_tokens': 3000,
                'description': '情节规划专用AI'
            },
            'character_agent': {
                'provider': AIProvider.OPENAI_COMPATIBLE,
                'model': os.getenv('CHARACTER_AGENT_MODEL', 'gemini-2.5-pro-preview-06-05'),
                'base_url': os.getenv('CHARACTER_AGENT_BASE_URL', os.getenv('OPENAI_BASE_URL')),
                'temperature': 0.8,
                'max_tokens': 2500,
                'description': '角色设定专用AI'
            },
            'content_agent': {
                'provider': AIProvider.OPENAI_COMPATIBLE,
                'model': os.getenv('CONTENT_AGENT_MODEL', 'gemini-2.5-pro-preview-06-05'),
                'base_url': os.getenv('CONTENT_AGENT_BASE_URL', os.getenv('OPENAI_BASE_URL')),
                'temperature': 0.9,
                'max_tokens': 4000,
                'description': '内容生成专用AI'
            },
            'host_agent': {
                'provider': AIProvider.OPENAI_COMPATIBLE,
                'model': os.getenv('HOST_AGENT_MODEL', 'gemini-2.5-pro-preview-06-05'),
                'base_url': os.getenv('HOST_AGENT_BASE_URL', os.getenv('OPENAI_BASE_URL')),
                'temperature': 0.7,
                'max_tokens': 2048,
                'description': '写作协调专用AI'
            }
        }
        
        # 为每个Agent分配专属密钥
        available_keys = self.key_pools['openai_compatible'].copy()
        
        for agent_id, config in agent_configs.items():
            if not available_keys:
                # 如果密钥不够，重新使用密钥池
                available_keys = self.key_pools['openai_compatible'].copy()
                logger.warning(f" 密钥不足，为 {agent_id} 重用密钥")
            
            if available_keys:
                # 为每个Agent分配专属密钥
                primary_key = available_keys.pop(0)
                backup_keys = available_keys[:2] if len(available_keys) >= 2 else available_keys.copy()
                
                self.configs[agent_id] = AIAPIConfig(
                    agent_id=agent_id,
                    provider=config['provider'],
                    api_key=primary_key,
                    base_url=config['base_url'],
                    model=config['model'],
                    temperature=config['temperature'],
                    max_tokens=config['max_tokens'],
                    backup_keys=backup_keys
                )
                
                logger.info(f" {agent_id}: {config['description']}")
                logger.info(f"   模型: {config['model']}")
                logger.info(f"   主密钥: {primary_key[:20]}...")
                logger.info(f"   备用密钥: {len(backup_keys)}个")
            else:
                logger.error(f"无法为 {agent_id} 分配API密钥")
    
    def get_config(self, agent_id: str) -> Optional[AIAPIConfig]:
        """获取指定Agent的AI API配置"""
        return self.configs.get(agent_id)
    
    def get_all_configs(self) -> Dict[str, AIAPIConfig]:
        """获取所有Agent的AI API配置"""
        return self.configs.copy()
    
    def rotate_key(self, agent_id: str) -> bool:
        """轮换指定Agent的API密钥"""
        config = self.configs.get(agent_id)
        if not config or not config.backup_keys:
            return False
        
        try:
            # 将当前密钥移到备用列表末尾
            old_key = config.api_key
            config.backup_keys.append(old_key)
            
            # 使用第一个备用密钥作为新的主密钥
            config.api_key = config.backup_keys.pop(0)
            
            logger.info(f"{agent_id} 密钥轮换成功")
            logger.info(f"   新密钥: {config.api_key[:20]}...")
            
            return True
            
        except Exception as e:
            logger.error(f"{agent_id} 密钥轮换失败: {e}")
            return False
    
    def get_random_key(self, provider: str = 'openai_compatible') -> Optional[str]:
        """获取随机密钥（用于负载均衡）"""
        keys = self.key_pools.get(provider, [])
        if keys:
            return random.choice(keys)
        return None
    
    def validate_config(self, agent_id: str) -> bool:
        """验证Agent配置的有效性"""
        config = self.configs.get(agent_id)
        if not config:
            return False
        
        # 基本配置检查
        if not config.api_key or not config.model:
            return False
        
        # 如果是OpenAI兼容API，检查base_url
        if config.provider == AIProvider.OPENAI_COMPATIBLE and not config.base_url:
            logger.warning(f" {agent_id} 缺少base_url配置")
        
        return True
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """获取API使用统计"""
        stats = {
            'total_agents': len(self.configs),
            'total_keys': sum(len(keys) for keys in self.key_pools.values()),
            'agents_per_provider': {},
            'key_distribution': {}
        }
        
        # 统计每个提供商的Agent数量
        for config in self.configs.values():
            provider = config.provider.value
            stats['agents_per_provider'][provider] = stats['agents_per_provider'].get(provider, 0) + 1
        
        # 统计密钥分布
        for provider, keys in self.key_pools.items():
            stats['key_distribution'][provider] = len(keys)
        
        return stats
    
    def reload_configurations(self):
        """重新加载配置"""
        logger.info("重新加载AI API配置...")
        self.configs.clear()
        self.key_pools.clear()
        self._load_configurations()
    
    def add_backup_key(self, agent_id: str, backup_key: str) -> bool:
        """为指定Agent添加备用密钥"""
        config = self.configs.get(agent_id)
        if not config:
            return False
        
        if backup_key not in config.backup_keys:
            config.backup_keys.append(backup_key)
            logger.info(f"为 {agent_id} 添加备用密钥")
            return True
        
        return False
    
    def remove_backup_key(self, agent_id: str, backup_key: str) -> bool:
        """移除指定Agent的备用密钥"""
        config = self.configs.get(agent_id)
        if not config:
            return False
        
        if backup_key in config.backup_keys:
            config.backup_keys.remove(backup_key)
            logger.info(f"从 {agent_id} 移除备用密钥")
            return True
        
        return False


# 全局API管理器实例
_api_manager: Optional[AIAPIManager] = None


def get_api_manager() -> AIAPIManager:
    """获取全局API管理器实例"""
    global _api_manager
    
    if _api_manager is None:
        _api_manager = AIAPIManager()
    
    return _api_manager


def get_agent_config(agent_id: str) -> Optional[AIAPIConfig]:
    """获取指定Agent的AI API配置"""
    manager = get_api_manager()
    return manager.get_config(agent_id)


def reload_api_configs():
    """重新加载API配置"""
    global _api_manager
    if _api_manager:
        _api_manager.reload_configurations()