#!/usr/bin/env python3
"""
启动写作协调器服务的便捷脚本
"""

import asyncio
import sys
import signal
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class WritingCoordinatorService:
    """写作协调器服务"""
    
    def __init__(self):
        self.coordinator = None
        self.running = False
        self.session_counter = 0
    
    async def start(self):
        """启动写作协调器服务"""
        print("📝 启动写作协调器服务...")
        print("=" * 50)
        
        try:
            from host_agent.writing_coordinator import get_writing_coordinator
            
            # 获取写作协调器
            self.coordinator = await get_writing_coordinator()
            self.running = True
            
            print("✅ 写作协调器服务启动成功")
            
            # 显示初始状态
            await self.show_status()
            
            # 启动交互式服务
            await self.interactive_service()
            
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            import traceback
            traceback.print_exc()
    
    async def show_status(self):
        """显示服务状态"""
        if not self.coordinator:
            return
        
        print("\n📊 写作协调器状态:")
        
        # 显示Agent连接状态
        workload = await self.coordinator.get_agent_workload()
        connected_count = sum(1 for load in workload.values() if load['status'] == 'connected')
        
        print(f"🤖 Agent连接状态 ({connected_count}/{len(workload)} 已连接):")
        for agent_id, load in workload.items():
            status_icon = "✅" if load['status'] == 'connected' else "❌"
            print(f"   {status_icon} {agent_id}: {load['status']} ({load['skills_count']}个技能)")
        
        # 显示活动会话
        active_sessions = len(self.coordinator.active_sessions)
        print(f"📝 活动会话: {active_sessions}个")
        
        # 显示任务队列
        pending_tasks = len([t for t in self.coordinator.task_queue if t.status == 'pending'])
        print(f"📋 待处理任务: {pending_tasks}个")
    
    async def interactive_service(self):
        """交互式服务"""
        print("\n🎯 写作协调器交互式服务")
        print("💡 输入写作请求，协调器将自动选择合适的Agent处理")
        print("💡 输入 'help' 查看帮助，'status' 查看状态，'quit' 退出")
        print("-" * 50)
        
        while self.running:
            try:
                # 获取用户输入
                user_input = input("\n📝 请输入写作请求: ").strip()
                
                if not user_input:
                    continue
                
                # 处理特殊命令
                if user_input.lower() == 'quit':
                    break
                elif user_input.lower() == 'help':
                    await self.show_help()
                    continue
                elif user_input.lower() == 'status':
                    await self.show_status()
                    continue
                elif user_input.lower().startswith('session'):
                    await self.handle_session_command(user_input)
                    continue
                
                # 处理写作请求
                await self.handle_writing_request(user_input)
                
            except KeyboardInterrupt:
                print("\n👋 用户中断，正在退出...")
                break
            except EOFError:
                print("\n👋 输入结束，正在退出...")
                break
            except Exception as e:
                print(f"❌ 处理请求时出错: {e}")
    
    async def show_help(self):
        """显示帮助信息"""
        print("\n📖 写作协调器帮助:")
        print("🎯 写作请求示例:")
        print("   - 帮我设计一个现代都市爱情故事的大纲")
        print("   - 为我的小说创建一个霸道总裁男主角")
        print("   - 写一个咖啡厅初次相遇的浪漫场景")
        print("   - 优化这段对话，使其更自然")
        print()
        print("🔧 特殊命令:")
        print("   - help: 显示此帮助信息")
        print("   - status: 显示服务状态")
        print("   - session list: 列出所有会话")
        print("   - session <id>: 查看指定会话状态")
        print("   - quit: 退出服务")
    
    async def handle_session_command(self, command: str):
        """处理会话相关命令"""
        parts = command.split()
        if len(parts) < 2:
            print("❌ 会话命令格式错误，使用 'session list' 或 'session <id>'")
            return
        
        if parts[1] == 'list':
            print(f"\n📝 活动会话列表 ({len(self.coordinator.active_sessions)}个):")
            for session_id, session in self.coordinator.active_sessions.items():
                print(f"   - {session_id}: {session.project_name} ({session.genre})")
                print(f"     任务: {len(session.tasks)}个, 状态: {session.status}")
        else:
            session_id = parts[1]
            status = await self.coordinator.get_session_status(session_id)
            if status:
                print(f"\n📊 会话状态: {session_id}")
                print(f"   项目: {status.get('project_name', 'Unknown')}")
                print(f"   类型: {status.get('genre', 'Unknown')}")
                print(f"   总任务: {status['total_tasks']}")
                print(f"   已完成: {status['completed_tasks']}")
                print(f"   失败: {status['failed_tasks']}")
                print(f"   待处理: {status['pending_tasks']}")
            else:
                print(f"❌ 会话 {session_id} 不存在")
    
    async def handle_writing_request(self, request: str):
        """处理写作请求"""
        print(f"\n🎯 处理写作请求: {request}")
        
        # 生成会话ID
        self.session_counter += 1
        session_id = f"session_{self.session_counter}"
        
        try:
            # 分析请求类型
            task_type = self.coordinator.analyze_writing_request(request)
            print(f"📋 任务类型: {task_type.value}")
            
            # 检查可用Agent
            workload = await self.coordinator.get_agent_workload()
            available_agents = [agent_id for agent_id, load in workload.items() if load['status'] == 'connected']
            
            if not available_agents:
                print("❌ 没有可用的Agent，请先启动Agent服务器")
                return
            
            # 选择Agent
            selected_agent = self.coordinator.select_best_agent(task_type, available_agents)
            print(f"🤖 选择Agent: {selected_agent}")
            
            # 执行协调
            print("⏳ 正在处理请求...")
            start_time = datetime.now()
            
            result = await self.coordinator.coordinate_writing(
                session_id=session_id,
                request=request,
                context={"timestamp": start_time.isoformat()}
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # 显示结果
            if result.get("success", False):
                print(f"✅ 请求处理成功 (耗时: {duration:.2f}秒)")
                print(f"📋 任务ID: {result['task_id']}")
                print(f"🤖 执行Agent: {result['assigned_agent']}")
                
                # 显示结果预览
                result_data = result.get('result', {})
                if isinstance(result_data, dict):
                    result_preview = str(result_data)[:200]
                else:
                    result_preview = str(result_data)[:200]
                
                print(f"📄 结果预览: {result_preview}...")
                
                # 询问是否查看完整结果
                try:
                    show_full = input("🔍 是否查看完整结果? (y/N): ").strip().lower()
                    if show_full in ['y', 'yes']:
                        print(f"\n📄 完整结果:")
                        print(result_data)
                except:
                    pass
                
            else:
                print(f"❌ 请求处理失败: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ 处理请求时出错: {e}")
    
    async def stop(self):
        """停止写作协调器服务"""
        print("\n🔄 停止写作协调器服务...")
        
        self.running = False
        
        if self.coordinator:
            from host_agent.writing_coordinator import shutdown_writing_coordinator
            await shutdown_writing_coordinator()
        
        print("✅ 写作协调器服务已停止")

# 全局服务实例
service = WritingCoordinatorService()

def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n📡 收到信号 {signum}，准备停止...")
    asyncio.create_task(service.stop())

async def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        await service.start()
    except KeyboardInterrupt:
        print("\n👋 用户中断，正在停止...")
    except Exception as e:
        print(f"❌ 运行异常: {e}")
    finally:
        await service.stop()

if __name__ == "__main__":
    print("📝 写作协调器服务")
    print("🤖 智能协调多个Agent完成写作任务")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)