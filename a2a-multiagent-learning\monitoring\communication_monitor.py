#!/usr/bin/env python3
"""
A2A协议通信监控系统
追踪Agent间的通信、性能和状态变化
"""

import asyncio
import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from pathlib import Path
import threading
from collections import defaultdict, deque

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class CommunicationEvent:
    """通信事件数据结构"""
    timestamp: datetime
    event_type: str  # request, response, error, status_change
    source_agent: str
    target_agent: Optional[str]
    task_id: str
    context_id: str
    request_data: Optional[Dict[str, Any]]
    response_data: Optional[Dict[str, Any]]
    duration_ms: Optional[float]
    status: str
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class AgentMetrics:
    """Agent性能指标"""
    agent_name: str
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    avg_response_time: float = 0.0
    min_response_time: float = float('inf')
    max_response_time: float = 0.0
    last_activity: Optional[datetime] = None
    status: str = "unknown"  # online, offline, error
    error_count: int = 0
    uptime_percentage: float = 100.0

@dataclass
class TaskMetrics:
    """任务性能指标"""
    task_id: str
    context_id: str
    task_type: str
    start_time: datetime
    end_time: Optional[datetime] = None
    duration_ms: Optional[float] = None
    status: str = "pending"  # pending, working, completed, failed, cancelled
    involved_agents: List[str] = None
    steps_completed: int = 0
    total_steps: int = 0
    quality_score: Optional[float] = None
    user_feedback: Optional[str] = None

    def __post_init__(self):
        if self.involved_agents is None:
            self.involved_agents = []

class CommunicationMonitor:
    """A2A协议通信监控器"""
    
    def __init__(self, max_events: int = 10000, metrics_window_hours: int = 24):
        self.max_events = max_events
        self.metrics_window = timedelta(hours=metrics_window_hours)
        
        # 事件存储
        self.events: deque = deque(maxlen=max_events)
        self.events_lock = threading.Lock()
        
        # 指标存储
        self.agent_metrics: Dict[str, AgentMetrics] = {}
        self.task_metrics: Dict[str, TaskMetrics] = {}
        self.metrics_lock = threading.Lock()
        
        # 监听器
        self.event_listeners: List[Callable] = []
        
        # 监控状态
        self.monitoring_active = False
        self.monitor_thread: Optional[threading.Thread] = None
        
        # 统计缓存
        self._stats_cache: Dict[str, Any] = {}
        self._cache_timestamp = datetime.now()
        self._cache_ttl = timedelta(seconds=30)
    
    def start_monitoring(self):
        """启动监控"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("📊 A2A通信监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("📊 A2A通信监控已停止")
    
    def _monitoring_loop(self):
        """监控主循环"""
        while self.monitoring_active:
            try:
                # 清理过期事件
                self._cleanup_old_events()
                
                # 更新Agent状态
                self._update_agent_status()
                
                # 计算性能指标
                self._calculate_metrics()
                
                # 检测异常
                self._detect_anomalies()
                
                time.sleep(10)  # 每10秒执行一次
                
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                time.sleep(5)
    
    def log_request(self, source_agent: str, target_agent: str, task_id: str, 
                   context_id: str, request_data: Dict[str, Any], 
                   metadata: Optional[Dict[str, Any]] = None):
        """记录请求事件"""
        event = CommunicationEvent(
            timestamp=datetime.now(),
            event_type="request",
            source_agent=source_agent,
            target_agent=target_agent,
            task_id=task_id,
            context_id=context_id,
            request_data=request_data,
            response_data=None,
            duration_ms=None,
            status="sent",
            metadata=metadata
        )
        
        self._add_event(event)
        self._update_agent_metrics(source_agent, "request_sent")
        
        logger.info(f"📤 请求: {source_agent} -> {target_agent} (任务: {task_id})")
    
    def log_response(self, source_agent: str, target_agent: str, task_id: str,
                    context_id: str, response_data: Dict[str, Any], 
                    duration_ms: float, success: bool = True,
                    metadata: Optional[Dict[str, Any]] = None):
        """记录响应事件"""
        event = CommunicationEvent(
            timestamp=datetime.now(),
            event_type="response",
            source_agent=target_agent,
            target_agent=source_agent,
            task_id=task_id,
            context_id=context_id,
            request_data=None,
            response_data=response_data,
            duration_ms=duration_ms,
            status="success" if success else "error",
            metadata=metadata
        )
        
        self._add_event(event)
        self._update_agent_metrics(target_agent, "response_sent", duration_ms, success)
        
        status_icon = "✅" if success else "❌"
        logger.info(f"📥 响应: {target_agent} -> {source_agent} {status_icon} ({duration_ms:.1f}ms)")
    
    def log_error(self, source_agent: str, target_agent: Optional[str], 
                 task_id: str, context_id: str, error_message: str,
                 metadata: Optional[Dict[str, Any]] = None):
        """记录错误事件"""
        event = CommunicationEvent(
            timestamp=datetime.now(),
            event_type="error",
            source_agent=source_agent,
            target_agent=target_agent,
            task_id=task_id,
            context_id=context_id,
            request_data=None,
            response_data=None,
            duration_ms=None,
            status="error",
            error_message=error_message,
            metadata=metadata
        )
        
        self._add_event(event)
        self._update_agent_metrics(source_agent, "error")
        
        logger.error(f"💥 错误: {source_agent} -> {target_agent or 'N/A'} - {error_message}")
    
    def log_task_start(self, task_id: str, context_id: str, task_type: str,
                      involved_agents: List[str], total_steps: int = 1,
                      metadata: Optional[Dict[str, Any]] = None):
        """记录任务开始"""
        task_metrics = TaskMetrics(
            task_id=task_id,
            context_id=context_id,
            task_type=task_type,
            start_time=datetime.now(),
            status="working",
            involved_agents=involved_agents,
            total_steps=total_steps
        )
        
        with self.metrics_lock:
            self.task_metrics[task_id] = task_metrics
        
        event = CommunicationEvent(
            timestamp=datetime.now(),
            event_type="status_change",
            source_agent="system",
            target_agent=None,
            task_id=task_id,
            context_id=context_id,
            request_data=None,
            response_data={"status": "started", "task_type": task_type},
            duration_ms=None,
            status="started",
            metadata=metadata
        )
        
        self._add_event(event)
        logger.info(f"🚀 任务开始: {task_id} ({task_type}) - 涉及Agent: {', '.join(involved_agents)}")
    
    def log_task_progress(self, task_id: str, steps_completed: int, 
                         status: str = "working", quality_score: Optional[float] = None,
                         metadata: Optional[Dict[str, Any]] = None):
        """记录任务进度"""
        with self.metrics_lock:
            if task_id in self.task_metrics:
                task = self.task_metrics[task_id]
                task.steps_completed = steps_completed
                task.status = status
                if quality_score is not None:
                    task.quality_score = quality_score
        
        event = CommunicationEvent(
            timestamp=datetime.now(),
            event_type="status_change",
            source_agent="system",
            target_agent=None,
            task_id=task_id,
            context_id=self.task_metrics.get(task_id, TaskMetrics("", "", "", datetime.now())).context_id,
            request_data=None,
            response_data={
                "status": status,
                "progress": f"{steps_completed}/{self.task_metrics.get(task_id, TaskMetrics('', '', '', datetime.now())).total_steps}",
                "quality_score": quality_score
            },
            duration_ms=None,
            status=status,
            metadata=metadata
        )
        
        self._add_event(event)
        logger.info(f"📈 任务进度: {task_id} - {steps_completed}步完成 (状态: {status})")
    
    def log_task_complete(self, task_id: str, success: bool = True, 
                         quality_score: Optional[float] = None,
                         user_feedback: Optional[str] = None,
                         metadata: Optional[Dict[str, Any]] = None):
        """记录任务完成"""
        end_time = datetime.now()
        
        with self.metrics_lock:
            if task_id in self.task_metrics:
                task = self.task_metrics[task_id]
                task.end_time = end_time
                task.duration_ms = (end_time - task.start_time).total_seconds() * 1000
                task.status = "completed" if success else "failed"
                if quality_score is not None:
                    task.quality_score = quality_score
                if user_feedback is not None:
                    task.user_feedback = user_feedback
        
        event = CommunicationEvent(
            timestamp=end_time,
            event_type="status_change",
            source_agent="system",
            target_agent=None,
            task_id=task_id,
            context_id=self.task_metrics.get(task_id, TaskMetrics("", "", "", datetime.now())).context_id,
            request_data=None,
            response_data={
                "status": "completed" if success else "failed",
                "quality_score": quality_score,
                "user_feedback": user_feedback
            },
            duration_ms=self.task_metrics.get(task_id, TaskMetrics("", "", "", datetime.now())).duration_ms,
            status="completed" if success else "failed",
            metadata=metadata
        )
        
        self._add_event(event)
        
        status_icon = "🎉" if success else "💥"
        duration = self.task_metrics.get(task_id, TaskMetrics("", "", "", datetime.now())).duration_ms or 0
        logger.info(f"{status_icon} 任务完成: {task_id} - {'成功' if success else '失败'} ({duration:.1f}ms)")
    
    def _add_event(self, event: CommunicationEvent):
        """添加事件到存储"""
        with self.events_lock:
            self.events.append(event)
        
        # 通知监听器
        for listener in self.event_listeners:
            try:
                listener(event)
            except Exception as e:
                logger.error(f"事件监听器异常: {e}")
    
    def _update_agent_metrics(self, agent_name: str, event_type: str, 
                            duration_ms: Optional[float] = None, success: bool = True):
        """更新Agent指标"""
        with self.metrics_lock:
            if agent_name not in self.agent_metrics:
                self.agent_metrics[agent_name] = AgentMetrics(agent_name=agent_name)
            
            metrics = self.agent_metrics[agent_name]
            metrics.last_activity = datetime.now()
            
            if event_type == "request_sent":
                metrics.total_requests += 1
            elif event_type == "response_sent":
                if success:
                    metrics.successful_requests += 1
                else:
                    metrics.failed_requests += 1
                
                if duration_ms is not None:
                    # 更新响应时间统计
                    if metrics.avg_response_time == 0:
                        metrics.avg_response_time = duration_ms
                    else:
                        # 简单移动平均
                        metrics.avg_response_time = (metrics.avg_response_time * 0.9 + duration_ms * 0.1)
                    
                    metrics.min_response_time = min(metrics.min_response_time, duration_ms)
                    metrics.max_response_time = max(metrics.max_response_time, duration_ms)
            elif event_type == "error":
                metrics.error_count += 1
                metrics.failed_requests += 1
    
    def _cleanup_old_events(self):
        """清理过期事件"""
        cutoff_time = datetime.now() - self.metrics_window
        
        with self.events_lock:
            # 移除过期事件
            while self.events and self.events[0].timestamp < cutoff_time:
                self.events.popleft()
    
    def _update_agent_status(self):
        """更新Agent状态"""
        current_time = datetime.now()
        
        with self.metrics_lock:
            for agent_name, metrics in self.agent_metrics.items():
                if metrics.last_activity:
                    time_since_activity = current_time - metrics.last_activity
                    
                    if time_since_activity < timedelta(minutes=5):
                        metrics.status = "online"
                    elif time_since_activity < timedelta(minutes=15):
                        metrics.status = "idle"
                    else:
                        metrics.status = "offline"
                else:
                    metrics.status = "unknown"
    
    def _calculate_metrics(self):
        """计算性能指标"""
        # 清除缓存
        if datetime.now() - self._cache_timestamp > self._cache_ttl:
            self._stats_cache.clear()
            self._cache_timestamp = datetime.now()
    
    def _detect_anomalies(self):
        """检测异常情况"""
        with self.metrics_lock:
            for agent_name, metrics in self.agent_metrics.items():
                # 检测高错误率
                if metrics.total_requests > 10:
                    error_rate = metrics.failed_requests / metrics.total_requests
                    if error_rate > 0.2:  # 错误率超过20%
                        logger.warning(f"⚠️ Agent {agent_name} 错误率过高: {error_rate:.1%}")
                
                # 检测响应时间异常
                if metrics.avg_response_time > 10000:  # 超过10秒
                    logger.warning(f"⚠️ Agent {agent_name} 响应时间过长: {metrics.avg_response_time:.1f}ms")
                
                # 检测离线状态
                if metrics.status == "offline" and metrics.total_requests > 0:
                    logger.warning(f"⚠️ Agent {agent_name} 已离线")
    
    def add_event_listener(self, listener: Callable[[CommunicationEvent], None]):
        """添加事件监听器"""
        self.event_listeners.append(listener)
    
    def remove_event_listener(self, listener: Callable[[CommunicationEvent], None]):
        """移除事件监听器"""
        if listener in self.event_listeners:
            self.event_listeners.remove(listener)
    
    def get_agent_metrics(self, agent_name: Optional[str] = None) -> Dict[str, AgentMetrics]:
        """获取Agent指标"""
        with self.metrics_lock:
            if agent_name:
                return {agent_name: self.agent_metrics.get(agent_name, AgentMetrics(agent_name))}
            return self.agent_metrics.copy()
    
    def get_task_metrics(self, task_id: Optional[str] = None) -> Dict[str, TaskMetrics]:
        """获取任务指标"""
        with self.metrics_lock:
            if task_id:
                return {task_id: self.task_metrics.get(task_id, TaskMetrics("", "", "", datetime.now()))}
            return self.task_metrics.copy()
    
    def get_recent_events(self, limit: int = 100, event_type: Optional[str] = None) -> List[CommunicationEvent]:
        """获取最近的事件"""
        with self.events_lock:
            events = list(self.events)
        
        if event_type:
            events = [e for e in events if e.event_type == event_type]
        
        return sorted(events, key=lambda x: x.timestamp, reverse=True)[:limit]
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        cache_key = "system_stats"
        if cache_key in self._stats_cache:
            return self._stats_cache[cache_key]
        
        with self.metrics_lock:
            total_agents = len(self.agent_metrics)
            online_agents = sum(1 for m in self.agent_metrics.values() if m.status == "online")
            total_tasks = len(self.task_metrics)
            completed_tasks = sum(1 for t in self.task_metrics.values() if t.status == "completed")
            failed_tasks = sum(1 for t in self.task_metrics.values() if t.status == "failed")
            
            total_requests = sum(m.total_requests for m in self.agent_metrics.values())
            total_errors = sum(m.error_count for m in self.agent_metrics.values())
            
            avg_response_time = 0
            if self.agent_metrics:
                response_times = [m.avg_response_time for m in self.agent_metrics.values() if m.avg_response_time > 0]
                if response_times:
                    avg_response_time = sum(response_times) / len(response_times)
        
        with self.events_lock:
            total_events = len(self.events)
        
        stats = {
            "timestamp": datetime.now().isoformat(),
            "agents": {
                "total": total_agents,
                "online": online_agents,
                "offline": total_agents - online_agents
            },
            "tasks": {
                "total": total_tasks,
                "completed": completed_tasks,
                "failed": failed_tasks,
                "success_rate": (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
            },
            "communication": {
                "total_requests": total_requests,
                "total_errors": total_errors,
                "error_rate": (total_errors / total_requests * 100) if total_requests > 0 else 0,
                "avg_response_time": avg_response_time
            },
            "events": {
                "total": total_events,
                "window_hours": self.metrics_window.total_seconds() / 3600
            }
        }
        
        self._stats_cache[cache_key] = stats
        return stats
    
    def export_metrics(self, filepath: str):
        """导出指标到文件"""
        try:
            data = {
                "timestamp": datetime.now().isoformat(),
                "system_stats": self.get_system_stats(),
                "agent_metrics": {name: asdict(metrics) for name, metrics in self.get_agent_metrics().items()},
                "task_metrics": {task_id: asdict(metrics) for task_id, metrics in self.get_task_metrics().items()},
                "recent_events": [asdict(event) for event in self.get_recent_events(1000)]
            }
            
            # 处理datetime序列化
            def datetime_handler(obj):
                if isinstance(obj, datetime):
                    return obj.isoformat()
                raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False, default=datetime_handler)
            
            logger.info(f"📊 指标已导出到: {filepath}")
            
        except Exception as e:
            logger.error(f"导出指标失败: {e}")

# 全局监控实例
_global_monitor: Optional[CommunicationMonitor] = None

def get_communication_monitor() -> CommunicationMonitor:
    """获取全局通信监控实例"""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = CommunicationMonitor()
        _global_monitor.start_monitoring()
    return _global_monitor

def shutdown_communication_monitor():
    """关闭全局通信监控"""
    global _global_monitor
    if _global_monitor:
        _global_monitor.stop_monitoring()
        _global_monitor = None