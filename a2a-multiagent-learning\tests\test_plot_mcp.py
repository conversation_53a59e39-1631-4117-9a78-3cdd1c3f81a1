#!/usr/bin/env python3
"""
Plot Agent MCP工具测试脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入MCP模块
import plot_agent.plot_mcp as plot_mcp

def test_generate_story_outline():
    """测试故事大纲生成"""
    print("🎯 测试故事大纲生成...")
    
    # 直接调用函数实现
    result = plot_mcp.generate_story_outline.__wrapped__(
        genre="现代都市",
        theme="爱情",
        target_length="中篇",
        structure_type="三幕式",
        additional_requirements="包含职场元素"
    )
    
    print("✅ 故事大纲生成结果:")
    print(result[:500] + "..." if len(result) > 500 else result)
    print()

def test_create_chapter_structure():
    """测试章节结构创建"""
    print("📖 测试章节结构创建...")
    
    story_outline = "一个关于现代都市爱情的故事，主角是职场新人，遇到了霸道总裁..."
    
    result = plot_mcp.create_chapter_structure.__wrapped__(
        story_outline=story_outline,
        total_chapters=15,
        chapter_length="4000字",
        pacing_style="稳步推进"
    )
    
    print("✅ 章节结构创建结果:")
    print(result[:800] + "..." if len(result) > 800 else result)
    print()

def test_analyze_plot_conflicts():
    """测试情节冲突分析"""
    print("⚔️ 测试情节冲突分析...")
    
    story_context = """
    故事背景：现代都市，主角李小雅是一名刚毕业的设计师，进入了一家知名广告公司。
    她遇到了公司的CEO陈浩然，两人因为工作产生了矛盾，但逐渐产生了感情。
    然而，陈浩然有一个未婚妻，是商业联姻的对象。
    """
    
    result = plot_mcp.analyze_plot_conflicts.__wrapped__(
        story_context=story_context,
        conflict_types=["人与人", "人与自我", "人与社会"]
    )
    
    print("✅ 情节冲突分析结果:")
    print(result[:600] + "..." if len(result) > 600 else result)
    print()

def test_suggest_plot_twists():
    """测试情节转折建议"""
    print("🌪️ 测试情节转折建议...")
    
    current_plot = """
    李小雅和陈浩然的关系逐渐升温，但陈浩然的未婚妻突然出现，
    要求加快婚礼进程。李小雅感到痛苦，考虑离开公司。
    """
    
    result = plot_mcp.suggest_plot_twists.__wrapped__(
        current_plot=current_plot,
        twist_type="反转与惊喜",
        target_chapter=12
    )
    
    print("✅ 情节转折建议结果:")
    print(result[:600] + "..." if len(result) > 600 else result)
    print()

def test_get_plot_templates():
    """测试获取模板列表"""
    print("📋 测试获取模板列表...")
    
    result = plot_mcp.get_plot_templates.__wrapped__()
    
    print("✅ 可用模板列表:")
    print(result[:400] + "..." if len(result) > 400 else result)
    print()

def main():
    """运行所有测试"""
    print("🧪 开始测试Plot Agent MCP工具...")
    print("=" * 50)
    
    try:
        test_generate_story_outline()
        test_create_chapter_structure()
        test_analyze_plot_conflicts()
        test_suggest_plot_twists()
        test_get_plot_templates()
        
        print("🎉 所有测试完成！Plot Agent MCP工具运行正常。")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()