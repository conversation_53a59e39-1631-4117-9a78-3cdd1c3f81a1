#!/usr/bin/env python3
"""
A2A通信监控面板
实时显示Agent状态、任务进度和系统性能
"""

import asyncio
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import threading
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from monitoring.communication_monitor import get_communication_monitor, CommunicationEvent

class MonitoringDashboard:
    """监控面板"""
    
    def __init__(self, refresh_interval: int = 5):
        self.monitor = get_communication_monitor()
        self.refresh_interval = refresh_interval
        self.running = False
        self.dashboard_thread: Optional[threading.Thread] = None
        
        # 添加事件监听器
        self.monitor.add_event_listener(self._on_event)
        
        # 显示缓存
        self._last_display_time = datetime.now()
        self._display_buffer: List[str] = []
    
    def start(self):
        """启动监控面板"""
        if self.running:
            return
        
        self.running = True
        self.dashboard_thread = threading.Thread(target=self._dashboard_loop, daemon=True)
        self.dashboard_thread.start()
        print("📊 监控面板已启动")
    
    def stop(self):
        """停止监控面板"""
        self.running = False
        if self.dashboard_thread:
            self.dashboard_thread.join(timeout=2)
        print("📊 监控面板已停止")
    
    def _dashboard_loop(self):
        """面板主循环"""
        while self.running:
            try:
                self._update_display()
                time.sleep(self.refresh_interval)
            except Exception as e:
                print(f"面板更新异常: {e}")
                time.sleep(1)
    
    def _on_event(self, event: CommunicationEvent):
        """事件监听器"""
        # 实时显示重要事件
        if event.event_type in ["error", "status_change"]:
            timestamp = event.timestamp.strftime("%H:%M:%S")
            if event.event_type == "error":
                print(f"[{timestamp}] 💥 错误: {event.source_agent} - {event.error_message}")
            elif event.event_type == "status_change" and event.status in ["started", "completed", "failed"]:
                status_icon = {"started": "🚀", "completed": "🎉", "failed": "💥"}.get(event.status, "📋")
                print(f"[{timestamp}] {status_icon} 任务{event.status}: {event.task_id}")
    
    def _update_display(self):
        """更新显示内容"""
        try:
            # 清屏（在支持的终端中）
            print("\033[2J\033[H", end="")
            
            # 显示标题
            print("=" * 80)
            print("🔍 A2A多Agent写作系统监控面板")
            print(f"📅 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("=" * 80)
            
            # 显示系统统计
            self._display_system_stats()
            
            # 显示Agent状态
            self._display_agent_status()
            
            # 显示任务状态
            self._display_task_status()
            
            # 显示最近事件
            self._display_recent_events()
            
            print("=" * 80)
            print("💡 按 Ctrl+C 退出监控")
            
        except Exception as e:
            print(f"显示更新失败: {e}")
    
    def _display_system_stats(self):
        """显示系统统计"""
        stats = self.monitor.get_system_stats()
        
        print("\n📊 系统概览")
        print("-" * 40)
        
        # Agent状态
        agents = stats["agents"]
        print(f"🤖 Agent状态: {agents['online']}/{agents['total']} 在线")
        
        # 任务状态
        tasks = stats["tasks"]
        success_rate = tasks["success_rate"]
        print(f"📋 任务状态: {tasks['completed']} 完成, {tasks['failed']} 失败 (成功率: {success_rate:.1f}%)")
        
        # 通信状态
        comm = stats["communication"]
        error_rate = comm["error_rate"]
        avg_time = comm["avg_response_time"]
        print(f"📡 通信状态: {comm['total_requests']} 请求, {comm['total_errors']} 错误 (错误率: {error_rate:.1f}%)")
        print(f"⏱️ 平均响应时间: {avg_time:.1f}ms")
    
    def _display_agent_status(self):
        """显示Agent状态"""
        agent_metrics = self.monitor.get_agent_metrics()
        
        print("\n🤖 Agent状态详情")
        print("-" * 60)
        print(f"{'Agent名称':<20} {'状态':<8} {'请求数':<8} {'成功率':<8} {'响应时间':<10}")
        print("-" * 60)
        
        for name, metrics in agent_metrics.items():
            status_icon = {
                "online": "🟢",
                "idle": "🟡", 
                "offline": "🔴",
                "unknown": "⚪"
            }.get(metrics.status, "⚪")
            
            success_rate = 0
            if metrics.total_requests > 0:
                success_rate = (metrics.successful_requests / metrics.total_requests) * 100
            
            response_time = f"{metrics.avg_response_time:.1f}ms" if metrics.avg_response_time > 0 else "N/A"
            
            print(f"{name:<20} {status_icon}{metrics.status:<7} {metrics.total_requests:<8} {success_rate:<7.1f}% {response_time:<10}")
    
    def _display_task_status(self):
        """显示任务状态"""
        task_metrics = self.monitor.get_task_metrics()
        
        # 只显示最近的任务
        recent_tasks = sorted(
            task_metrics.values(),
            key=lambda x: x.start_time,
            reverse=True
        )[:10]
        
        if not recent_tasks:
            return
        
        print("\n📋 最近任务状态")
        print("-" * 80)
        print(f"{'任务ID':<15} {'类型':<15} {'状态':<10} {'进度':<8} {'耗时':<10} {'质量':<6}")
        print("-" * 80)
        
        for task in recent_tasks:
            status_icon = {
                "working": "🔄",
                "completed": "✅",
                "failed": "❌",
                "pending": "⏳",
                "cancelled": "🚫"
            }.get(task.status, "❓")
            
            progress = f"{task.steps_completed}/{task.total_steps}"
            
            duration = "N/A"
            if task.duration_ms:
                if task.duration_ms < 1000:
                    duration = f"{task.duration_ms:.0f}ms"
                else:
                    duration = f"{task.duration_ms/1000:.1f}s"
            
            quality = f"{task.quality_score:.1f}" if task.quality_score else "N/A"
            
            task_id_short = task.task_id[:12] + "..." if len(task.task_id) > 15 else task.task_id
            task_type_short = task.task_type[:12] + "..." if len(task.task_type) > 15 else task.task_type
            
            print(f"{task_id_short:<15} {task_type_short:<15} {status_icon}{task.status:<9} {progress:<8} {duration:<10} {quality:<6}")
    
    def _display_recent_events(self):
        """显示最近事件"""
        events = self.monitor.get_recent_events(limit=10)
        
        if not events:
            return
        
        print("\n📝 最近事件")
        print("-" * 80)
        
        for event in events:
            timestamp = event.timestamp.strftime("%H:%M:%S")
            
            if event.event_type == "request":
                print(f"[{timestamp}] 📤 {event.source_agent} -> {event.target_agent} (请求)")
            elif event.event_type == "response":
                status_icon = "✅" if event.status == "success" else "❌"
                duration = f"({event.duration_ms:.1f}ms)" if event.duration_ms else ""
                print(f"[{timestamp}] 📥 {event.source_agent} -> {event.target_agent} {status_icon} {duration}")
            elif event.event_type == "error":
                print(f"[{timestamp}] 💥 {event.source_agent}: {event.error_message}")
            elif event.event_type == "status_change":
                status_icon = {
                    "started": "🚀",
                    "completed": "🎉", 
                    "failed": "💥",
                    "working": "🔄"
                }.get(event.status, "📋")
                print(f"[{timestamp}] {status_icon} 任务 {event.task_id[:12]}... {event.status}")
    
    def display_agent_details(self, agent_name: str):
        """显示Agent详细信息"""
        metrics = self.monitor.get_agent_metrics(agent_name)
        if agent_name not in metrics:
            print(f"❌ Agent '{agent_name}' 不存在")
            return
        
        agent = metrics[agent_name]
        
        print(f"\n🤖 Agent详情: {agent_name}")
        print("=" * 50)
        print(f"状态: {agent.status}")
        print(f"总请求数: {agent.total_requests}")
        print(f"成功请求: {agent.successful_requests}")
        print(f"失败请求: {agent.failed_requests}")
        print(f"错误次数: {agent.error_count}")
        print(f"平均响应时间: {agent.avg_response_time:.1f}ms")
        print(f"最小响应时间: {agent.min_response_time:.1f}ms")
        print(f"最大响应时间: {agent.max_response_time:.1f}ms")
        print(f"最后活动: {agent.last_activity.strftime('%Y-%m-%d %H:%M:%S') if agent.last_activity else 'N/A'}")
        
        # 显示相关事件
        events = [e for e in self.monitor.get_recent_events(50) 
                 if e.source_agent == agent_name or e.target_agent == agent_name]
        
        if events:
            print(f"\n📝 最近事件 (最多10条):")
            for event in events[:10]:
                timestamp = event.timestamp.strftime("%H:%M:%S")
                if event.event_type == "error":
                    print(f"  [{timestamp}] 💥 错误: {event.error_message}")
                elif event.event_type == "response":
                    status = "✅" if event.status == "success" else "❌"
                    duration = f"({event.duration_ms:.1f}ms)" if event.duration_ms else ""
                    print(f"  [{timestamp}] 📥 响应 {status} {duration}")
    
    def display_task_details(self, task_id: str):
        """显示任务详细信息"""
        metrics = self.monitor.get_task_metrics(task_id)
        if task_id not in metrics:
            print(f"❌ 任务 '{task_id}' 不存在")
            return
        
        task = metrics[task_id]
        
        print(f"\n📋 任务详情: {task_id}")
        print("=" * 50)
        print(f"类型: {task.task_type}")
        print(f"状态: {task.status}")
        print(f"开始时间: {task.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        if task.end_time:
            print(f"结束时间: {task.end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        if task.duration_ms:
            print(f"耗时: {task.duration_ms:.1f}ms")
        print(f"进度: {task.steps_completed}/{task.total_steps}")
        print(f"涉及Agent: {', '.join(task.involved_agents)}")
        if task.quality_score:
            print(f"质量评分: {task.quality_score:.1f}")
        if task.user_feedback:
            print(f"用户反馈: {task.user_feedback}")
        
        # 显示相关事件
        events = [e for e in self.monitor.get_recent_events(100) if e.task_id == task_id]
        
        if events:
            print(f"\n📝 任务事件:")
            for event in sorted(events, key=lambda x: x.timestamp):
                timestamp = event.timestamp.strftime("%H:%M:%S")
                if event.event_type == "status_change":
                    print(f"  [{timestamp}] 📋 状态变更: {event.status}")
                elif event.event_type == "request":
                    print(f"  [{timestamp}] 📤 请求: {event.source_agent} -> {event.target_agent}")
                elif event.event_type == "response":
                    status = "✅" if event.status == "success" else "❌"
                    duration = f"({event.duration_ms:.1f}ms)" if event.duration_ms else ""
                    print(f"  [{timestamp}] 📥 响应: {event.source_agent} {status} {duration}")
                elif event.event_type == "error":
                    print(f"  [{timestamp}] 💥 错误: {event.error_message}")

def main():
    """主函数 - 交互式监控面板"""
    dashboard = MonitoringDashboard()
    
    try:
        print("🚀 启动A2A监控面板...")
        dashboard.start()
        
        print("\n📋 可用命令:")
        print("  stats - 显示系统统计")
        print("  agents - 显示Agent状态")
        print("  tasks - 显示任务状态")
        print("  events - 显示最近事件")
        print("  agent <name> - 显示Agent详情")
        print("  task <id> - 显示任务详情")
        print("  export <file> - 导出监控数据")
        print("  help - 显示帮助")
        print("  quit - 退出")
        
        while True:
            try:
                command = input("\n> ").strip().lower()
                
                if command == "quit" or command == "exit":
                    break
                elif command == "stats":
                    stats = dashboard.monitor.get_system_stats()
                    print(json.dumps(stats, indent=2, ensure_ascii=False))
                elif command == "agents":
                    dashboard._display_agent_status()
                elif command == "tasks":
                    dashboard._display_task_status()
                elif command == "events":
                    dashboard._display_recent_events()
                elif command.startswith("agent "):
                    agent_name = command[6:].strip()
                    dashboard.display_agent_details(agent_name)
                elif command.startswith("task "):
                    task_id = command[5:].strip()
                    dashboard.display_task_details(task_id)
                elif command.startswith("export "):
                    filepath = command[7:].strip()
                    dashboard.monitor.export_metrics(filepath)
                elif command == "help":
                    print("\n📋 可用命令:")
                    print("  stats - 显示系统统计")
                    print("  agents - 显示Agent状态")
                    print("  tasks - 显示任务状态")
                    print("  events - 显示最近事件")
                    print("  agent <name> - 显示Agent详情")
                    print("  task <id> - 显示任务详情")
                    print("  export <file> - 导出监控数据")
                    print("  help - 显示帮助")
                    print("  quit - 退出")
                elif command:
                    print("❌ 未知命令，输入 'help' 查看可用命令")
                    
            except KeyboardInterrupt:
                break
            except EOFError:
                break
            except Exception as e:
                print(f"命令执行错误: {e}")
    
    finally:
        dashboard.stop()
        print("\n👋 监控面板已退出")

if __name__ == "__main__":
    main()