#!/usr/bin/env python3
"""
Content Agent数据模型
定义内容生成相关的数据结构
"""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from enum import Enum


class ContentType(str, Enum):
    """内容类型"""
    SCENE = "场景描写"
    DIALOGUE = "对话内容"
    CHAPTER = "章节内容"
    DESCRIPTION = "环境描述"
    ACTION = "动作描写"
    EMOTION = "情感描写"


class WritingStyle(str, Enum):
    """写作风格"""
    MODERN_URBAN = "现代都市"
    ANCIENT_ROMANCE = "古代言情"
    FANTASY_CULTIVATION = "玄幻修仙"
    HISTORICAL_FICTION = "历史小说"
    SCIENCE_FICTION = "科幻小说"
    MYSTERY_THRILLER = "悬疑推理"
    YOUTH_CAMPUS = "青春校园"


class ContentRequirement(BaseModel):
    """内容生成要求"""
    content_type: ContentType = Field(description="内容类型")
    writing_style: WritingStyle = Field(description="写作风格")
    word_count: Optional[int] = Field(None, description="字数要求")
    tone: Optional[str] = Field(None, description="语调风格")
    perspective: Optional[str] = Field(None, description="叙述视角")
    special_requirements: List[str] = Field(default_factory=list, description="特殊要求")


class SceneRequirement(BaseModel):
    """场景描写要求"""
    location: str = Field(description="场景地点")
    time: Optional[str] = Field(None, description="时间设定")
    weather: Optional[str] = Field(None, description="天气情况")
    atmosphere: Optional[str] = Field(None, description="氛围营造")
    characters_present: List[str] = Field(default_factory=list, description="在场角色")
    key_events: List[str] = Field(default_factory=list, description="关键事件")
    sensory_details: List[str] = Field(default_factory=list, description="感官细节")


class DialogueRequirement(BaseModel):
    """对话生成要求"""
    characters: List[str] = Field(description="对话角色")
    character_personalities: Dict[str, str] = Field(description="角色性格特点")
    dialogue_purpose: str = Field(description="对话目的")
    emotional_tone: Optional[str] = Field(None, description="情感基调")
    conflict_level: Optional[int] = Field(None, ge=1, le=10, description="冲突程度(1-10)")
    dialogue_length: Optional[str] = Field(None, description="对话长度")


class ChapterRequirement(BaseModel):
    """章节生成要求"""
    chapter_title: Optional[str] = Field(None, description="章节标题")
    plot_summary: str = Field(description="情节概要")
    key_scenes: List[str] = Field(description="关键场景")
    character_arcs: Dict[str, str] = Field(description="角色发展")
    chapter_goal: str = Field(description="章节目标")
    hooks: List[str] = Field(default_factory=list, description="悬念设置")
    transitions: List[str] = Field(default_factory=list, description="过渡安排")


class GeneratedContent(BaseModel):
    """生成的内容"""
    content_type: ContentType = Field(description="内容类型")
    title: Optional[str] = Field(None, description="标题")
    content: str = Field(description="生成的内容")
    word_count: int = Field(description="字数统计")
    writing_style: WritingStyle = Field(description="写作风格")
    quality_score: Optional[float] = Field(None, ge=0, le=10, description="质量评分")
    tags: List[str] = Field(default_factory=list, description="内容标签")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class ContentRevision(BaseModel):
    """内容修订"""
    original_content: str = Field(description="原始内容")
    revised_content: str = Field(description="修订后内容")
    revision_type: str = Field(description="修订类型")
    revision_reason: str = Field(description="修订原因")
    improvement_areas: List[str] = Field(description="改进方面")


class ContentTemplate(BaseModel):
    """内容模板"""
    template_name: str = Field(description="模板名称")
    content_type: ContentType = Field(description="内容类型")
    writing_style: WritingStyle = Field(description="写作风格")
    template_structure: List[str] = Field(description="模板结构")
    example_content: Optional[str] = Field(None, description="示例内容")
    usage_tips: List[str] = Field(default_factory=list, description="使用技巧")


class ContentGenerationResponse(BaseModel):
    """内容生成响应格式"""
    generated_content: GeneratedContent = Field(description="生成的内容")
    generation_notes: List[str] = Field(default_factory=list, description="生成说明")
    suggestions: List[str] = Field(default_factory=list, description="优化建议")
    alternative_versions: List[str] = Field(default_factory=list, description="备选版本")


class ContentAnalysis(BaseModel):
    """内容分析"""
    content: str = Field(description="分析的内容")
    readability_score: Optional[float] = Field(None, description="可读性评分")
    emotional_tone: Optional[str] = Field(None, description="情感基调")
    pacing: Optional[str] = Field(None, description="节奏感")
    character_voice: Optional[str] = Field(None, description="角色声音")
    strengths: List[str] = Field(default_factory=list, description="优点")
    weaknesses: List[str] = Field(default_factory=list, description="不足")
    improvement_suggestions: List[str] = Field(default_factory=list, description="改进建议")