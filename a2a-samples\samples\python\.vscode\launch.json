{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug Semantic Kernel Agent",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/agents/semantickernel/__main__.py",
            "console": "integratedTerminal",
            "justMyCode": false,
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
            },
            "args": [
                "--host", "localhost",
                "--port", "10020"
            ]
        }
    ]
}
