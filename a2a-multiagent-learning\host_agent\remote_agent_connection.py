#!/usr/bin/env python3
"""
Remote Agent连接管理器
封装A2A客户端，管理与写作相关Agents的连接
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum
import aiohttp
import json
from urllib.parse import urljoin

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AgentStatus(Enum):
    """Agent状态枚举"""
    UNKNOWN = "unknown"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    ERROR = "error"


@dataclass
class AgentInfo:
    """Agent信息数据类"""
    name: str
    url: str
    port: int
    status: AgentStatus = AgentStatus.UNKNOWN
    capabilities: List[str] = None
    skills: List[Dict[str, Any]] = None
    last_error: Optional[str] = None
    retry_count: int = 0
    
    def __post_init__(self):
        if self.capabilities is None:
            self.capabilities = []
        if self.skills is None:
            self.skills = []


class RemoteAgentConnection:
    """单个Remote Agent连接管理器"""
    
    def __init__(self, agent_info: AgentInfo, max_retries: int = 3, timeout: int = 30):
        self.agent_info = agent_info
        self.max_retries = max_retries
        self.timeout = timeout
        self.session: Optional[aiohttp.ClientSession] = None
        self._lock = asyncio.Lock()
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()
    
    async def connect(self) -> bool:
        """连接到Agent"""
        async with self._lock:
            if self.session and not self.session.closed:
                return True
            
            try:
                self.agent_info.status = AgentStatus.CONNECTING
                logger.info(f"连接到 {self.agent_info.name} ({self.agent_info.url})")
                
                # 创建HTTP会话
                timeout = aiohttp.ClientTimeout(total=self.timeout)
                self.session = aiohttp.ClientSession(timeout=timeout)
                
                # 获取Agent Card验证连接
                agent_card = await self.get_agent_card()
                if agent_card:
                    self.agent_info.capabilities = agent_card.get('capabilities', {}).get('modes', [])
                    self.agent_info.skills = agent_card.get('skills', [])
                    self.agent_info.status = AgentStatus.CONNECTED
                    self.agent_info.retry_count = 0
                    self.agent_info.last_error = None
                    logger.info(f"✅ 成功连接到 {self.agent_info.name}")
                    return True
                else:
                    raise Exception("无法获取Agent Card")
                    
            except Exception as e:
                self.agent_info.status = AgentStatus.ERROR
                self.agent_info.last_error = str(e)
                self.agent_info.retry_count += 1
                logger.error(f"❌ 连接 {self.agent_info.name} 失败: {e}")
                
                if self.session:
                    await self.session.close()
                    self.session = None
                
                return False
    
    async def disconnect(self):
        """断开连接"""
        async with self._lock:
            if self.session and not self.session.closed:
                await self.session.close()
                self.session = None
            
            self.agent_info.status = AgentStatus.DISCONNECTED
            logger.info(f"断开与 {self.agent_info.name} 的连接")
    
    async def get_agent_card(self) -> Optional[Dict[str, Any]]:
        """获取Agent Card"""
        if not self.session:
            return None
        
        try:
            url = urljoin(self.agent_info.url, "/")
            async with self.session.get(url) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logger.warning(f"获取Agent Card失败，状态码: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"获取Agent Card异常: {e}")
            return None
    
    async def send_task(self, task_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """发送任务到Agent"""
        if not self.session or self.agent_info.status != AgentStatus.CONNECTED:
            logger.warning(f"Agent {self.agent_info.name} 未连接，无法发送任务")
            return None
        
        try:
            url = urljoin(self.agent_info.url, "/tasks")
            headers = {'Content-Type': 'application/json'}
            
            async with self.session.post(url, json=task_data, headers=headers) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logger.error(f"发送任务失败，状态码: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"发送任务异常: {e}")
            return None
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        if not self.session or self.agent_info.status != AgentStatus.CONNECTED:
            return None
        
        try:
            url = urljoin(self.agent_info.url, f"/tasks/{task_id}")
            async with self.session.get(url) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return None
        except Exception as e:
            logger.error(f"获取任务状态异常: {e}")
            return None
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            agent_card = await self.get_agent_card()
            if agent_card:
                self.agent_info.status = AgentStatus.CONNECTED
                return True
            else:
                self.agent_info.status = AgentStatus.ERROR
                return False
        except Exception as e:
            self.agent_info.status = AgentStatus.ERROR
            self.agent_info.last_error = str(e)
            return False


class RemoteAgentConnections:
    """Remote Agent连接管理器"""
    
    def __init__(self, max_retries: int = 3, health_check_interval: int = 60):
        self.agents: Dict[str, RemoteAgentConnection] = {}
        self.max_retries = max_retries
        self.health_check_interval = health_check_interval
        self._health_check_task: Optional[asyncio.Task] = None
        self._shutdown = False
        
        # 预定义的写作相关Agents
        self.default_agents = {
            "plot_agent": AgentInfo(
                name="Plot Agent",
                url="http://localhost:10006/",
                port=10006
            ),
            "character_agent": AgentInfo(
                name="Character Agent", 
                url="http://localhost:10003/",
                port=10003
            ),
            "content_agent": AgentInfo(
                name="Content Agent",
                url="http://localhost:10004/",
                port=10004
            )
        }
    
    async def initialize(self) -> bool:
        """初始化连接管理器"""
        logger.info("🚀 初始化Remote Agent连接管理器...")
        
        # 注册默认的写作Agents
        for agent_id, agent_info in self.default_agents.items():
            await self.register_agent(agent_id, agent_info)
        
        # 启动健康检查任务
        self._health_check_task = asyncio.create_task(self._health_check_loop())
        
        logger.info("✅ Remote Agent连接管理器初始化完成")
        return True
    
    async def shutdown(self):
        """关闭连接管理器"""
        logger.info("🔄 关闭Remote Agent连接管理器...")
        
        self._shutdown = True
        
        # 停止健康检查任务
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        # 断开所有连接
        for connection in self.agents.values():
            await connection.disconnect()
        
        self.agents.clear()
        logger.info("✅ Remote Agent连接管理器已关闭")
    
    async def register_agent(self, agent_id: str, agent_info: AgentInfo) -> bool:
        """注册Agent"""
        logger.info(f"📝 注册Agent: {agent_id} ({agent_info.name})")
        
        connection = RemoteAgentConnection(agent_info, self.max_retries)
        self.agents[agent_id] = connection
        
        # 尝试连接
        success = await connection.connect()
        if success:
            logger.info(f"✅ Agent {agent_id} 注册并连接成功")
        else:
            logger.warning(f"⚠️ Agent {agent_id} 注册成功但连接失败")
        
        return success
    
    async def unregister_agent(self, agent_id: str) -> bool:
        """注销Agent"""
        if agent_id in self.agents:
            await self.agents[agent_id].disconnect()
            del self.agents[agent_id]
            logger.info(f"📤 Agent {agent_id} 已注销")
            return True
        return False
    
    async def get_agent_connection(self, agent_id: str) -> Optional[RemoteAgentConnection]:
        """获取Agent连接"""
        if agent_id not in self.agents:
            return None
        
        connection = self.agents[agent_id]
        
        # 检查连接状态，如果需要则重连
        if connection.agent_info.status != AgentStatus.CONNECTED:
            await connection.connect()
        
        return connection if connection.agent_info.status == AgentStatus.CONNECTED else None
    
    async def send_task_to_agent(self, agent_id: str, task_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """发送任务到指定Agent"""
        connection = await self.get_agent_connection(agent_id)
        if connection:
            return await connection.send_task(task_data)
        else:
            logger.error(f"无法连接到Agent: {agent_id}")
            return None
    
    async def get_available_agents(self) -> List[str]:
        """获取可用的Agent列表"""
        available = []
        for agent_id, connection in self.agents.items():
            if connection.agent_info.status == AgentStatus.CONNECTED:
                available.append(agent_id)
        return available
    
    async def get_agent_capabilities(self, agent_id: str) -> List[str]:
        """获取Agent能力列表"""
        if agent_id in self.agents:
            return self.agents[agent_id].agent_info.capabilities
        return []
    
    async def get_agent_skills(self, agent_id: str) -> List[Dict[str, Any]]:
        """获取Agent技能列表"""
        if agent_id in self.agents:
            return self.agents[agent_id].agent_info.skills
        return []
    
    async def get_all_agent_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有Agent状态"""
        status = {}
        for agent_id, connection in self.agents.items():
            info = connection.agent_info
            status[agent_id] = {
                'name': info.name,
                'url': info.url,
                'status': info.status.value,
                'capabilities': info.capabilities,
                'skills_count': len(info.skills),
                'last_error': info.last_error,
                'retry_count': info.retry_count
            }
        return status
    
    async def discover_agents(self, base_urls: List[str]) -> List[str]:
        """发现可用的Agents"""
        discovered = []
        
        for url in base_urls:
            try:
                timeout = aiohttp.ClientTimeout(total=5)
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.get(url) as response:
                        if response.status == 200:
                            agent_card = await response.json()
                            agent_name = agent_card.get('name', 'Unknown')
                            
                            # 创建Agent信息
                            agent_info = AgentInfo(
                                name=agent_name,
                                url=url,
                                port=int(url.split(':')[-1].rstrip('/')) if ':' in url else 80
                            )
                            
                            # 生成agent_id
                            agent_id = agent_name.lower().replace(' ', '_').replace('-', '_')
                            
                            # 注册发现的Agent
                            if await self.register_agent(agent_id, agent_info):
                                discovered.append(agent_id)
                                logger.info(f"🔍 发现并注册Agent: {agent_id} ({agent_name})")
                            
            except Exception as e:
                logger.debug(f"发现Agent失败 {url}: {e}")
        
        return discovered
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while not self._shutdown:
            try:
                await asyncio.sleep(self.health_check_interval)
                
                if self._shutdown:
                    break
                
                logger.debug("🔍 执行Agent健康检查...")
                
                for agent_id, connection in self.agents.items():
                    if connection.agent_info.status == AgentStatus.CONNECTED:
                        healthy = await connection.health_check()
                        if not healthy:
                            logger.warning(f"⚠️ Agent {agent_id} 健康检查失败")
                            # 尝试重连
                            await connection.connect()
                    elif connection.agent_info.status == AgentStatus.ERROR:
                        # 对错误状态的Agent尝试重连
                        if connection.agent_info.retry_count < self.max_retries:
                            logger.info(f"🔄 尝试重连Agent {agent_id}")
                            await connection.connect()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康检查异常: {e}")
    
    async def create_writing_session(self, session_id: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """创建写作会话"""
        session_data = {
            'session_id': session_id,
            'context': context,
            'created_at': asyncio.get_event_loop().time(),
            'agents': list(self.agents.keys()),
            'status': 'active'
        }
        
        logger.info(f"📝 创建写作会话: {session_id}")
        return session_data
    
    async def get_writing_context(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取写作会话上下文"""
        # 这里可以实现会话存储逻辑
        # 暂时返回基本信息
        return {
            'session_id': session_id,
            'available_agents': await self.get_available_agents(),
            'agent_status': await self.get_all_agent_status()
        }


# 全局连接管理器实例
_connection_manager: Optional[RemoteAgentConnections] = None


async def get_connection_manager() -> RemoteAgentConnections:
    """获取全局连接管理器实例"""
    global _connection_manager
    
    if _connection_manager is None:
        _connection_manager = RemoteAgentConnections()
        await _connection_manager.initialize()
    
    return _connection_manager


async def shutdown_connection_manager():
    """关闭全局连接管理器"""
    global _connection_manager
    
    if _connection_manager:
        await _connection_manager.shutdown()
        _connection_manager = None