#!/usr/bin/env python3
"""
Plot Agent A2A服务器测试脚本
"""

import asyncio
import sys
import json
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_plot_agent_executor():
    """测试Plot Agent执行器"""
    print("🧪 测试Plot Agent A2A执行器...")
    
    try:
        from plot_agent.plot_executor import PlotAgentExecutor
        
        # 创建执行器
        executor = PlotAgentExecutor()
        
        print("✅ Plot Agent执行器创建成功")
        print(f"   Agent名称: {executor.agent_card.name}")
        print(f"   版本: {executor.agent_card.version}")
        print(f"   能力数量: {len(executor.agent_card.capabilities)}")
        
        # 测试状态获取
        status = await executor.get_status()
        print(f"✅ 执行器状态: {status['status']}")
        print(f"   健康状态: {status['health']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Plot Agent执行器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_classic_plot_analysis_task():
    """测试经典情节分析任务"""
    print("\n🎭 测试经典情节分析任务...")
    
    try:
        from plot_agent.plot_executor import PlotAgentExecutor
        
        executor = PlotAgentExecutor()
        
        # 准备任务数据
        task_data = {
            "type": "analyze_classic_plot",
            "plot_type": "探寻",
            "story_context": "一个年轻的考古学家寻找失落的古代文明遗迹",
            "adaptation_requirements": "现代背景，加入科技元素"
        }
        
        # 执行任务
        task_id = "test_classic_plot_001"
        events = []
        
        async for event in executor.execute_task(task_id, task_data):
            events.append(event)
            print(f"📨 事件: {event.type} - {event.data.get('message', '')}")
        
        # 检查结果
        completed_event = next((e for e in events if e.type == "task_completed"), None)
        if completed_event:
            result = completed_event.data.get("result", "")
            print(f"✅ 经典情节分析完成")
            print(f"   结果长度: {len(result)} 字符")
            print(f"   结果预览: {result[:200]}...")
            return True
        else:
            print("❌ 未找到完成事件")
            return False
            
    except Exception as e:
        print(f"❌ 经典情节分析任务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_story_outline_generation_task():
    """测试故事大纲生成任务"""
    print("\n📖 测试故事大纲生成任务...")
    
    try:
        from plot_agent.plot_executor import PlotAgentExecutor
        
        executor = PlotAgentExecutor()
        
        # 准备任务数据
        task_data = {
            "type": "generate_story_outline",
            "genre": "现代都市",
            "theme": "成长",
            "target_length": "中篇",
            "structure_type": "三幕式",
            "additional_requirements": "职场背景，主角是刚毕业的大学生"
        }
        
        # 执行任务
        task_id = "test_outline_001"
        events = []
        
        async for event in executor.execute_task(task_id, task_data):
            events.append(event)
            print(f"📨 事件: {event.type}")
        
        # 检查结果
        completed_event = next((e for e in events if e.type == "task_completed"), None)
        if completed_event:
            result = completed_event.data.get("result", "")
            print(f"✅ 故事大纲生成完成")
            print(f"   结果长度: {len(result)} 字符")
            return True
        else:
            print("❌ 未找到完成事件")
            return False
            
    except Exception as e:
        print(f"❌ 故事大纲生成任务测试失败: {e}")
        return False

async def test_general_request_task():
    """测试通用请求任务"""
    print("\n💬 测试通用请求任务...")
    
    try:
        from plot_agent.plot_executor import PlotAgentExecutor
        
        executor = PlotAgentExecutor()
        
        # 准备任务数据
        task_data = {
            "type": "general_request",
            "message": "我想写一个关于时间旅行的科幻小说，请给我一些情节建议",
            "context": {"genre": "科幻", "theme": "时间旅行"}
        }
        
        # 执行任务
        task_id = "test_general_001"
        events = []
        
        async for event in executor.execute_task(task_id, task_data):
            events.append(event)
            print(f"📨 事件: {event.type}")
        
        # 检查结果
        completed_event = next((e for e in events if e.type == "task_completed"), None)
        if completed_event:
            result = completed_event.data.get("result", "")
            print(f"✅ 通用请求处理完成")
            print(f"   结果长度: {len(result)} 字符")
            return True
        else:
            print("❌ 未找到完成事件")
            return False
            
    except Exception as e:
        print(f"❌ 通用请求任务测试失败: {e}")
        return False

async def test_agent_card():
    """测试Agent Card"""
    print("\n📋 测试Agent Card...")
    
    try:
        from plot_agent.plot_executor import PlotAgentExecutor
        
        executor = PlotAgentExecutor()
        card = executor.agent_card
        
        print(f"✅ Agent Card信息:")
        print(f"   名称: {card.name}")
        print(f"   描述: {card.description}")
        print(f"   版本: {card.version}")
        print(f"   能力数量: {len(card.capabilities)}")
        print(f"   技能数量: {len(card.skills)}")
        print(f"   标签: {', '.join(card.tags)}")
        
        # 检查元数据
        metadata = card.metadata
        print(f"   模板数量: {metadata.get('template_count', 0)}")
        print(f"   经典情节数量: {len(metadata.get('classic_plots', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent Card测试失败: {e}")
        return False

async def test_task_management():
    """测试任务管理功能"""
    print("\n⚙️ 测试任务管理功能...")
    
    try:
        from plot_agent.plot_executor import PlotAgentExecutor
        
        executor = PlotAgentExecutor()
        
        # 测试取消任务
        task_id = "test_cancel_001"
        executor.current_task_id = task_id
        executor.task_status = executor.task_status.WORKING
        
        result = await executor.cancel_task(task_id)
        print(f"✅ 任务取消测试: {result}")
        
        # 测试暂停和恢复任务
        task_id = "test_pause_001"
        executor.current_task_id = task_id
        executor.task_status = executor.task_status.WORKING
        
        pause_result = await executor.pause_task(task_id)
        print(f"✅ 任务暂停测试: {pause_result}")
        
        resume_result = await executor.resume_task(task_id)
        print(f"✅ 任务恢复测试: {resume_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务管理功能测试失败: {e}")
        return False

async def main():
    """运行所有测试"""
    print("🧪 开始测试Plot Agent A2A功能...")
    print("=" * 60)
    
    tests = [
        ("Plot Agent执行器", test_plot_agent_executor),
        ("Agent Card", test_agent_card),
        ("经典情节分析任务", test_classic_plot_analysis_task),
        ("故事大纲生成任务", test_story_outline_generation_task),
        ("通用请求任务", test_general_request_task),
        ("任务管理功能", test_task_management),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔍 测试: {test_name}")
            if await test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有A2A功能测试通过！")
        print("\n💡 启动A2A服务器命令:")
        print("   python -m plot_agent --mode a2a")
        print("   python -m plot_agent --mode a2a --host 0.0.0.0 --port 10001")
    else:
        print("❌ 部分测试失败，请检查实现")

if __name__ == "__main__":
    asyncio.run(main())