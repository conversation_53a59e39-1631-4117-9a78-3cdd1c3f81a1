#!/usr/bin/env python3
"""
跨平台启动所有写作Agent的Python脚本
"""
import os
import sys
import time
import subprocess
import platform
from pathlib import Path


def check_env_file():
    """检查环境变量文件是否存在"""
    if not Path(".env").exists():
        print("[ERROR] 错误: .env 文件不存在")
        print("请先复制 .env.example 为 .env 并配置API密钥")
        return False
    return True


def create_logs_dir():
    """创建日志目录"""
    logs_dir = Path("logs")
    if not logs_dir.exists():
        logs_dir.mkdir()
        print("[INFO] 创建日志目录: logs/")


def start_agent(agent_name: str, agent_dir: str, port: int):
    """启动单个Agent"""
    print(f"[START] 启动 {agent_name} (端口{port})...")
    
    # 构建命令
    if platform.system() == "Windows":
        # Windows下使用start命令在新窗口中启动
        cmd = [
            "cmd", "/c", "start", f"{agent_name}",
            "cmd", "/c", 
            f"cd {agent_dir} && python -m {agent_dir.replace('_', '_')} > ../logs/{agent_dir}.log 2>&1"
        ]
    else:
        # Unix/Linux下使用nohup在后台启动
        cmd = [
            "bash", "-c",
            f"cd {agent_dir} && nohup python -m {agent_dir.replace('_', '_')} > ../logs/{agent_dir}.log 2>&1 &"
        ]
    
    try:
        if platform.system() == "Windows":
            # Windows下直接启动新进程
            subprocess.Popen([
                "cmd", "/c", f"cd {agent_dir} && python -m {agent_dir}"
            ], creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:
            # Unix/Linux下后台启动
            subprocess.Popen([
                "bash", "-c",
                f"cd {agent_dir} && python -m {agent_dir} > ../logs/{agent_dir}.log 2>&1 &"
            ])
        
        print(f"[OK] {agent_name} 启动成功")
        return True

    except Exception as e:
        print(f"[ERROR] {agent_name} 启动失败: {e}")
        return False


def main():
    """主函数"""
    print("[SYSTEM] A2A网文写作助手系统启动器")
    print("=" * 40)
    
    # 检查环境
    if not check_env_file():
        return 1
    
    # 创建日志目录
    create_logs_dir()
    
    # 定义要启动的Agent
    agents = [
        ("Plot Agent", "plot_agent", 10001),
        ("Character Agent", "character_agent", 10002),
        ("Content Agent", "content_agent", 10003),
        ("Host Agent", "host_agent", 12000)
    ]
    
    # 启动所有Agent
    success_count = 0
    for agent_name, agent_dir, port in agents:
        if start_agent(agent_name, agent_dir, port):
            success_count += 1
        
        # 等待启动
        time.sleep(2)
    
    print("\n" + "=" * 40)
    print(f"[RESULT] 成功启动 {success_count}/{len(agents)} 个Agent")

    if success_count == len(agents):
        print("\n[INFO] 服务地址:")
        for agent_name, _, port in agents:
            print(f"   {agent_name}: http://localhost:{port}")

        print(f"\n[GUIDE] 使用说明:")
        print(f"   - 查看日志: 检查 logs/ 目录下的日志文件")
        print(f"   - 测试系统: python scripts/test_writing_system.py")
        print(f"   - 停止服务: 关闭对应的进程或命令行窗口")
    else:
        print("\n[WARN] 部分Agent启动失败，请检查错误信息")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())