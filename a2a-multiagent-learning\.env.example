# A2A多Agent协调系统环境变量配置模板 - 网文写作助手
# 复制此文件为 .env 并填入实际的API密钥和配置

# =============================================================================
# AI模型配置 (支持OpenAI兼容API)
# =============================================================================

# 第三方AI API配置 (兼容OpenAI格式)
OPENAI_API_KEY=sk-VV4jqqjlIWn4hS46EiUyUSp3J85j0KAa56Gq9g4HF7EeIwFx
OPENAI_BASE_URL=https://noapi.ggb.today/v1
OPENAI_MODEL=gemini-2.5-pro-preview-06-05

# Google AI配置 (备用选项，如果不使用第三方API)
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_GENAI_USE_VERTEXAI=FALSE
GOOGLE_CLOUD_PROJECT=your_project_id
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_GENAI_MODEL=gemini-2.5-flash-preview-04-17

# 使用哪种AI服务 (openai_compatible, google_ai)
AI_PROVIDER=openai_compatible

# =============================================================================
# 写作Agent服务器配置
# =============================================================================

# Plot Agent (情节规划) 服务器地址
PLOT_AGENT_URL=http://localhost:10001
PLOT_AGENT_HOST=localhost
PLOT_AGENT_PORT=10001

# Character Agent (角色设定) 服务器地址
CHARACTER_AGENT_URL=http://localhost:10002
CHARACTER_AGENT_HOST=localhost
CHARACTER_AGENT_PORT=10002

# Content Agent (内容生成) 服务器地址
CONTENT_AGENT_URL=http://localhost:10003
CONTENT_AGENT_HOST=localhost
CONTENT_AGENT_PORT=10003

# Host Agent (写作协调器) 服务器地址
HOST_AGENT_URL=http://localhost:12000
HOST_AGENT_HOST=localhost
HOST_AGENT_PORT=12000

# =============================================================================
# 写作相关配置
# =============================================================================

# 默认写作风格 (modern_urban, ancient_romance, fantasy_cultivation)
DEFAULT_WRITING_STYLE=modern_urban

# 默认故事结构模板 (three_act, hero_journey, save_the_cat)
DEFAULT_STORY_STRUCTURE=three_act

# 内容生成最大长度 (字符数)
MAX_CONTENT_LENGTH=5000

# 角色档案存储路径
CHARACTER_PROFILES_PATH=./data/characters

# 故事项目存储路径
STORY_PROJECTS_PATH=./data/projects

# =============================================================================
# 系统配置
# =============================================================================

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# HTTP请求超时时间 (秒)
HTTP_TIMEOUT=30

# Agent发现超时时间 (秒)
AGENT_DISCOVERY_TIMEOUT=10

# 最大重试次数
MAX_RETRY_ATTEMPTS=3

# =============================================================================
# 开发和调试配置
# =============================================================================

# 是否启用详细日志
VERBOSE_LOGGING=FALSE

# 是否启用性能监控
ENABLE_MONITORING=FALSE

# 测试模式 (跳过某些外部API调用)
TEST_MODE=FALSE