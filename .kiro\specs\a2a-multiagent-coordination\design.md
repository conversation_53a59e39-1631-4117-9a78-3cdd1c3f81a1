# A2A多Agent协调系统设计文档 - 网文写作助手

## 概述

本设计文档详细描述了如何使用A2A协议构建一个网文写作助手的多agent协调系统。该系统展示了Host Agent如何智能地协调多个专门化的Remote Agents（情节规划、角色设定、文本生成）来辅助网文创作的复杂任务。

## 架构

### 系统架构图

```mermaid
graph TB
    User[网文作者] --> HostAgent[Host Agent<br/>写作协调器]
    
    HostAgent --> PlotAgent[Plot Agent<br/>情节规划]
    HostAgent --> CharacterAgent[Character Agent<br/>角色设定]
    HostAgent --> ContentAgent[Content Agent<br/>文本生成]
    
    PlotAgent --> PlotMCP[Plot MCP Server<br/>故事结构工具]
    CharacterAgent --> CharacterMCP[Character MCP Server<br/>角色档案工具]
    ContentAgent --> ContentMCP[Content MCP Server<br/>文本生成工具]
    
    subgraph "A2A协议通信"
        HostAgent -.->|A2A Protocol| PlotAgent
        HostAgent -.->|A2A Protocol| CharacterAgent
        HostAgent -.->|A2A Protocol| ContentAgent
    end
    
    subgraph "MCP工具集成"
        PlotAgent -.->|MCP Protocol| PlotMCP
        CharacterAgent -.->|MCP Protocol| CharacterMCP
        ContentAgent -.->|MCP Protocol| ContentMCP
    end
```

### 核心组件

1. **Host Agent (写作协调器)**
   - 基于Google ADK构建
   - 负责理解作者的写作需求并路由到合适的Remote Agent
   - 维护写作项目的会话状态和上下文
   - 协调多个agents的协作完成复杂的创作任务

2. **Plot Agent (情节规划)**
   - 专门处理故事情节和结构规划
   - 通过MCP协议集成故事结构分析工具
   - 支持生成故事大纲、章节规划、情节发展线
   - 提供经典故事结构模板（三幕式、英雄之旅等）

3. **Character Agent (角色设定)**
   - 专门处理角色创建和人物设定
   - 基于LangGraph ReAct模式构建
   - 通过MCP工具访问角色档案模板和性格分析工具
   - 支持角色关系网络构建和人物弧线设计

4. **Content Agent (文本生成)**
   - 专门处理具体的文本内容生成
   - 集成多种文本生成模型和写作风格
   - 根据情节大纲和角色设定生成章节内容
   - 支持不同文体和风格的适配（玄幻、都市、历史等）

## 组件和接口

### Host Agent (RoutingAgent)

**核心职责：**
- 用户意图识别和任务分解
- Remote Agent发现和注册
- 任务路由和结果聚合
- 会话状态管理

**关键接口：**
```python
class RoutingAgent:
    async def send_message(self, agent_name: str, task: str, tool_context: ToolContext)
    def list_remote_agents(self) -> list[dict]
    async def _async_init_components(self, remote_agent_addresses: list[str])
```

**路由逻辑：**
- 使用Gemini 2.5 Flash模型进行意图理解
- 基于agent capabilities和用户查询内容进行路由决策
- 支持上下文感知的多轮对话

### Plot Agent (情节规划)

**技术栈：**
- Google ADK LlmAgent
- MCP (Model Context Protocol) 工具集成
- FastMCP服务器

**MCP工具：**
```python
@mcp.tool()
async def generate_story_outline(genre: str, theme: str, length: str) -> str
    
@mcp.tool()
async def create_chapter_structure(outline: str, chapter_count: int) -> str
    
@mcp.tool()
async def analyze_plot_conflicts(outline: str) -> str

@mcp.tool()
async def suggest_plot_twists(current_plot: str, genre: str) -> str
```

**故事结构模板：**
- 三幕式结构
- 英雄之旅模板
- 悬疑推理结构
- 言情小说结构

### Character Agent (角色设定)

**技术栈：**
- LangGraph ReAct Agent
- LangChain Google Generative AI
- MCP工具集成
- 内存检查点 (MemorySaver)

**响应格式：**
```python
class CharacterResponseFormat(BaseModel):
    status: Literal['input_required', 'completed', 'error']
    character_data: dict
    message: str
```

**核心方法：**
```python
class CharacterAgent:
    async def create_character(self, requirements: str, session_id: str) -> dict[str, Any]
    async def build_relationship_network(self, characters: list, session_id: str) -> dict[str, Any]
    async def stream(self, query: str, session_id: str) -> AsyncIterable[Any]
```

**MCP工具：**
```python
@mcp.tool()
async def generate_character_profile(name: str, role: str, genre: str) -> str

@mcp.tool()
async def create_character_backstory(profile: dict, plot_context: str) -> str

@mcp.tool()
async def design_character_arc(character: dict, story_outline: str) -> str
```

### Content Agent (文本生成)

**技术栈：**
- LangGraph ReAct Agent
- 多模型集成 (Gemini, Claude, GPT)
- 风格适配器
- 内容质量评估

**响应格式：**
```python
class ContentResponseFormat(BaseModel):
    status: Literal['input_required', 'completed', 'error']
    content: str
    word_count: int
    style_score: float
    message: str
```

**核心方法：**
```python
class ContentAgent:
    async def generate_chapter(self, outline: str, characters: dict, style: str) -> dict[str, Any]
    async def continue_story(self, previous_content: str, direction: str) -> dict[str, Any]
    async def refine_content(self, content: str, feedback: str) -> dict[str, Any]
```

### A2A通信层

**RemoteAgentConnections类：**
```python
class RemoteAgentConnections:
    def __init__(self, agent_card: AgentCard, agent_url: str)
    async def send_message(self, message_request: SendMessageRequest) -> SendMessageResponse
```

**A2A协议特性：**
- 标准化的AgentCard发现机制
- JSON-RPC 2.0消息格式
- 流式和非流式通信支持
- 任务状态管理和错误处理

## 数据模型

### AgentCard模型
```python
AgentCard(
    name: str,
    description: str,
    url: str,
    version: str,
    defaultInputModes: list[str],
    defaultOutputModes: list[str],
    capabilities: AgentCapabilities,
    skills: list[AgentSkill]
)
```

### 消息流模型
```python
MessageSendParams(
    message: Message(
        role: 'user',
        parts: list[Part],
        messageId: str,
        contextId: str,
        taskId: str
    ),
    configuration: MessageSendConfiguration
)
```

### 任务状态模型
```python
TaskState = Literal[
    'submitted', 'working', 'completed', 
    'failed', 'canceled', 'input_required'
]
```

## 错误处理

### 连接错误处理
- HTTP连接超时和重试机制
- Agent不可用时的优雅降级
- 网络异常的自动恢复

### 业务逻辑错误处理
- 无效输入参数验证
- API限流和配额管理
- 部分结果返回策略

### 系统级错误处理
```python
try:
    response = await client.send_message(request)
except httpx.HTTPStatusError as e:
    return handle_http_error(e)
except httpx.TimeoutException:
    return handle_timeout_error()
except Exception as e:
    return handle_generic_error(e)
```

## 测试策略

### 单元测试
- 各Agent的核心逻辑测试
- MCP工具功能测试
- 路由逻辑准确性测试

### 集成测试
- A2A协议通信测试
- 多Agent协作流程测试
- 端到端用户场景测试

### 性能测试
- 并发请求处理能力
- 响应时间基准测试
- 资源使用效率评估

### 测试用例示例
```python
# 情节规划测试
"帮我创建一个玄幻小说的故事大纲，主题是修仙成神，大约30万字"

# 角色设定测试  
"为我的玄幻小说创建一个主角，要求是天才少年，有特殊体质"

# 文本生成测试
"根据大纲和角色设定，生成第一章的内容，大约3000字"

# 复合创作测试
"我想写一部都市言情小说，帮我完成从大纲到角色再到第一章的完整创作"
```

## 部署配置

### 环境变量配置
```bash
# Host Agent配置
GOOGLE_GENAI_USE_VERTEXAI=TRUE
GOOGLE_CLOUD_PROJECT=your-project
GOOGLE_CLOUD_LOCATION=us-central1
PLOT_AGENT_URL=http://localhost:10001
CHARACTER_AGENT_URL=http://localhost:10002
CONTENT_AGENT_URL=http://localhost:10003

# Remote Agents配置
GOOGLE_API_KEY=your-api-key
GOOGLE_GENAI_MODEL=gemini-2.5-flash-preview-04-17

# 写作相关配置
DEFAULT_WRITING_STYLE=现代网文
MAX_CHAPTER_LENGTH=5000
STORY_TEMPLATES_PATH=./templates/story_structures
CHARACTER_TEMPLATES_PATH=./templates/character_archetypes
```

### 服务端口分配
- Plot Agent (情节规划): 端口 10001
- Character Agent (角色设定): 端口 10002
- Content Agent (文本生成): 端口 10003
- Host Agent: 端口 12000 (Web UI)

### 启动顺序
1. 启动Plot Agent (情节规划服务)
2. 启动Character Agent (角色设定服务)
3. 启动Content Agent (文本生成服务)
4. 启动Host Agent (会自动发现Remote Agents)
5. 启动Web UI (可选)

## 扩展性考虑

### 水平扩展
- 支持多个相同类型Agent实例的负载均衡
- Agent注册中心和服务发现机制
- 分布式会话状态管理

### 垂直扩展
- 新Agent类型的插件化集成
- 动态技能注册和发现
- 复杂工作流的编排支持

### 监控和可观测性
- A2A协议通信的链路追踪
- Agent性能指标收集
- 错误率和成功率监控