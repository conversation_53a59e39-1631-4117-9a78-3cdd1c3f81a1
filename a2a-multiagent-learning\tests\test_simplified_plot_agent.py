#!/usr/bin/env python3
"""
测试简化后的Plot Agent结构
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_simplified_structure():
    """测试简化后的Plot Agent结构"""
    print("🧪 测试简化后的Plot Agent结构...")
    print("=" * 50)
    
    try:
        # 测试导入
        print("📦 测试模块导入...")
        from plot_agent.agent import PlotAgent
        from plot_agent.agent_executor import PlotAgentExecutor
        print("✅ 模块导入成功")
        
        # 测试Plot Agent核心功能
        print("\n🎭 测试Plot Agent核心功能...")
        agent = PlotAgent()
        
        test_query = "帮我生成一个现代都市爱情故事的大纲"
        print(f"📝 测试查询: {test_query}")
        
        result_parts = []
        async for event in agent.stream(test_query):
            if event['content']:
                result_parts.append(event['content'])
            if event['done']:
                break
        
        result = ''.join(result_parts)
        print(f"✅ 获得响应 ({len(result)} 字符)")
        print(f"📄 响应预览: {result[:200]}...")
        
        # 测试AgentExecutor
        print("\n🔧 测试AgentExecutor...")
        executor = PlotAgentExecutor()
        print("✅ AgentExecutor创建成功")
        
        print("\n🎉 简化结构测试通过！")
        
        # 显示最终的简洁结构
        print("\n📁 简化后的目录结构:")
        print("plot_agent/")
        print("├── __init__.py")
        print("├── __main__.py          # 主入口（包含AgentCard和服务器启动）")
        print("├── agent_executor.py    # A2A执行器")
        print("├── agent.py            # Agent核心逻辑")
        print("├── plot_mcp.py         # MCP工具服务器")
        print("└── README.md           # 说明文档")
        
        print("\n🚀 启动命令:")
        print("   python -m plot_agent")
        print("   服务器地址: http://localhost:10002")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_simplified_structure())
    if not success:
        sys.exit(1)