#!/usr/bin/env python3
"""
Google ADK集成模块
使用Google ADK Agent包装WritingCoordinator，提供智能写作助手服务
"""

import asyncio
import logging
import json
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
    logger.info("✅ 成功加载.env文件")
except ImportError:
    logger.warning("python-dotenv未安装，无法加载.env文件")

try:
    from openai import AsyncOpenAI
    AI_CLIENT_AVAILABLE = True
except ImportError:
    AI_CLIENT_AVAILABLE = False
    logger.warning("OpenAI客户端未安装，请运行: pip install openai")

from .writing_coordinator import get_writing_coordinator, WritingCoordinator


class WritingAssistantAgent:
    """基于AI API的智能写作助手Agent"""
    
    def __init__(self):
        self.coordinator: Optional[WritingCoordinator] = None
        self.client: Optional[AsyncOpenAI] = None
        self.model_name = "gemini-2.5-pro-preview-06-05"
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self._initialize_ai_client()
    
    def _initialize_ai_client(self):
        """初始化AI客户端"""
        if not AI_CLIENT_AVAILABLE:
            logger.error("AI客户端不可用")
            return
        
        try:
            # 从环境变量获取API配置
            api_key = os.getenv('OPENAI_API_KEY')
            base_url = os.getenv('OPENAI_BASE_URL')
            self.model_name = os.getenv('OPENAI_MODEL', 'gemini-2.5-pro-preview-06-05')
            
            if not api_key:
                logger.error("未找到OPENAI_API_KEY环境变量")
                return
            
            # 初始化AI客户端
            self.client = AsyncOpenAI(
                api_key=api_key,
                base_url=base_url
            )
            
            logger.info(f"✅ AI客户端初始化成功，模型: {self.model_name}")
            
        except Exception as e:
            logger.error(f"❌ AI客户端初始化失败: {e}")
            self.client = None
    
    async def initialize(self):
        """初始化写作助手Agent"""
        logger.info("🚀 初始化写作助手Agent...")
        
        try:
            # 获取写作协调器
            self.coordinator = await get_writing_coordinator()
            
            # 注册写作工具
            self._register_writing_tools()
            
            logger.info("✅ 写作助手Agent初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 写作助手Agent初始化失败: {e}")
            return False
    
    def _register_writing_tools(self):
        """注册写作工具"""
        self.writing_tools = [
            {
                "name": "coordinate_writing",
                "description": "协调多个专业Agent完成写作任务",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "session_id": {
                            "type": "string",
                            "description": "写作会话ID"
                        },
                        "request": {
                            "type": "string", 
                            "description": "写作请求内容"
                        },
                        "context": {
                            "type": "object",
                            "description": "额外的上下文信息"
                        }
                    },
                    "required": ["session_id", "request"]
                }
            },
            {
                "name": "create_writing_session",
                "description": "创建新的写作会话",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "project_name": {
                            "type": "string",
                            "description": "项目名称"
                        },
                        "genre": {
                            "type": "string",
                            "description": "小说类型，如现代都市、古代言情、玄幻修仙等"
                        },
                        "description": {
                            "type": "string",
                            "description": "项目描述"
                        }
                    },
                    "required": ["project_name", "genre"]
                }
            },
            {
                "name": "get_session_status",
                "description": "获取写作会话状态",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "session_id": {
                            "type": "string",
                            "description": "写作会话ID"
                        }
                    },
                    "required": ["session_id"]
                }
            },
            {
                "name": "execute_writing_workflow",
                "description": "执行完整的写作工作流程",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "session_id": {
                            "type": "string",
                            "description": "写作会话ID"
                        },
                        "workflow_steps": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "工作流程步骤列表"
                        }
                    },
                    "required": ["session_id", "workflow_steps"]
                }
            }
        ]
        
        logger.info(f"📋 注册了 {len(self.writing_tools)} 个写作工具")
    
    async def before_model_callback(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """模型调用前的回调函数，管理写作会话生命周期"""
        try:
            # 提取用户消息
            user_message = ""
            if "messages" in request:
                for message in request["messages"]:
                    if message.get("role") == "user":
                        user_message = message.get("content", "")
                        break
            
            # 分析是否需要创建新会话
            session_id = self._extract_or_create_session(user_message)
            
            # 更新会话活动时间
            if session_id in self.active_sessions:
                self.active_sessions[session_id]["last_activity"] = datetime.now()
            
            # 添加会话上下文到请求
            request["session_context"] = {
                "session_id": session_id,
                "active_sessions": len(self.active_sessions),
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"📝 处理会话: {session_id}")
            return request
            
        except Exception as e:
            logger.error(f"❌ before_model_callback失败: {e}")
            return request
    
    def _extract_or_create_session(self, user_message: str) -> str:
        """提取或创建会话ID"""
        # 简单的会话管理逻辑
        # 在实际应用中，可以基于用户ID、项目名称等创建会话
        
        # 检查是否提到了特定项目
        if "新项目" in user_message or "新小说" in user_message:
            session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.active_sessions[session_id] = {
                "created_at": datetime.now(),
                "last_activity": datetime.now(),
                "project_name": "新写作项目",
                "status": "active"
            }
            return session_id
        
        # 如果没有活动会话，创建默认会话
        if not self.active_sessions:
            session_id = "default_session"
            self.active_sessions[session_id] = {
                "created_at": datetime.now(),
                "last_activity": datetime.now(),
                "project_name": "默认写作项目",
                "status": "active"
            }
            return session_id
        
        # 返回最近活动的会话
        latest_session = max(self.active_sessions.items(), 
                           key=lambda x: x[1]["last_activity"])
        return latest_session[0]
    
    async def coordinate_writing_tool(self, session_id: str, request: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """协调写作工具实现"""
        if not self.coordinator:
            return {"error": "写作协调器未初始化"}
        
        try:
            logger.info(f"🎯 协调写作任务: {session_id}")
            
            # 调用写作协调器
            result = await self.coordinator.coordinate_writing(
                session_id=session_id,
                request=request,
                context=context or {}
            )
            
            # 更新进度追踪
            self._update_progress_tracking(session_id, "coordinate_writing", result)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 协调写作失败: {e}")
            return {"error": str(e)}
    
    async def create_writing_session_tool(self, project_name: str, genre: str, description: str = "") -> Dict[str, Any]:
        """创建写作会话工具实现"""
        if not self.coordinator:
            return {"error": "写作协调器未初始化"}
        
        try:
            # 生成会话ID
            session_id = f"project_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 创建写作会话
            session = await self.coordinator.create_writing_session(
                session_id=session_id,
                project_name=project_name,
                genre=genre,
                context={"description": description}
            )
            
            # 添加到活动会话
            self.active_sessions[session_id] = {
                "created_at": datetime.now(),
                "last_activity": datetime.now(),
                "project_name": project_name,
                "genre": genre,
                "description": description,
                "status": "active"
            }
            
            logger.info(f"📝 创建写作会话: {session_id} ({project_name})")
            
            return {
                "success": True,
                "session_id": session_id,
                "project_name": project_name,
                "genre": genre,
                "message": f"成功创建写作项目：{project_name}"
            }
            
        except Exception as e:
            logger.error(f"❌ 创建写作会话失败: {e}")
            return {"error": str(e)}
    
    async def get_session_status_tool(self, session_id: str) -> Dict[str, Any]:
        """获取会话状态工具实现"""
        if not self.coordinator:
            return {"error": "写作协调器未初始化"}
        
        try:
            # 获取协调器中的会话状态
            status = await self.coordinator.get_session_status(session_id)
            
            # 合并本地会话信息
            if session_id in self.active_sessions:
                local_info = self.active_sessions[session_id]
                if status:
                    status.update({
                        "local_created_at": local_info["created_at"].isoformat(),
                        "local_last_activity": local_info["last_activity"].isoformat(),
                        "local_status": local_info["status"]
                    })
                else:
                    status = {
                        "session_id": session_id,
                        "project_name": local_info.get("project_name", "Unknown"),
                        "genre": local_info.get("genre", "Unknown"),
                        "local_status": local_info["status"],
                        "message": "会话存在但协调器中无详细信息"
                    }
            
            return status or {"error": f"会话 {session_id} 不存在"}
            
        except Exception as e:
            logger.error(f"❌ 获取会话状态失败: {e}")
            return {"error": str(e)}
    
    async def execute_writing_workflow_tool(self, session_id: str, workflow_steps: List[str]) -> Dict[str, Any]:
        """执行写作工作流程工具实现"""
        if not self.coordinator:
            return {"error": "写作协调器未初始化"}
        
        try:
            logger.info(f"🔄 执行写作工作流程: {session_id} ({len(workflow_steps)}个步骤)")
            
            # 调用写作协调器的工作流程方法
            results = await self.coordinator.writing_workflow(session_id, workflow_steps)
            
            # 更新进度追踪
            self._update_progress_tracking(session_id, "writing_workflow", {
                "total_steps": len(workflow_steps),
                "results": results
            })
            
            return {
                "success": True,
                "session_id": session_id,
                "total_steps": len(workflow_steps),
                "completed_steps": len([r for r in results if r.get("result", {}).get("success", False)]),
                "results": results
            }
            
        except Exception as e:
            logger.error(f"❌ 执行工作流程失败: {e}")
            return {"error": str(e)}
    
    def _update_progress_tracking(self, session_id: str, operation: str, result: Dict[str, Any]):
        """更新进度追踪"""
        try:
            if session_id not in self.active_sessions:
                return
            
            # 初始化进度追踪
            if "progress" not in self.active_sessions[session_id]:
                self.active_sessions[session_id]["progress"] = []
            
            # 添加进度记录
            progress_entry = {
                "timestamp": datetime.now().isoformat(),
                "operation": operation,
                "success": result.get("success", False),
                "details": {
                    "task_id": result.get("task_id"),
                    "task_type": result.get("task_type"),
                    "assigned_agent": result.get("assigned_agent")
                }
            }
            
            self.active_sessions[session_id]["progress"].append(progress_entry)
            
            # 保持最近50条记录
            if len(self.active_sessions[session_id]["progress"]) > 50:
                self.active_sessions[session_id]["progress"] = \
                    self.active_sessions[session_id]["progress"][-50:]
            
            logger.debug(f"📊 更新进度追踪: {session_id} - {operation}")
            
        except Exception as e:
            logger.error(f"❌ 更新进度追踪失败: {e}")
    
    async def process_user_request(self, user_message: str) -> str:
        """处理用户请求的主入口"""
        if not self.client:
            return "❌ AI客户端未初始化，请检查配置"
        
        try:
            # 构建系统提示
            system_prompt = self._build_system_prompt()
            
            # 构建工具定义
            tools = self._build_tool_definitions()
            
            # 调用AI模型
            response = await self._call_ai_with_tools(
                system_prompt=system_prompt,
                user_message=user_message,
                tools=tools
            )
            
            return response
            
        except Exception as e:
            logger.error(f"❌ 处理用户请求失败: {e}")
            return f"处理请求时出错: {str(e)}"
    
    def _build_system_prompt(self) -> str:
        """构建系统提示"""
        return """你是一个专业的网文写作助手，具备以下能力：

1. **智能写作协调**: 能够分析用户的写作需求，自动选择合适的专业Agent（情节规划、角色设定、内容生成）来完成任务。

2. **项目管理**: 可以创建和管理多个写作项目，跟踪写作进度和状态。

3. **工作流程管理**: 支持复杂的多步骤写作工作流程，从大纲设计到内容生成的完整流程。

4. **专业写作服务**: 
   - 情节规划：故事大纲、章节结构、冲突分析、转折建议
   - 角色设定：角色创建、关系网络、性格分析、成长弧线
   - 内容生成：场景描写、对话创作、章节写作、内容优化

使用原则：
- 理解用户的写作意图和需求
- 提供专业的写作建议和指导
- 协调多个专业Agent完成复杂任务
- 保持友好、专业的交流风格
- 主动询问必要的项目信息（如小说类型、主题等）

当用户提出写作需求时，你应该：
1. 分析需求类型
2. 选择合适的工具和Agent
3. 提供详细的执行结果
4. 给出后续建议"""
    
    def _build_tool_definitions(self) -> List[Dict[str, Any]]:
        """构建工具定义"""
        return self.writing_tools
    
    async def _call_ai_with_tools(self, system_prompt: str, user_message: str, tools: List[Dict[str, Any]]) -> str:
        """调用AI模型并处理工具调用"""
        try:
            # 构建消息
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ]
            
            # 调用AI模型进行意图理解和工具选择
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                temperature=0.7,
                max_tokens=1000
            )
            
            ai_response = response.choices[0].message.content
            
            # 基于AI响应进行工具调用
            tool_result = await self._analyze_and_execute_tools(user_message, ai_response)
            
            # 如果有工具执行结果，结合AI响应和工具结果
            if tool_result:
                final_response = f"{ai_response}\n\n📋 执行结果:\n{tool_result}"
            else:
                final_response = ai_response
            
            return final_response
            
        except Exception as e:
            logger.error(f"❌ 调用AI模型失败: {e}")
            # 回退到模拟工具调用
            return await self._simulate_tool_calling(user_message)
    
    async def _analyze_and_execute_tools(self, user_message: str, ai_response: str) -> Optional[str]:
        """分析AI响应并执行相应的工具"""
        try:
            # 基于用户消息和AI响应分析需要执行的工具
            user_lower = user_message.lower()
            ai_lower = ai_response.lower()
            
            # 检查是否需要创建项目
            if any(keyword in user_lower for keyword in ["创建", "新建", "开始"]) and \
               any(keyword in user_lower for keyword in ["项目", "小说", "故事"]):
                
                # 从用户消息中提取项目信息
                project_name = "新写作项目"
                genre = "现代都市"
                
                # 简单的类型识别
                if "古代" in user_message or "古风" in user_message:
                    genre = "古代言情"
                elif "玄幻" in user_message or "修仙" in user_message:
                    genre = "玄幻修仙"
                elif "科幻" in user_message:
                    genre = "科幻"
                elif "悬疑" in user_message or "推理" in user_message:
                    genre = "悬疑推理"
                
                result = await self.create_writing_session_tool(
                    project_name=project_name,
                    genre=genre,
                    description=f"基于用户请求创建: {user_message}"
                )
                
                if result.get("success"):
                    return f"✅ 项目创建成功: {result['project_name']} ({result['genre']})\n会话ID: {result['session_id']}"
                else:
                    return f"❌ 项目创建失败: {result.get('error')}"
            
            # 检查是否需要查询状态
            elif any(keyword in user_lower for keyword in ["状态", "进度", "查看", "显示"]):
                session_id = list(self.active_sessions.keys())[0] if self.active_sessions else "default_session"
                result = await self.get_session_status_tool(session_id)
                
                if "error" not in result:
                    return f"📊 项目状态: {result.get('project_name', 'Unknown')}\n类型: {result.get('genre', 'Unknown')}\n任务数: {result.get('total_tasks', 0)}"
                else:
                    return f"❌ 获取状态失败: {result['error']}"
            
            # 检查是否需要执行写作任务
            elif any(keyword in user_lower for keyword in ["写", "生成", "创作", "设计", "帮我"]):
                session_id = list(self.active_sessions.keys())[0] if self.active_sessions else "default_session"
                result = await self.coordinate_writing_tool(session_id, user_message)
                
                if result.get("success"):
                    return f"✅ 写作任务完成!\n类型: {result.get('task_type')}\nAgent: {result.get('assigned_agent')}"
                else:
                    return f"❌ 写作任务失败: {result.get('error')}"
            
            # 没有匹配的工具
            return None
            
        except Exception as e:
            logger.error(f"❌ 工具分析执行失败: {e}")
            return f"工具执行失败: {str(e)}"
    
    async def _simulate_tool_calling(self, user_message: str) -> str:
        """模拟工具调用逻辑（实际实现中应该使用真实的Google ADK API）"""
        try:
            # 简单的意图识别
            if "创建" in user_message and ("项目" in user_message or "小说" in user_message):
                # 创建写作会话
                result = await self.create_writing_session_tool(
                    project_name="新写作项目",
                    genre="现代都市",
                    description="基于用户请求创建的项目"
                )
                return f"✅ {result.get('message', '项目创建成功')}\n会话ID: {result.get('session_id')}"
            
            elif "状态" in user_message or "进度" in user_message:
                # 获取会话状态
                session_id = list(self.active_sessions.keys())[0] if self.active_sessions else "default_session"
                result = await self.get_session_status_tool(session_id)
                return f"📊 会话状态:\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            
            else:
                # 协调写作任务
                session_id = list(self.active_sessions.keys())[0] if self.active_sessions else "default_session"
                result = await self.coordinate_writing_tool(session_id, user_message)
                
                if result.get("success"):
                    return f"✅ 写作任务完成!\n任务类型: {result.get('task_type')}\n执行Agent: {result.get('assigned_agent')}\n\n结果预览: {str(result.get('result', {}))[:200]}..."
                else:
                    return f"❌ 写作任务失败: {result.get('error')}"
            
        except Exception as e:
            logger.error(f"❌ 模拟工具调用失败: {e}")
            return f"处理请求失败: {str(e)}"
    
    async def get_progress_report(self, session_id: str) -> Dict[str, Any]:
        """获取写作进度报告"""
        try:
            if session_id not in self.active_sessions:
                return {"error": f"会话 {session_id} 不存在"}
            
            session_info = self.active_sessions[session_id]
            progress_entries = session_info.get("progress", [])
            
            # 统计信息
            total_operations = len(progress_entries)
            successful_operations = len([p for p in progress_entries if p.get("success", False)])
            
            # 最近活动
            recent_activities = progress_entries[-10:] if progress_entries else []
            
            # 构建报告
            report = {
                "session_id": session_id,
                "project_name": session_info.get("project_name"),
                "genre": session_info.get("genre"),
                "created_at": session_info["created_at"].isoformat(),
                "last_activity": session_info["last_activity"].isoformat(),
                "status": session_info["status"],
                "statistics": {
                    "total_operations": total_operations,
                    "successful_operations": successful_operations,
                    "success_rate": (successful_operations / total_operations * 100) if total_operations > 0 else 0
                },
                "recent_activities": recent_activities
            }
            
            return report
            
        except Exception as e:
            logger.error(f"❌ 获取进度报告失败: {e}")
            return {"error": str(e)}


# 全局写作助手实例
_writing_assistant: Optional[WritingAssistantAgent] = None


async def get_writing_assistant() -> WritingAssistantAgent:
    """获取全局写作助手实例"""
    global _writing_assistant
    
    if _writing_assistant is None:
        _writing_assistant = WritingAssistantAgent()
        await _writing_assistant.initialize()
    
    return _writing_assistant


async def shutdown_writing_assistant():
    """关闭全局写作助手"""
    global _writing_assistant
    
    if _writing_assistant:
        # 清理资源
        _writing_assistant.active_sessions.clear()
        _writing_assistant = None

# A2A执行器实现
try:
    from typing import override
except ImportError:
    from typing_extensions import override

from a2a.server.agent_execution import AgentExecutor, RequestContext
from a2a.server.events import EventQueue
from a2a.types import (
    TaskArtifactUpdateEvent,
    TaskState,
    TaskStatus,
    TaskStatusUpdateEvent,
)
from a2a.utils import new_text_artifact


class HostAgentExecutor(AgentExecutor):
    """Host Agent A2A执行器"""
    
    def __init__(self):
        self.writing_assistant: Optional[WritingAssistantAgent] = None
        self.active_tasks: Dict[str, Dict[str, Any]] = {}
    
    @override
    async def execute(
        self,
        context: RequestContext,
        event_queue: EventQueue,
    ) -> None:
        """执行Host Agent任务"""
        query = context.get_user_input()
        if not context.message:
            raise Exception('No message provided')
        
        try:
            # 初始化写作助手
            if not self.writing_assistant:
                self.writing_assistant = await get_writing_assistant()
            
            # 记录任务信息
            task_id = context.task_id if context.task_id else "unknown_task"
            self.active_tasks[task_id] = {
                "context_id": context.context_id,
                "status": "working",
                "query": query,
                "start_time": asyncio.get_event_loop().time(),
                "events": []
            }
            
            # 发送开始状态
            status = TaskStatusUpdateEvent(
                contextId=context.context_id, # type: ignore
                taskId=task_id, # type: ignore
                status=TaskStatus(state=TaskState.working),
                final=False
            )
            await event_queue.enqueue_event(status)
            
            # 处理用户请求
            response = await self.writing_assistant.process_user_request(query)
            
            # 发送结果
            message = TaskArtifactUpdateEvent(
                contextId=context.context_id, # type: ignore
                taskId=task_id, # type: ignore
                artifact=new_text_artifact(
                    name='writing_assistant_response',
                    text=response,
                ),
            )
            await event_queue.enqueue_event(message)
            
            # 发送完成状态
            status = TaskStatusUpdateEvent(
                contextId=context.context_id, # type: ignore
                taskId=task_id, # type: ignore
                status=TaskStatus(state=TaskState.completed),
                final=True
            )
            await event_queue.enqueue_event(status)
            
            # 更新任务状态
            if task_id in self.active_tasks:
                self.active_tasks[task_id]["status"] = "completed"
                self.active_tasks[task_id]["end_time"] = asyncio.get_event_loop().time()
            
        except Exception as e:
            # 发送错误状态
            error_message = TaskArtifactUpdateEvent(
                contextId=context.context_id, # type: ignore
                taskId=context.task_id, # type: ignore
                artifact=new_text_artifact(
                    name='error',
                    text=f'处理请求时出错: {str(e)}',
                ),
            )
            await event_queue.enqueue_event(error_message)
            
            status = TaskStatusUpdateEvent(
                contextId=context.context_id, # type: ignore
                taskId=context.task_id, # type: ignore
                status=TaskStatus(state=TaskState.failed),
                final=True
            )
            await event_queue.enqueue_event(status)
            
            # 更新任务状态
            if task_id in self.active_tasks:
                self.active_tasks[task_id]["status"] = "failed"
                self.active_tasks[task_id]["error"] = str(e)
                self.active_tasks[task_id]["end_time"] = asyncio.get_event_loop().time()
    
    @override
    async def cancel(
        self, context: RequestContext, event_queue: EventQueue
    ) -> None:
        """取消任务"""
        task_id = context.task_id if context.task_id else "unknown_task"
        
        if task_id in self.active_tasks and self.active_tasks[task_id]["status"] == "working":
            # 更新任务状态
            self.active_tasks[task_id]["status"] = "cancelled"
            self.active_tasks[task_id]["end_time"] = asyncio.get_event_loop().time()
            
            # 发送取消状态
            status = TaskStatusUpdateEvent(
                contextId=context.context_id, # type: ignore
                taskId=task_id, # type: ignore
                status=TaskStatus(state=TaskState.cancelled),
                final=True
            )
            await event_queue.enqueue_event(status)
            
            # 发送取消消息
            message = TaskArtifactUpdateEvent(
                contextId=context.context_id, # type: ignore
                taskId=task_id, # type: ignore
                artifact=new_text_artifact(
                    name='cancel_message',
                    text='任务已取消',
                ),
            )
            await event_queue.enqueue_event(message)
        else:
            # 任务不存在或已完成，无法取消
            message = TaskArtifactUpdateEvent(
                contextId=context.context_id, # type: ignore
                taskId=task_id, # type: ignore
                artifact=new_text_artifact(
                    name='cancel_error',
                    text='任务不存在或已完成，无法取消',
                ),
            )
            await event_queue.enqueue_event(message)