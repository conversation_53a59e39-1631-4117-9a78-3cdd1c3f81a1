#!/usr/bin/env python3
"""
Plot Agent A2A服务器启动脚本
"""

import sys
import os
import subprocess
from pathlib import Path

def main():
    """启动Plot Agent A2A服务器"""
    print("🎭 Plot Agent A2A服务器启动器")
    print("=" * 50)
    
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    
    # 检查依赖
    print("📦 检查依赖...")
    try:
        import a2a
        print("✅ A2A SDK已安装")
    except ImportError:
        print("❌ A2A SDK未安装")
        print("💡 请运行: pip install a2a-sdk")
        return False
    
    try:
        import fastmcp
        print("✅ FastMCP已安装")
    except ImportError:
        print("❌ FastMCP未安装")
        print("💡 请运行: pip install fastmcp")
        return False
    
    # 启动服务器
    print("\n🚀 启动Plot Agent A2A服务器...")
    server_script = project_root / "plot_agent" / "__main_a2a__.py"
    
    try:
        # 使用subprocess启动服务器
        subprocess.run([sys.executable, str(server_script)], cwd=str(project_root))
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动服务器时出错: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)