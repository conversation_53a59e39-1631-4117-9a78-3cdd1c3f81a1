#!/usr/bin/env python3
"""
测试编码问题的简单脚本
"""

import sys
import os

# 设置环境变量
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 测试Unicode输出
try:
    print("测试Unicode字符:")
    print("✅ 成功")
    print("⚠️ 警告")
    print("❌ 错误")
    print("🔧 配置")
    print("📊 统计")
    print("🔑 密钥")
    print("编码测试完成")
except UnicodeEncodeError as e:
    print(f"Unicode编码错误: {e}")
    print("使用ASCII替代字符:")
    print("[OK] 成功")
    print("[WARN] 警告")
    print("[ERROR] 错误")
    print("[CONFIG] 配置")
    print("[STATS] 统计")
    print("[KEY] 密钥")