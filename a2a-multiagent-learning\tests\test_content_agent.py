#!/usr/bin/env python3
"""
测试Content Agent的内容生成功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_content_agent():
    """测试Content Agent功能"""
    print("📝 测试Content Agent内容生成功能...")
    print("=" * 60)
    
    try:
        # 测试导入
        print("📦 测试模块导入...")
        from content_agent.agent import ContentAgent, WRITING_STYLE_TEMPLATES
        print("✅ 模块导入成功")
        
        # 检查写作风格模板
        print("\n📋 检查写作风格模板...")
        if WRITING_STYLE_TEMPLATES:
            template_count = len(WRITING_STYLE_TEMPLATES)
            print(f"✅ 已加载写作风格模板: {template_count}个")
            for name in WRITING_STYLE_TEMPLATES.keys():
                print(f"   - {name}")
        else:
            print("❌ 写作风格模板加载失败")
            return False
        
        # 创建Content Agent实例
        print("\n📝 创建Content Agent实例...")
        agent = ContentAgent()
        print(f"✅ Agent名称: {agent.name}")
        
        # 测试1: 场景描写生成
        print("\n" + "="*60)
        print("🎯 测试1: 场景描写生成")
        print("="*60)
        
        test_query1 = "生成一个现代都市咖啡厅的场景描写，营造浪漫氛围"
        print(f"📝 测试查询: {test_query1}")
        print("\n📄 响应:")
        print("-" * 40)
        
        result_parts1 = []
        async for event in agent.stream(test_query1):
            if event['content']:
                print(event['content'], end='', flush=True)
                result_parts1.append(event['content'])
            if event['done']:
                break
        
        result1 = ''.join(result_parts1)
        print(f"\n\n✅ 响应完成 ({len(result1)} 字符)")
        
        # 测试2: 对话生成
        print("\n" + "="*60)
        print("🎯 测试2: 对话生成")
        print("="*60)
        
        test_query2 = "生成古代言情小说中男女主角的对话，表现初次相遇的情景"
        print(f"📝 测试查询: {test_query2}")
        print("\n📄 响应:")
        print("-" * 40)
        
        result_parts2 = []
        async for event in agent.stream(test_query2):
            if event['content']:
                print(event['content'], end='', flush=True)
                result_parts2.append(event['content'])
            if event['done']:
                break
        
        result2 = ''.join(result_parts2)
        print(f"\n\n✅ 响应完成 ({len(result2)} 字符)")
        
        # 测试3: 写作模板查询
        print("\n" + "="*60)
        print("🎯 测试3: 写作模板查询")
        print("="*60)
        
        test_query3 = "显示可用的写作风格模板"
        print(f"📝 测试查询: {test_query3}")
        print("\n📄 响应:")
        print("-" * 40)
        
        result_parts3 = []
        async for event in agent.stream(test_query3):
            if event['content']:
                print(event['content'], end='', flush=True)
                result_parts3.append(event['content'])
            if event['done']:
                break
        
        result3 = ''.join(result_parts3)
        print(f"\n\n✅ 响应完成 ({len(result3)} 字符)")
        
        print("\n🎉 Content Agent内容生成功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_content_agent())
    if not success:
        sys.exit(1)