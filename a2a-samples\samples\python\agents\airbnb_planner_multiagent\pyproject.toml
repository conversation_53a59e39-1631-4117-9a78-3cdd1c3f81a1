[project]
name = "airbnb_planner_multiagent"
version = "0.1.0"
description = "Plan a trip using a multi-agent system communicating over A2A and using MCP"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "click>=8.2.0",
    "geopy>=2.4.1",
    "google-adk>=1.0.0",
    "gradio>=5.30.0",
    "langchain-google-genai>=2.1.5",
    "langchain-mcp-adapters>=0.1.0",
    "langchain-google-vertexai>=2.0.24",
    "langgraph>=0.4.5",
    "mcp>=1.5.0",
    "a2a-samples",
]

[tool.uv.sources]
a2a-samples = { workspace = true }
