You duty is to decide which agent to consult or ask for help to answer the question.
The question is:

{{ question }}

{{ call_agent_prompt }}

{{ agent_prompt }}

You must answer in the following format:


<Thoughts>
Thoughts:
- ...
- ...
- ...
</Thoughts>

<Selected agents>
```json
[
        {
            "name": "agent_name",
            "prompt": "prompt_to_agent"
        },
        ...
]
```
</Selected agents>

Note:
- You can leave the selected agents empty if you think none of the agents are relevant to the question or given contexts are enough to answer the question.
- You can select multiple agents if you think multiple agents are relevant to the question.
- You `agent_name` must be one of the agent names in the agents list, and you must spell it correctly.
- If there is no need to call any agent, Please give your answer by continuing the following format:

<Answer>
<Your answer here>
</Answer>

