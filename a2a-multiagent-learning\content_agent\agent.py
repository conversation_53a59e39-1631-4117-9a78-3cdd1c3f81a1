#!/usr/bin/env python3
"""
Content Agent核心逻辑
集成AI API和写作风格模板，提供智能内容生成服务
"""

import os
import sys
import json
import re
import asyncio
from collections.abc import AsyncGenerator
from typing import Any, Dict, List
from pathlib import Path

# 导入AI API
try:
    from openai import AsyncOpenAI
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False
    print("⚠️ OpenAI库未安装，将使用基础模式")

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("⚠️ python-dotenv库未安装")

# 导入内容生成数据模型
from .content_models import (
    ContentType, WritingStyle, ContentRequirement,
    SceneRequirement, DialogueRequirement, ChapterRequirement,
    GeneratedContent, ContentGenerationResponse
)

# 加载写作风格模板
def load_writing_style_templates():
    """加载写作风格模板"""
    try:
        template_path = Path(__file__).parent.parent / 'templates' / 'writing_styles'
        templates = {}
        
        if template_path.exists():
            for file in template_path.glob('*.md'):
                style_name = file.stem
                with open(file, 'r', encoding='utf-8') as f:
                    templates[style_name] = f.read()
        
        return templates
        
    except Exception as e:
        print(f"⚠️ 加载写作风格模板失败: {e}")
        return {}

# 全局写作风格模板
WRITING_STYLE_TEMPLATES = load_writing_style_templates()


class ContentAgent:
    """Content Agent核心类 - 集成AI API和写作风格模板的智能内容生成"""

    def __init__(self):
        """初始化Content Agent"""
        self.name = "Content Agent"
        self.description = "专业的网文内容生成Agent，集成AI智能分析和写作风格模板"
        
        # 初始化AI客户端
        self._init_ai_client()
        
        # 加载写作风格模板
        self.writing_templates = WRITING_STYLE_TEMPLATES
        if self.writing_templates:
            template_count = len(self.writing_templates)
            print(f"✅ 已加载写作风格模板: {template_count}个")
            for name in self.writing_templates.keys():
                print(f"   - {name}")
        else:
            print("⚠️ 写作风格模板加载失败")
        
        # 技能映射
        self.skills_map = {
            'write_scene': self._handle_write_scene,
            'write_dialogue': self._handle_write_dialogue,
            'write_chapter': self._handle_write_chapter,
            'refine_content': self._handle_refine_content,
            'get_writing_templates': self._handle_writing_templates,
            'analyze_content': self._handle_analyze_content,
        }

    def _init_ai_client(self):
        """初始化AI客户端"""
        self.ai_client = None
        
        if not AI_AVAILABLE:
            print("⚠️ AI功能不可用，将使用基础模式")
            return
            
        try:
            # 从环境变量获取配置
            api_key = os.getenv('OPENAI_API_KEY')
            base_url = os.getenv('OPENAI_BASE_URL')
            self.model = os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo')
            
            if not api_key:
                print("⚠️ 未找到OPENAI_API_KEY，将使用基础模式")
                return
            
            # 创建AI客户端
            self.ai_client = AsyncOpenAI(
                api_key=api_key,
                base_url=base_url
            )
            print(f"✅ Content Agent AI客户端初始化成功，模型: {self.model}")
            
        except Exception as e:
            print(f"⚠️ Content Agent AI客户端初始化失败: {e}，将使用基础模式")
            self.ai_client = None

    async def stream(self, query: str) -> AsyncGenerator[dict[str, Any], None]:
        """流式处理用户查询"""
        try:
            # 分析用户意图并选择合适的处理方法
            skill_id, params = self._analyze_intent(query)
            
            if skill_id in self.skills_map:
                # 调用对应的技能处理函数
                async for chunk in self.skills_map[skill_id](params):
                    yield chunk
            else:
                # 通用内容生成建议
                async for chunk in self._handle_general_content_advice(query):
                    yield chunk
                    
        except Exception as e:
            yield {
                'content': f'处理请求时出错: {str(e)}',
                'done': True,
            }

    def _analyze_intent(self, query: str) -> tuple[str, dict]:
        """分析用户意图，返回技能ID和参数"""
        query_lower = query.lower()
        
        # 场景描写
        if any(keyword in query_lower for keyword in ['场景', '环境', '描写', '场面']):
            params = self._extract_scene_params(query)
            return 'write_scene', params
            
        # 对话生成
        elif any(keyword in query_lower for keyword in ['对话', '对白', '谈话', '交流']):
            params = self._extract_dialogue_params(query)
            return 'write_dialogue', params
            
        # 章节生成
        elif any(keyword in query_lower for keyword in ['章节', '一章', '写章', '章']):
            params = self._extract_chapter_params(query)
            return 'write_chapter', params
            
        # 内容优化
        elif any(keyword in query_lower for keyword in ['优化', '润色', '修改', '完善']):
            params = self._extract_refine_params(query)
            return 'refine_content', params
            
        # 模板查询
        elif any(keyword in query_lower for keyword in ['模板', '风格', '样式', '参考']):
            params = self._extract_template_params(query)
            return 'get_writing_templates', params
            
        # 内容分析
        elif any(keyword in query_lower for keyword in ['分析', '评价', '检查', '评估']):
            params = self._extract_analysis_params(query)
            return 'analyze_content', params
            
        else:
            # 默认为场景描写
            params = self._extract_scene_params(query)
            return 'write_scene', params

    def _extract_scene_params(self, query: str) -> dict:
        """从查询中提取场景参数"""
        params = {
            'location': '',
            'time': '',
            'atmosphere': '',
            'characters': [],
            'writing_style': 'modern_urban',
            'word_count': 300,
            'query': query
        }
        
        # 提取写作风格
        style_patterns = {
            'modern_urban': ['现代', '都市', '城市'],
            'ancient_romance': ['古代', '古风', '古典'],
            'fantasy_cultivation': ['玄幻', '修仙', '仙侠'],
        }
        
        for style, keywords in style_patterns.items():
            if any(keyword in query for keyword in keywords):
                params['writing_style'] = style
                break
        
        return params

    def _extract_dialogue_params(self, query: str) -> dict:
        """从查询中提取对话参数"""
        params = {
            'characters': [],
            'purpose': '',
            'tone': '',
            'writing_style': 'modern_urban',
            'query': query
        }
        
        # 提取写作风格
        style_patterns = {
            'modern_urban': ['现代', '都市', '城市'],
            'ancient_romance': ['古代', '古风', '古典'],
            'fantasy_cultivation': ['玄幻', '修仙', '仙侠'],
        }
        
        for style, keywords in style_patterns.items():
            if any(keyword in query for keyword in keywords):
                params['writing_style'] = style
                break
        
        return params

    def _extract_chapter_params(self, query: str) -> dict:
        """从查询中提取章节参数"""
        params = {
            'plot_summary': '',
            'key_scenes': [],
            'writing_style': 'modern_urban',
            'word_count': 2000,
            'query': query
        }
        
        # 提取写作风格
        style_patterns = {
            'modern_urban': ['现代', '都市', '城市'],
            'ancient_romance': ['古代', '古风', '古典'],
            'fantasy_cultivation': ['玄幻', '修仙', '仙侠'],
        }
        
        for style, keywords in style_patterns.items():
            if any(keyword in query for keyword in keywords):
                params['writing_style'] = style
                break
        
        return params

    def _extract_refine_params(self, query: str) -> dict:
        """从查询中提取优化参数"""
        return {
            'content': '',
            'improvement_focus': '全面优化',
            'query': query
        }

    def _extract_template_params(self, query: str) -> dict:
        """从查询中提取模板参数"""
        return {
            'style_type': '',
            'query': query
        }

    def _extract_analysis_params(self, query: str) -> dict:
        """从查询中提取分析参数"""
        return {
            'content': '',
            'analysis_focus': '全面分析',
            'query': query
        }

    async def _handle_write_scene(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """处理场景描写生成"""
        yield {'content': '🎬 正在生成场景描写...\n\n', 'done': False}
        
        try:
            if self.ai_client:
                async for chunk in self._ai_enhanced_scene_writing(params):
                    yield chunk
            else:
                result = await self._basic_scene_writing(params)
                chunks = self._split_content(result)
                for chunk in chunks:
                    yield {'content': chunk, 'done': False}
                
        except Exception as e:
            yield {'content': f'生成场景描写时出错: {str(e)}', 'done': False}
        
        yield {'content': '', 'done': True}

    async def _ai_enhanced_scene_writing(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """AI增强的场景描写生成"""
        try:
            # 获取相关的写作风格模板
            style_template = ""
            writing_style = params.get('writing_style', 'modern_urban')
            if writing_style in self.writing_templates:
                style_template = f"\n\n【写作风格参考】\n{self.writing_templates[writing_style]}"
            
            # 构建AI提示词
            prompt = f"""作为专业的网文内容创作专家，请根据以下要求生成精彩的场景描写：

用户需求：{params.get('query', '')}
写作风格：{writing_style}
字数要求：{params.get('word_count', 300)}字左右

{style_template}

请创建一个包含以下要素的场景描写：
1. 环境描述 - 详细描绘场景的视觉效果
2. 氛围营造 - 通过环境烘托情感氛围
3. 感官细节 - 调动读者的五感体验
4. 情感渲染 - 将环境与人物情感相结合

要求：
- 语言生动形象，富有画面感
- 符合指定的写作风格特点
- 注重细节描写和氛围营造
- 避免过于冗长的描述
- 为后续情节发展做好铺垫"""

            # 调用AI API
            response = await self.ai_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一位专业的网文内容创作专家，擅长创作生动精彩的场景描写。你熟悉各种写作风格，能够根据不同需求创作出符合风格特点的优质内容。"},
                    {"role": "user", "content": prompt}
                ],
                stream=True,
                temperature=0.8
            )
            
            yield {'content': '🤖 AI正在结合写作风格模板生成场景描写...\n\n', 'done': False}
            
            # 流式返回AI响应
            async for chunk in response:
                if chunk.choices[0].delta.content:
                    yield {'content': chunk.choices[0].delta.content, 'done': False}
                    
        except Exception as e:
            yield {'content': f'AI增强生成失败，使用基础模式: {str(e)}\n\n', 'done': False}
            
            # 回退到基础模式
            result = await self._basic_scene_writing(params)
            chunks = self._split_content(result)
            for chunk in chunks:
                yield {'content': chunk, 'done': False}

    async def _basic_scene_writing(self, params: dict) -> str:
        """基础场景描写生成（无AI时使用）"""
        writing_style = params.get('writing_style', 'modern_urban')
        
        result = f"## 场景描写\n\n"
        result += f"**写作风格**: {writing_style}\n"
        result += f"**用户需求**: {params.get('query', '无')}\n\n"
        
        if writing_style in self.writing_templates:
            result += f"### 参考风格模板\n\n"
            # 提取模板的关键部分
            template = self.writing_templates[writing_style]
            lines = template.split('\n')
            for line in lines[:20]:  # 取前20行作为参考
                if line.strip():
                    result += f"{line}\n"
            result += f"\n请参考以上风格特点进行场景描写创作。"
        
        return result

    async def _handle_write_dialogue(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """处理对话生成"""
        yield {'content': '💬 正在生成对话内容...\n\n', 'done': False}
        
        try:
            if self.ai_client:
                async for chunk in self._ai_enhanced_dialogue_writing(params):
                    yield chunk
            else:
                result = await self._basic_dialogue_writing(params)
                chunks = self._split_content(result)
                for chunk in chunks:
                    yield {'content': chunk, 'done': False}
                
        except Exception as e:
            yield {'content': f'生成对话内容时出错: {str(e)}', 'done': False}
        
        yield {'content': '', 'done': True}

    async def _ai_enhanced_dialogue_writing(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """AI增强的对话生成"""
        try:
            # 获取相关的写作风格模板
            style_template = ""
            writing_style = params.get('writing_style', 'modern_urban')
            if writing_style in self.writing_templates:
                style_template = f"\n\n【写作风格参考】\n{self.writing_templates[writing_style]}"
            
            # 构建AI提示词
            prompt = f"""作为专业的网文内容创作专家，请根据以下要求生成精彩的对话内容：

用户需求：{params.get('query', '')}
写作风格：{writing_style}

{style_template}

请创建包含以下要素的对话内容：
1. 角色对话 - 符合角色性格特点的自然对话
2. 对话标签 - 适当的动作和神态描写
3. 情感表达 - 通过对话展现角色情感
4. 情节推进 - 对话要为故事发展服务

要求：
- 对话自然流畅，符合角色身份
- 语言风格与写作类型匹配
- 包含适当的动作和心理描写
- 避免过于冗长的独白
- 通过对话展现角色关系和冲突"""

            # 调用AI API
            response = await self.ai_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一位专业的网文内容创作专家，擅长创作生动自然的对话内容。你能够根据不同角色特点和写作风格，创作出符合人物性格的精彩对话。"},
                    {"role": "user", "content": prompt}
                ],
                stream=True,
                temperature=0.8
            )
            
            yield {'content': '🤖 AI正在结合写作风格模板生成对话内容...\n\n', 'done': False}
            
            # 流式返回AI响应
            async for chunk in response:
                if chunk.choices[0].delta.content:
                    yield {'content': chunk.choices[0].delta.content, 'done': False}
                    
        except Exception as e:
            yield {'content': f'AI增强生成失败，使用基础模式: {str(e)}\n\n', 'done': False}
            
            # 回退到基础模式
            result = await self._basic_dialogue_writing(params)
            chunks = self._split_content(result)
            for chunk in chunks:
                yield {'content': chunk, 'done': False}

    async def _basic_dialogue_writing(self, params: dict) -> str:
        """基础对话生成（无AI时使用）"""
        writing_style = params.get('writing_style', 'modern_urban')
        
        result = f"## 对话内容\n\n"
        result += f"**写作风格**: {writing_style}\n"
        result += f"**用户需求**: {params.get('query', '无')}\n\n"
        
        if writing_style in self.writing_templates:
            result += f"### 对话风格参考\n\n"
            # 提取模板中的对话部分
            template = self.writing_templates[writing_style]
            if "对话风格" in template:
                lines = template.split('\n')
                in_dialogue_section = False
                for line in lines:
                    if "对话风格" in line:
                        in_dialogue_section = True
                    elif in_dialogue_section and line.startswith('#'):
                        break
                    elif in_dialogue_section and line.strip():
                        result += f"{line}\n"
            result += f"\n请参考以上对话风格进行创作。"
        
        return result

    async def _handle_write_chapter(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """处理章节生成"""
        yield {'content': '📖 正在生成章节内容...\n\n', 'done': False}
        
        try:
            if self.ai_client:
                async for chunk in self._ai_enhanced_chapter_writing(params):
                    yield chunk
            else:
                result = await self._basic_chapter_writing(params)
                chunks = self._split_content(result)
                for chunk in chunks:
                    yield {'content': chunk, 'done': False}
                
        except Exception as e:
            yield {'content': f'生成章节内容时出错: {str(e)}', 'done': False}
        
        yield {'content': '', 'done': True}

    async def _ai_enhanced_chapter_writing(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """AI增强的章节生成"""
        try:
            # 获取相关的写作风格模板
            style_template = ""
            writing_style = params.get('writing_style', 'modern_urban')
            if writing_style in self.writing_templates:
                style_template = f"\n\n【写作风格参考】\n{self.writing_templates[writing_style]}"
            
            # 构建AI提示词
            prompt = f"""作为专业的网文内容创作专家，请根据以下要求生成完整的章节内容：

用户需求：{params.get('query', '')}
写作风格：{writing_style}
字数要求：{params.get('word_count', 2000)}字左右

{style_template}

请创建包含以下要素的章节内容：
1. 章节开头 - 吸引读者的精彩开场
2. 情节发展 - 合理的故事推进
3. 场景描写 - 生动的环境和氛围
4. 人物对话 - 符合角色特点的对话
5. 情感渲染 - 恰当的情感表达
6. 章节结尾 - 留有悬念的结尾

要求：
- 结构完整，层次分明
- 语言风格统一，符合类型特点
- 情节紧凑，节奏适中
- 人物形象鲜明，对话自然
- 适当设置悬念和冲突
- 为下一章节做好铺垫"""

            # 调用AI API
            response = await self.ai_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一位专业的网文内容创作专家，擅长创作结构完整、情节精彩的章节内容。你熟悉各种网文类型的写作技巧和风格特点。"},
                    {"role": "user", "content": prompt}
                ],
                stream=True,
                temperature=0.8
            )
            
            yield {'content': '🤖 AI正在结合写作风格模板生成章节内容...\n\n', 'done': False}
            
            # 流式返回AI响应
            async for chunk in response:
                if chunk.choices[0].delta.content:
                    yield {'content': chunk.choices[0].delta.content, 'done': False}
                    
        except Exception as e:
            yield {'content': f'AI增强生成失败，使用基础模式: {str(e)}\n\n', 'done': False}
            
            # 回退到基础模式
            result = await self._basic_chapter_writing(params)
            chunks = self._split_content(result)
            for chunk in chunks:
                yield {'content': chunk, 'done': False}

    async def _basic_chapter_writing(self, params: dict) -> str:
        """基础章节生成（无AI时使用）"""
        writing_style = params.get('writing_style', 'modern_urban')
        
        result = f"## 章节内容\n\n"
        result += f"**写作风格**: {writing_style}\n"
        result += f"**用户需求**: {params.get('query', '无')}\n"
        result += f"**字数要求**: {params.get('word_count', 2000)}字\n\n"
        
        if writing_style in self.writing_templates:
            result += f"### 写作风格指导\n\n"
            template = self.writing_templates[writing_style]
            result += f"请参考以下风格特点进行章节创作：\n\n"
            # 提取模板的关键技巧部分
            if "写作技巧" in template:
                lines = template.split('\n')
                in_tips_section = False
                for line in lines:
                    if "写作技巧" in line:
                        in_tips_section = True
                    elif in_tips_section and line.startswith('#'):
                        break
                    elif in_tips_section and line.strip():
                        result += f"{line}\n"
        
        return result

    async def _handle_refine_content(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """处理内容优化"""
        yield {'content': '✨ 正在优化内容...\n\n', 'done': False}
        
        # 内容优化功能开发中
        yield {'content': '内容优化功能开发中...', 'done': True}

    async def _handle_writing_templates(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """处理写作模板查询"""
        yield {'content': '📝 正在获取写作风格模板...\n\n', 'done': False}
        
        try:
            result = "## 写作风格模板\n\n"
            
            if self.writing_templates:
                result += f"### 可用的写作风格 ({len(self.writing_templates)}个)\n\n"
                for name, template in self.writing_templates.items():
                    result += f"#### {name}\n"
                    # 提取模板的简介部分
                    lines = template.split('\n')
                    for line in lines[:10]:  # 取前10行作为简介
                        if line.strip() and not line.startswith('#'):
                            result += f"{line}\n"
                    result += "\n---\n\n"
            else:
                result += "暂无可用的写作风格模板。"
            
            chunks = self._split_content(result)
            for chunk in chunks:
                yield {'content': chunk, 'done': False}
                
        except Exception as e:
            yield {'content': f'获取写作模板时出错: {str(e)}', 'done': False}
        
        yield {'content': '', 'done': True}

    async def _handle_analyze_content(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """处理内容分析"""
        yield {'content': '🔍 正在分析内容...\n\n', 'done': False}
        
        # 内容分析功能开发中
        yield {'content': '内容分析功能开发中...', 'done': True}

    async def _handle_general_content_advice(self, query: str) -> AsyncGenerator[dict[str, Any], None]:
        """处理通用内容建议"""
        yield {'content': f'💭 收到查询: {query}\n\n通用内容生成建议功能开发中...', 'done': True}

    def _split_content(self, content: str, chunk_size: int = 200) -> List[str]:
        """将长内容分割成小块"""
        if len(content) <= chunk_size:
            return [content]
        
        chunks = []
        for i in range(0, len(content), chunk_size):
            chunks.append(content[i:i + chunk_size])
        return chunks