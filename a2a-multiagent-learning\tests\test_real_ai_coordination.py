#!/usr/bin/env python3
"""
测试写作协调器与真实AI API的集成
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_real_ai_coordination():
    """测试与真实AI API的写作协调"""
    print("🤖 测试写作协调器与真实AI API集成...")
    print("=" * 60)
    
    try:
        from host_agent.writing_coordinator import (
            get_writing_coordinator,
            shutdown_writing_coordinator
        )
        
        # 获取写作协调器
        print("🚀 初始化写作协调器...")
        coordinator = await get_writing_coordinator()
        
        # 等待Agent连接稳定
        print("⏳ 等待Agent连接稳定...")
        await asyncio.sleep(5)
        
        # 检查Agent连接状态
        print("\n📊 检查Agent连接状态...")
        workload = await coordinator.get_agent_workload()
        
        connected_agents = []
        for agent_id, load in workload.items():
            status_icon = "✅" if load['status'] == 'connected' else "❌"
            print(f"{status_icon} {agent_id}: {load['status']}")
            if load['status'] == 'connected':
                connected_agents.append(agent_id)
        
        if not connected_agents:
            print("❌ 没有Agent连接成功")
            print("💡 请确保至少启动一个Agent服务器:")
            print("   python -m plot_agent")
            return False
        
        print(f"✅ 发现 {len(connected_agents)} 个可用Agent: {connected_agents}")
        
        # 测试真实AI协调任务
        print(f"\n🎯 测试真实AI协调任务...")
        
        test_cases = [
            {
                "request": "帮我设计一个现代都市爱情故事的大纲，男主是程序员，女主是咖啡店老板",
                "description": "情节规划任务"
            },
            {
                "request": "为这个爱情故事创建一个内向但才华横溢的程序员男主角",
                "description": "角色创建任务"
            },
            {
                "request": "写一个程序员第一次走进咖啡店遇见女主的场景",
                "description": "内容生成任务"
            }
        ]
        
        session_id = "real_ai_test_session"
        successful_tests = 0
        
        for i, test_case in enumerate(test_cases, 1):
            request = test_case["request"]
            description = test_case["description"]
            
            print(f"\n📝 测试 {i}: {description}")
            print(f"🎯 请求: {request}")
            
            try:
                # 分析任务类型
                task_type = coordinator.analyze_writing_request(request)
                print(f"📋 任务类型: {task_type.value}")
                
                # 选择Agent
                selected_agent = coordinator.select_best_agent(task_type, connected_agents)
                print(f"🤖 选择Agent: {selected_agent}")
                
                if selected_agent not in connected_agents:
                    print(f"⚠️ 所需Agent {selected_agent} 不可用，跳过测试")
                    continue
                
                # 执行协调任务
                print("⏳ 正在执行任务...")
                start_time = time.time()
                
                result = await coordinator.coordinate_writing(
                    session_id=session_id,
                    request=request,
                    context={
                        "test_case": i,
                        "description": description,
                        "timestamp": start_time
                    }
                )
                
                end_time = time.time()
                duration = end_time - start_time
                
                # 分析结果
                if result.get("success", False):
                    print(f"✅ 任务执行成功 (耗时: {duration:.2f}秒)")
                    print(f"📋 任务ID: {result['task_id']}")
                    print(f"🤖 执行Agent: {result['assigned_agent']}")
                    
                    # 显示AI生成的结果
                    ai_result = result.get('result', {})
                    if ai_result:
                        print(f"🎨 AI生成结果:")
                        if isinstance(ai_result, dict):
                            # 如果是字典，尝试提取主要内容
                            content = ai_result.get('content', ai_result.get('text', str(ai_result)))
                        else:
                            content = str(ai_result)
                        
                        # 显示前300个字符
                        preview = content[:300] + "..." if len(content) > 300 else content
                        print(f"   {preview}")
                        
                        # 分析结果质量
                        if len(content) > 50:
                            print(f"✅ 内容长度: {len(content)}字符 (质量良好)")
                        else:
                            print(f"⚠️ 内容长度: {len(content)}字符 (可能需要优化)")
                    
                    successful_tests += 1
                    
                else:
                    print(f"❌ 任务执行失败: {result.get('error', 'Unknown error')}")
                
            except Exception as e:
                print(f"❌ 任务执行异常: {e}")
                import traceback
                traceback.print_exc()
            
            # 任务间延迟
            if i < len(test_cases):
                print("⏳ 等待下一个任务...")
                await asyncio.sleep(2)
        
        # 测试会话状态
        print(f"\n📊 检查会话状态...")
        session_status = await coordinator.get_session_status(session_id)
        if session_status:
            print(f"✅ 会话统计:")
            print(f"   总任务: {session_status['total_tasks']}")
            print(f"   已完成: {session_status['completed_tasks']}")
            print(f"   失败: {session_status['failed_tasks']}")
            
            # 显示任务详情
            if session_status.get('tasks'):
                print(f"📋 任务详情:")
                for task in session_status['tasks']:
                    status_icon = "✅" if task['status'] == 'completed' else "❌"
                    print(f"   {status_icon} {task['task_id']}: {task['task_type']} ({task['status']})")
        
        # 清理资源
        print(f"\n🧹 清理测试资源...")
        await shutdown_writing_coordinator()
        
        # 测试总结
        print(f"\n🎉 真实AI协调测试完成!")
        print(f"📈 成功率: {successful_tests}/{len(test_cases)} ({successful_tests/len(test_cases)*100:.1f}%)")
        
        if successful_tests > 0:
            print("✅ 写作协调器与真实AI API集成成功")
            print("✅ 智能路由和任务执行正常")
            print("✅ AI生成内容质量良好")
        else:
            print("❌ 没有成功的测试案例")
        
        return successful_tests > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ai_quality_analysis():
    """测试AI生成内容的质量分析"""
    print("\n🔍 测试AI生成内容质量分析...")
    print("=" * 40)
    
    try:
        from host_agent.writing_coordinator import WritingCoordinator
        
        coordinator = WritingCoordinator()
        
        # 模拟AI生成的不同质量内容
        test_contents = [
            {
                "content": "这是一个关于程序员和咖啡店老板的爱情故事。男主角李明是一个内向的程序员，每天都会路过女主角小雅开的咖啡店。有一天，李明终于鼓起勇气走进了咖啡店，点了一杯拿铁。小雅注意到了这个经常路过但从未进店的男孩，主动和他聊天。从此，两人开始了一段美好的恋情。",
                "expected_quality": "良好"
            },
            {
                "content": "程序员遇见咖啡店老板。",
                "expected_quality": "较差"
            },
            {
                "content": "在这个快节奏的都市里，李明是一名资深的软件工程师，专注于人工智能算法的研究。他的生活规律而单调，每天早上8点准时经过街角的那家名为'时光咖啡'的小店。店主小雅是一个充满活力的女孩，她用心经营着这家温馨的咖啡店，每一杯咖啡都承载着她对生活的热爱。命运的转折点出现在一个雨天的下午，李明因为躲雨而第一次推开了咖啡店的门...",
                "expected_quality": "优秀"
            }
        ]
        
        print("📊 内容质量分析结果:")
        
        for i, test in enumerate(test_contents, 1):
            content = test["content"]
            expected = test["expected_quality"]
            
            # 简单的质量评估指标
            length = len(content)
            word_count = len(content.split())
            has_dialogue = "\"" in content or """ in content
            has_description = any(word in content for word in ["描写", "场景", "环境", "外貌"])
            has_emotion = any(word in content for word in ["爱", "喜欢", "感动", "温暖", "心动"])
            
            # 计算质量分数
            quality_score = 0
            if length > 100:
                quality_score += 2
            elif length > 50:
                quality_score += 1
            
            if word_count > 20:
                quality_score += 1
            
            if has_dialogue:
                quality_score += 1
            
            if has_description:
                quality_score += 1
                
            if has_emotion:
                quality_score += 1
            
            # 质量等级
            if quality_score >= 5:
                quality_level = "优秀"
            elif quality_score >= 3:
                quality_level = "良好"
            elif quality_score >= 1:
                quality_level = "一般"
            else:
                quality_level = "较差"
            
            print(f"\n内容 {i}:")
            print(f"   长度: {length}字符")
            print(f"   词数: {word_count}词")
            print(f"   包含对话: {'是' if has_dialogue else '否'}")
            print(f"   包含描写: {'是' if has_description else '否'}")
            print(f"   包含情感: {'是' if has_emotion else '否'}")
            print(f"   质量分数: {quality_score}/6")
            print(f"   质量等级: {quality_level} (预期: {expected})")
            
            # 内容预览
            preview = content[:100] + "..." if len(content) > 100 else content
            print(f"   内容预览: {preview}")
        
        print("\n✅ AI内容质量分析测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 质量分析测试失败: {e}")
        return False

if __name__ == "__main__":
    async def main():
        print("🚀 开始真实AI协调测试...")
        print("📋 确保已启动Agent服务器并配置了AI API")
        print()
        
        success1 = await test_real_ai_coordination()
        success2 = await test_ai_quality_analysis()
        
        if success1 and success2:
            print("\n🎉 所有真实AI测试通过!")
            print("\n💡 测试总结:")
            print("   ✅ 写作协调器与AI API集成正常")
            print("   ✅ 智能路由和任务分析准确")
            print("   ✅ AI生成内容质量良好")
            print("   ✅ 任务执行流程完整")
        else:
            print("\n⚠️ 部分测试未通过")
            if not success1:
                print("   - 请检查Agent服务器状态和AI API配置")
            if not success2:
                print("   - 请检查内容质量分析逻辑")
    
    asyncio.run(main())