#!/usr/bin/env python3
"""
使用真实AI API测试Character Agent A2A服务器
"""

import asyncio
import sys
import time
import subprocess
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class CharacterAgentServerManager:
    """Character Agent服务器管理器"""
    
    def __init__(self):
        self.process = None
        self.server_ready = False
    
    def start_server(self):
        """在后台启动服务器"""
        try:
            print("[START] 启动Character Agent A2A服务器...")
            self.process = subprocess.Popen(
                [sys.executable, "-m", "character_agent"],
                cwd=project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 监控服务器输出
            def monitor_output():
                for line in iter(self.process.stdout.readline, ''):
                    print(f"[SERVER] {line.strip()}")
                    if "启动Character Agent A2A服务器" in line or "Application startup complete" in line:
                        self.server_ready = True
            
            threading.Thread(target=monitor_output, daemon=True).start()
            
            # 等待服务器启动
            for i in range(30):  # 最多等待30秒
                if self.server_ready:
                    break
                time.sleep(1)
                print(f"[WAIT] 等待服务器启动... ({i+1}/30)")

            if self.server_ready:
                print("[OK] Character Agent服务器启动成功")
                return True
            else:
                print("[TIMEOUT] 服务器启动超时")
                return False

        except Exception as e:
            print(f"[ERROR] 启动服务器失败: {e}")
            return False

    def stop_server(self):
        """停止服务器"""
        if self.process:
            print("[STOP] 停止Character Agent服务器...")
            self.process.terminate()
            self.process.wait()
            print("[OK] 服务器已停止")

async def test_character_agent_with_real_api():
    """使用真实AI API测试Character Agent"""
    print("[TEST] 使用真实AI API测试Character Agent A2A服务器")
    print("=" * 70)

    # 直接测试Agent功能（不启动HTTP服务器）
    try:
        print("[IMPORT] 导入Character Agent...")
        from character_agent.agent import CharacterAgent

        agent = CharacterAgent()
        print(f"[OK] Agent初始化成功: {agent.name}")

        # 测试用例1: 角色原型查询
        print("\n" + "="*70)
        print("[TEST-1] 测试1: 角色原型查询")
        print("="*70)

        test_query1 = "显示女性角色原型"
        print(f"[QUERY] 查询: {test_query1}")
        print("\n[RESPONSE] AI响应:")
        print("-" * 50)
        
        start_time = time.time()
        result_parts1 = []
        
        async for event in agent.stream(test_query1):
            if event['content']:
                print(event['content'], end='', flush=True)
                result_parts1.append(event['content'])
            if event['done']:
                break
        
        end_time = time.time()
        result1 = ''.join(result_parts1)
        print(f"\n\n✅ 测试1完成 - 响应长度: {len(result1)} 字符, 耗时: {end_time-start_time:.2f}秒")
        
        # 测试用例2: 创建复杂角色
        print("\n" + "="*70)
        print("🎯 测试2: 创建复杂角色")
        print("="*70)
        
        test_query2 = "创建一个古代玄幻小说的女主角，要求有修仙背景，性格坚韧但内心温柔，有复杂的身世"
        print(f"📝 查询: {test_query2}")
        print("\n📄 AI响应:")
        print("-" * 50)
        
        start_time = time.time()
        result_parts2 = []
        
        async for event in agent.stream(test_query2):
            if event['content']:
                print(event['content'], end='', flush=True)
                result_parts2.append(event['content'])
            if event['done']:
                break
        
        end_time = time.time()
        result2 = ''.join(result_parts2)
        print(f"\n\n✅ 测试2完成 - 响应长度: {len(result2)} 字符, 耗时: {end_time-start_time:.2f}秒")
        
        # 测试用例3: 角色原型推荐
        print("\n" + "="*70)
        print("🎯 测试3: 角色原型推荐")
        print("="*70)
        
        test_query3 = "推荐适合悬疑推理小说的男主角原型，要求智慧型角色"
        print(f"📝 查询: {test_query3}")
        print("\n📄 AI响应:")
        print("-" * 50)
        
        start_time = time.time()
        result_parts3 = []
        
        async for event in agent.stream(test_query3):
            if event['content']:
                print(event['content'], end='', flush=True)
                result_parts3.append(event['content'])
            if event['done']:
                break
        
        end_time = time.time()
        result3 = ''.join(result_parts3)
        print(f"\n\n✅ 测试3完成 - 响应长度: {len(result3)} 字符, 耗时: {end_time-start_time:.2f}秒")
        
        # 测试总结
        print("\n" + "="*70)
        print("📊 测试总结")
        print("="*70)
        print(f"✅ 测试1 (角色原型查询): {len(result1)} 字符")
        print(f"✅ 测试2 (创建复杂角色): {len(result2)} 字符") 
        print(f"✅ 测试3 (原型推荐): {len(result3)} 字符")
        print(f"✅ AI API调用: 正常工作")
        print(f"✅ 角色原型模板: 正确集成")
        print(f"✅ 流式响应: 功能正常")
        
        print("\n🎉 Character Agent真实AI API测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_character_agent_with_real_api())
    if not success:
        sys.exit(1)