#!/usr/bin/env python3
"""
启动单个Agent的测试脚本，避免Unicode编码问题
"""

import sys
import os
import asyncio
import uvicorn
from pathlib import Path

# 设置环境变量以避免编码问题
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def start_plot_agent():
    """启动Plot Agent"""
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        from a2a.server.apps import A2AStarletteApplication
        from a2a.server.request_handlers import DefaultRequestHandler
        from a2a.server.tasks import InMemoryTaskStore
        from a2a.types import (
            AgentCapabilities,
            AgentCard,
            AgentSkill,
        )
        from plot_agent.agent_executor import PlotAgentExecutor
        
        # 创建Agent卡片
        agent_card = AgentCard(
            name='Plot Agent - 情节规划专家',
            description='专业的网文情节规划Agent',
            url='http://localhost:10002/',
            version='1.0.0',
            defaultInputModes=['text'],
            defaultOutputModes=['text'],
            capabilities=AgentCapabilities(streaming=True),
            skills=[
                AgentSkill(
                    id='generate_story_outline',
                    name='生成故事大纲',
                    description='根据题材、主题等要求生成完整的故事大纲',
                    tags=['故事大纲', '情节规划', '创作'],
                    examples=['帮我生成一个现代都市爱情故事的大纲'],
                )
            ],
        )

        # 创建请求处理器
        request_handler = DefaultRequestHandler(
            agent_executor=PlotAgentExecutor(),
            task_store=InMemoryTaskStore(),
        )

        # 创建A2A服务器应用
        server = A2AStarletteApplication(
            agent_card=agent_card,
            http_handler=request_handler
        )

        # 启动服务器
        print("启动Plot Agent A2A服务器...")
        print("服务器地址: http://localhost:10002")
        
        uvicorn.run(server.build(), host='localhost', port=10002)
        
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    start_plot_agent()