# Azure AI Foundry Configuration
# Copy this file to .env and fill in your actual values

# Required: Azure AI Foundry Project Endpoint
# Example: https://your-project.cognitiveservices.azure.com/
AZURE_AI_FOUNDRY_PROJECT_ENDPOINT=Your Azure AI Foundry Project Endpoint

# Required: Azure AI Agent Model Deployment Name  
AZURE_AI_AGENT_MODEL_DEPLOYMENT_NAME=Your Azure AI Foundry Agent Model Deployment Name


# A2A Server Configuration
A2A_HOST=localhost
A2A_PORT=10007

# Logging Level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO
