# 真实AI API测试指南

## 概述

本指南将帮助您使用真实的AI API测试A2A多Agent写作助手系统，替代之前的硬编码模拟数据。

## 🚀 快速开始

### 1. 环境准备

#### 安装依赖
```bash
pip install aiohttp openai python-dotenv
```

#### 配置API密钥
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，添加您的API密钥
# OPENAI_API_KEY=your_api_key_here
# OPENAI_BASE_URL=https://api.openai.com/v1  # 可选，自定义API端点
```

### 2. 一键测试（推荐）

使用自动化测试脚本：

```bash
# 运行完整的真实AI测试套件
python scripts/run_real_ai_tests.py
```

这个脚本会自动：
- ✅ 检查环境配置
- 🚀 启动所有Agent服务器
- ⏳ 等待服务器就绪
- 🧪 运行真实AI API测试
- 🧹 自动清理资源

### 3. 手动测试

如果您想手动控制测试过程：

#### 步骤1: 启动Agent服务器
```bash
# 启动所有Agent (推荐使用统一启动器)
python scripts/start_all_agents.py

# 或者分别启动每个Agent
python -m plot_agent.__main__ --port 10002
python -m character_agent.__main__ --port 10003  
python -m content_agent.__main__ --port 10004
python -m host_agent.__main__ --port 10001
```

#### 步骤2: 运行真实AI测试
```bash
# 运行真实AI集成测试
python tests/test_real_ai_integration.py

# 运行端到端写作系统测试
python scripts/test_writing_system.py

# 运行具体测试用例
python tests/test_cases.py
```

## 🧪 测试类型

### 1. 真实AI集成测试 (`test_real_ai_integration.py`)

**测试内容:**
- Plot Agent AI功能测试
- Character Agent AI功能测试  
- Content Agent AI功能测试
- Host Agent协调功能测试
- 端到端写作流程测试
- AI API错误处理测试
- 性能压力测试

**示例输出:**
```
🤖 A2A多Agent系统真实AI API测试
============================================================
✅ plot_agent: Plot Agent - 情节规划专家 (5个技能)
✅ character_agent: Character Agent - 角色设定专家 (6个技能)
✅ content_agent: Content Agent - 内容创作专家 (6个技能)
✅ host_agent: Host Agent - 智能写作助手 (5个技能)

🧪 执行测试: Plot Agent AI测试
----------------------------------------
✅ 通过 (15.23s)
📝 Plot Agent AI功能正常，生成了完整的故事大纲
🤖 AI响应预览: 根据您的要求，我为您设计了一个现代都市爱情故事的大纲...
```

### 2. 端到端写作系统测试 (`test_writing_system.py`)

**测试内容:**
- 情节规划功能测试
- 角色设定功能测试
- 内容生成功能测试
- 复合写作任务测试
- 错误场景处理测试
- Agent连接性测试
- 配置系统测试

### 3. 具体测试用例 (`test_cases.py`)

**测试内容:**
- 3个情节规划测试用例
- 3个角色设定测试用例
- 3个内容生成测试用例
- 2个复合写作测试用例
- 3个错误场景测试用例

## 🔧 配置选项

### API配置

在`.env`文件中配置：

```bash
# OpenAI API配置
OPENAI_API_KEY=your_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# 特定Agent的模型配置（可选）
PLOT_AGENT_MODEL=gpt-4
CHARACTER_AGENT_MODEL=gpt-3.5-turbo
CONTENT_AGENT_MODEL=gpt-4
```

### Agent端口配置

默认端口分配：
- Plot Agent: 10002
- Character Agent: 10003
- Content Agent: 10004
- Host Agent: 10001

可以通过命令行参数修改：
```bash
python -m plot_agent.__main__ --port 12002
```

## 📊 测试结果解读

### 成功指标

**AI功能测试:**
- ✅ 响应包含相关关键词（故事、角色、场景等）
- ✅ 响应长度充足（通常>200字符）
- ✅ 无错误状态
- ✅ AI生成内容质量良好

**系统集成测试:**
- ✅ Agent服务器正常启动
- ✅ HTTP请求响应正常
- ✅ 多Agent协作成功
- ✅ 错误处理机制有效

### 常见问题

**1. API密钥问题**
```
⚠️ 未找到OPENAI_API_KEY环境变量
```
**解决方案:** 检查`.env`文件中的API密钥配置

**2. Agent服务器未启动**
```
❌ Plot Agent不可用: Connection refused
```
**解决方案:** 确保Agent服务器正在运行

**3. 超时问题**
```
❌ AI测试执行失败: Timeout
```
**解决方案:** 增加超时时间或检查网络连接

## 🆚 对比：模拟数据 vs 真实AI

### 之前（模拟数据）
```python
# 硬编码的模拟响应
return {
    "success": True,
    "response": {
        "story_outline": {
            "title": "都市爱情物语",  # 固定内容
            "genre": "现代都市爱情",
            # ... 更多硬编码数据
        }
    }
}
```

**问题:**
- ❌ 内容固定，无法测试AI的真实能力
- ❌ 无法验证不同输入的响应差异
- ❌ 无法测试AI API的错误处理
- ❌ 无法评估实际性能和质量

### 现在（真实AI API）
```python
# 真实的Agent调用
async with session.post(
    "http://localhost:10002/execute",
    json={"message": request, ...},
    timeout=aiohttp.ClientTimeout(total=60)
) as response:
    result = await response.json()
    return {"success": True, "response": result}
```

**优势:**
- ✅ 测试真实的AI生成能力
- ✅ 验证不同输入的响应变化
- ✅ 测试实际的错误处理机制
- ✅ 评估真实的性能和质量
- ✅ 发现实际部署中的问题

## 📈 性能基准

### 响应时间基准
- Plot Agent: 10-30秒（复杂故事大纲）
- Character Agent: 8-25秒（详细角色档案）
- Content Agent: 15-40秒（场景描写/对话）
- Host Agent: 30-120秒（多Agent协调）

### 质量评估标准
- **内容长度**: >200字符（基础内容）
- **关键词匹配**: 包含相关领域关键词
- **结构完整性**: 包含必要的数据字段
- **逻辑一致性**: 内容逻辑合理，符合要求

## 🔍 调试技巧

### 1. 查看详细日志
```bash
# 启动Agent时启用详细日志
python -m plot_agent.__main__ --log-level DEBUG
```

### 2. 单独测试Agent
```bash
# 测试单个Agent的功能
curl -X POST http://localhost:10002/execute \
  -H "Content-Type: application/json" \
  -d '{"message": "设计一个科幻故事大纲"}'
```

### 3. 检查测试报告
测试完成后会生成详细报告：
- `tests/real_ai_test_report.json` - 真实AI测试报告
- `tests/test_report.json` - 系统测试报告

## 🚀 最佳实践

### 1. 测试前准备
- ✅ 确保网络连接稳定
- ✅ 验证API密钥有效且有足够额度
- ✅ 关闭不必要的程序释放系统资源

### 2. 测试执行
- ✅ 使用自动化脚本减少手动操作
- ✅ 监控测试过程，及时发现问题
- ✅ 保存测试结果用于后续分析

### 3. 结果分析
- ✅ 关注成功率和响应时间
- ✅ 分析失败用例的原因
- ✅ 评估AI生成内容的质量

## 📞 支持

如果您在使用过程中遇到问题：

1. **检查环境配置** - 确保所有依赖和配置正确
2. **查看日志输出** - 分析错误信息和调试信息
3. **参考测试报告** - 查看详细的测试结果和统计
4. **逐步调试** - 从单个Agent开始，逐步扩展到完整系统

---

**注意:** 使用真实AI API会产生费用，请合理控制测试频率和规模。建议在开发和调试阶段使用，生产环境部署前进行充分测试。