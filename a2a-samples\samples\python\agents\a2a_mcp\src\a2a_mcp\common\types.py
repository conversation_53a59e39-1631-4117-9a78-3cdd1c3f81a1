# type: ignore

from typing import Any, Literal

from pydantic import BaseModel, Field, model_validator


class ServerConfig(BaseModel):
    """Server Confgiguration."""

    host: str
    port: int
    transport: str
    url: str


class PlannerTask(BaseModel):
    """Represents a single task generated by the Planner."""

    id: int = Field(description='Sequential ID for the task.')
    description: str = Field(
        description='Clear description of the task to be executed.'
    )
    status: (
        Any
        | Literal[
            'input_required',
            'completed',
            'error',
            'pending',
            'incomplete',
            'todo',
            'not_started',
        ]
        | None
    ) = Field(description='Status of the task', default='input_required')


class TripInfo(BaseModel):
    """Trip Info."""

    total_budget: str | None = Field(description='Total Budget for the trip')
    origin: str | None = Field(description='Trip Origin')
    destination: str | None = Field(description='Trip destination')
    type: str | None = Field(description='Trip type, business or leisure')
    start_date: str | None = Field(description='Trip Start Date')
    end_date: str | None = Field(description='Trip End Date')
    travel_class: str | None = Field(
        description='Travel class, first, business or economy'
    )
    accomodation_type: str | None = Field(
        description='Luxury Hotel, Budget Hotel, AirBnB, etc'
    )
    room_type: str | None = Field(description='Suite, Single, Double etc.')
    is_car_rental_required: str | None = Field(
        description='Whether a rental car is required in the trip.'
    )
    type_of_car: str | None = Field(
        description='Type of the car, SUV, Sedan, Truck etc.'
    )
    no_of_travellers: str | None = Field(
        description='Total number of travellers in the trip'
    )

    checkin_date: str | None = Field(description='Hotel Checkin Date')
    checkout_date: str | None = Field(description='Hotel Checkout Date')
    car_rental_start_date: str | None = Field(
        description='Car Rental Start Date'
    )
    car_rental_end_date: str | None = Field(description='Car Rental End Date')

    @model_validator(mode='before')
    @classmethod
    def set_dependent_var(cls, values):
        """Pydantic dependent setters."""
        if isinstance(values, dict) and 'start_date' in values:
            values['checkin_date'] = values['start_date']

        if isinstance(values, dict) and 'end_date' in values:
            values['checkout_date'] = values['end_date']

        if isinstance(values, dict) and 'start_date' in values:
            values['car_rental_start_date'] = values['start_date']

        if isinstance(values, dict) and 'end_date' in values:
            values['car_rental_end_date'] = values['end_date']
        return values


class TaskList(BaseModel):
    """Output schema for the Planner Agent."""

    original_query: str | None = Field(
        description='The original user query for context.'
    )

    trip_info: TripInfo | None = Field(description='Trip information')

    tasks: list[PlannerTask] = Field(
        description='A list of tasks to be executed sequentially.'
    )


class AgentResponse(BaseModel):
    """Output schema for the Agent."""

    content: str | dict = Field(description='The content of the response.')
    is_task_complete: bool = Field(description='Whether the task is complete.')
    require_user_input: bool = Field(
        description='Whether the agent requires user input.'
    )
