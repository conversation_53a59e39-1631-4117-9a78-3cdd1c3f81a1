#!/usr/bin/env python3
"""
简单的Agent启动测试，避免Unicode编码问题
"""

import sys
import os
from pathlib import Path

# 设置UTF-8编码
if sys.platform == 'win32':
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from dotenv import load_dotenv
    load_dotenv()
    
    # 测试环境变量
    api_key = os.getenv('OPENAI_API_KEY')
    if api_key:
        print(f"API密钥已配置: {api_key[:20]}...")
    else:
        print("未找到API密钥")
    
    # 测试导入Plot Agent
    from plot_agent.agent import PlotAgent
    print("成功导入Plot Agent")
    
    # 测试初始化
    agent = PlotAgent()
    print("Plot Agent初始化成功")
    
    if agent.ai_client:
        print("AI客户端可用")
    else:
        print("AI客户端不可用")
        
except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()