#!/usr/bin/env python3
"""
A2A多Agent写作助手启动器
统一管理所有Agent的启动和配置
"""

import sys
import os
import argparse
import subprocess
import time
import signal
from pathlib import Path
from typing import List, Dict, Optional
import threading
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.config_manager import get_config_manager, AgentConfig

class AgentLauncher:
    """Agent启动器"""
    
    def __init__(self):
        self.config_manager = get_config_manager()
        self.processes: Dict[str, subprocess.Popen] = {}
        self.running = False
        
    def start_agent(self, agent_name: str, config: AgentConfig) -> bool:
        """启动单个Agent"""
        if not config.enabled:
            print(f"⏸️ {agent_name} 已禁用，跳过启动")
            return True
            
        print(f"🚀 启动 {agent_name}...")
        
        try:
            # 构建启动命令
            cmd = [
                sys.executable, 
                "-m", 
                f"{agent_name}.__main__"
            ]
            
            # 添加命令行参数
            if config.host != "localhost":
                cmd.extend(["--host", config.host])
            if config.port != 10000:
                cmd.extend(["--port", str(config.port)])
            if config.writing_mode != "ai":
                cmd.extend(["--writing-mode", config.writing_mode])
            
            # 设置环境变量
            env = os.environ.copy()
            if config.api_key:
                env['GOOGLE_API_KEY'] = config.api_key
            env['AGENT_NAME'] = agent_name
            env['AGENT_PORT'] = str(config.port)
            env['WRITING_MODE'] = config.writing_mode
            
            # 启动进程
            process = subprocess.Popen(
                cmd,
                cwd=project_root,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes[agent_name] = process
            print(f"✅ {agent_name} 启动成功 (PID: {process.pid}, 端口: {config.port})")
            
            # 等待一下确保启动成功
            time.sleep(1)
            if process.poll() is not None:
                stdout, stderr = process.communicate()
                print(f"❌ {agent_name} 启动失败:")
                if stdout:
                    print(f"STDOUT: {stdout}")
                if stderr:
                    print(f"STDERR: {stderr}")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ 启动 {agent_name} 时出错: {e}")
            return False
    
    def stop_agent(self, agent_name: str):
        """停止单个Agent"""
        if agent_name in self.processes:
            process = self.processes[agent_name]
            print(f"🛑 停止 {agent_name}...")
            
            try:
                # 尝试优雅关闭
                process.terminate()
                process.wait(timeout=5)
                print(f"✅ {agent_name} 已停止")
            except subprocess.TimeoutExpired:
                # 强制关闭
                process.kill()
                process.wait()
                print(f"⚡ {agent_name} 已强制停止")
            except Exception as e:
                print(f"❌ 停止 {agent_name} 时出错: {e}")
            
            del self.processes[agent_name]
    
    def start_all_agents(self, agent_names: Optional[List[str]] = None) -> bool:
        """启动所有或指定的Agent"""
        if agent_names is None:
            agent_names = self.config_manager.get_enabled_agents()
        
        print("🏠 A2A多Agent写作助手启动器")
        print("=" * 50)
        
        # 验证配置
        errors = self.config_manager.validate_config()
        if errors:
            print("⚠️ 配置验证失败:")
            for error in errors:
                print(f"  - {error}")
            return False
        
        success_count = 0
        
        # 按顺序启动Agent (Host Agent最后启动)
        ordered_agents = [name for name in agent_names if name != 'host_agent']
        if 'host_agent' in agent_names:
            ordered_agents.append('host_agent')
        
        for agent_name in ordered_agents:
            config = self.config_manager.get_agent_config(agent_name)
            if config and self.start_agent(agent_name, config):
                success_count += 1
                time.sleep(2)  # 给每个Agent一些启动时间
        
        print(f"\n📊 启动结果: {success_count}/{len(ordered_agents)} 个Agent启动成功")
        
        if success_count > 0:
            self.running = True
            print("\n🎉 系统启动完成!")
            print("📋 运行中的Agent:")
            for agent_name in self.processes:
                config = self.config_manager.get_agent_config(agent_name)
                print(f"  - {agent_name}: http://{config.host}:{config.port}")
            
            return True
        else:
            print("\n❌ 没有Agent启动成功")
            return False
    
    def stop_all_agents(self):
        """停止所有Agent"""
        print("\n🛑 停止所有Agent...")
        
        # 先停止Host Agent
        if 'host_agent' in self.processes:
            self.stop_agent('host_agent')
        
        # 再停止其他Agent
        for agent_name in list(self.processes.keys()):
            self.stop_agent(agent_name)
        
        self.running = False
        print("✅ 所有Agent已停止")
    
    def monitor_agents(self):
        """监控Agent状态"""
        while self.running:
            time.sleep(5)
            
            failed_agents = []
            for agent_name, process in list(self.processes.items()):
                if process.poll() is not None:
                    failed_agents.append(agent_name)
            
            if failed_agents:
                print(f"\n⚠️ 检测到Agent异常退出: {', '.join(failed_agents)}")
                for agent_name in failed_agents:
                    del self.processes[agent_name]
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n📡 收到信号 {signum}，正在关闭系统...")
        self.stop_all_agents()
        sys.exit(0)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="A2A多Agent写作助手启动器")
    parser.add_argument("--agents", nargs="+", 
                       help="指定要启动的Agent (默认启动所有启用的Agent)")
    parser.add_argument("--config", 
                       help="指定配置文件路径")
    parser.add_argument("--host", default="localhost",
                       help="默认主机地址")
    parser.add_argument("--writing-mode", choices=["ai", "mcp", "hybrid"], 
                       default="ai", help="写作模式")
    parser.add_argument("--list-agents", action="store_true",
                       help="列出所有可用的Agent")
    parser.add_argument("--validate-config", action="store_true",
                       help="验证配置文件")
    parser.add_argument("--daemon", action="store_true",
                       help="以守护进程模式运行")
    
    args = parser.parse_args()
    
    # 创建启动器
    launcher = AgentLauncher()
    
    # 处理特殊命令
    if args.list_agents:
        print("📋 可用的Agent:")
        for name, config in launcher.config_manager.config.agents.items():
            status = "✅ 启用" if config.enabled else "❌ 禁用"
            print(f"  - {name}: {config.host}:{config.port} {status}")
        return
    
    if args.validate_config:
        launcher.config_manager.print_config_summary()
        return
    
    # 更新配置
    if args.host != "localhost":
        for agent_name in launcher.config_manager.config.agents:
            launcher.config_manager.update_agent_config(agent_name, host=args.host)
    
    if args.writing_mode != "ai":
        for agent_name in launcher.config_manager.config.agents:
            launcher.config_manager.update_agent_config(agent_name, writing_mode=args.writing_mode)
    
    # 设置信号处理
    signal.signal(signal.SIGINT, launcher.signal_handler)
    signal.signal(signal.SIGTERM, launcher.signal_handler)
    
    # 启动Agent
    success = launcher.start_all_agents(args.agents)
    
    if success:
        if args.daemon:
            print("🔄 以守护进程模式运行...")
            # 启动监控线程
            monitor_thread = threading.Thread(target=launcher.monitor_agents)
            monitor_thread.daemon = True
            monitor_thread.start()
            
            # 主线程等待
            try:
                while launcher.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass
        else:
            print("\n按 Ctrl+C 停止系统...")
            try:
                while launcher.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass
        
        launcher.stop_all_agents()
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()