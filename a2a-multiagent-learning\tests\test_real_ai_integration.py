#!/usr/bin/env python3
"""
真实AI API集成测试
使用真实的AI API调用测试各个Agent的功能
删除硬编码的模拟数据，使用实际的Agent服务
"""

import asyncio
import sys
import time
import json
import aiohttp
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RealAIAgentTester:
    """真实AI API Agent测试器"""
    
    def __init__(self):
        self.agent_urls = {
            'plot_agent': 'http://localhost:10002',
            'character_agent': 'http://localhost:10003', 
            'content_agent': 'http://localhost:10004',
            'host_agent': 'http://localhost:10001'
        }
        self.test_results: Dict[str, Dict[str, Any]] = {}
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        
    async def run_all_tests(self):
        """运行所有真实AI API测试"""
        print("[AI-TEST] A2A多Agent系统真实AI API测试")
        print("=" * 60)
        print("[WARN] 注意：此测试需要真实的AI API密钥和运行中的Agent服务器")
        print("=" * 60)
        
        # 首先检查Agent连接状态
        await self.check_agent_connectivity()
        
        # 测试用例列表
        test_cases = [
            ("Plot Agent AI测试", self.test_plot_agent_ai),
            ("Character Agent AI测试", self.test_character_agent_ai),
            ("Content Agent AI测试", self.test_content_agent_ai),
            ("Host Agent协调测试", self.test_host_agent_coordination),
            ("端到端写作流程测试", self.test_end_to_end_writing),
            ("AI API错误处理测试", self.test_ai_error_handling),
            ("性能压力测试", self.test_performance_stress)
        ]
        
        # 执行测试
        for test_name, test_func in test_cases:
            await self.run_test(test_name, test_func)
        
        # 显示测试总结
        self.print_test_summary()
    
    async def check_agent_connectivity(self):
        """检查Agent连接状态"""
        print("\n[CHECK] 检查Agent连接状态...")
        print("-" * 40)
        
        connectivity_results = {}
        
        async with aiohttp.ClientSession() as session:
            for agent_name, url in self.agent_urls.items():
                try:
                    # 检查Agent Card
                    async with session.get(f"{url}/", timeout=aiohttp.ClientTimeout(total=5)) as response:
                        if response.status == 200:
                            agent_card = await response.json()
                            connectivity_results[agent_name] = {
                                'status': 'online',
                                'name': agent_card.get('name', 'Unknown'),
                                'skills': len(agent_card.get('skills', []))
                            }
                            print(f"[OK] {agent_name}: {agent_card.get('name', 'Unknown')} ({len(agent_card.get('skills', []))}个技能)")
                        else:
                            connectivity_results[agent_name] = {'status': 'error', 'code': response.status}
                            print(f"[ERROR] {agent_name}: HTTP {response.status}")

                except Exception as e:
                    connectivity_results[agent_name] = {'status': 'offline', 'error': str(e)}
                    print(f"[OFFLINE] {agent_name}: 离线 ({str(e)})")
        
        # 统计连接状态
        online_count = sum(1 for result in connectivity_results.values() if result['status'] == 'online')
        total_count = len(connectivity_results)
        
        print(f"\n[STATUS] 连接状态: {online_count}/{total_count} Agent在线")

        if online_count == 0:
            print("[WARN] 警告: 没有Agent在线，请先启动Agent服务器")
            print("   启动命令: python scripts/start_all_agents.py")
            return False

        return True

    async def run_test(self, test_name: str, test_func):
        """运行单个测试"""
        print(f"\n[TEST] 执行测试: {test_name}")
        print("-" * 40)
        
        self.total_tests += 1
        start_time = time.time()
        
        try:
            result = await test_func()
            end_time = time.time()
            duration = end_time - start_time
            
            if result.get("success", False):
                self.passed_tests += 1
                status = "[PASS] 通过"
            else:
                self.failed_tests += 1
                status = "[FAIL] 失败"

            self.test_results[test_name] = {
                "status": status,
                "duration": duration,
                "details": result
            }

            print(f"{status} ({duration:.2f}s)")
            if result.get("message"):
                print(f"[INFO] {result['message']}")

            # 显示详细结果
            if result.get("ai_response"):
                print(f"[AI] AI响应预览: {result['ai_response'][:100]}...")
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            self.failed_tests += 1
            
            self.test_results[test_name] = {
                "status": "[ERROR] 异常",
                "duration": duration,
                "error": str(e)
            }

            print(f"[ERROR] 异常 ({duration:.2f}s): {e}")
            logger.error(f"测试 {test_name} 异常: {e}", exc_info=True)
    
    async def test_plot_agent_ai(self) -> Dict[str, Any]:
        """测试Plot Agent的真实AI功能"""
        test_request = "帮我设计一个现代都市爱情故事的大纲，男主是霸道总裁，女主是独立的建筑师"
        
        try:
            async with aiohttp.ClientSession() as session:
                # 发送请求到Plot Agent
                request_data = {
                    "message": test_request,
                    "context_id": f"test_plot_{int(time.time())}",
                    "task_id": f"plot_task_{int(time.time())}"
                }
                
                async with session.post(
                    f"{self.agent_urls['plot_agent']}/execute",
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        
                        # 验证AI响应
                        if self.validate_plot_ai_response(result):
                            return {
                                "success": True,
                                "message": "Plot Agent AI功能正常，生成了完整的故事大纲",
                                "ai_response": str(result),
                                "response_length": len(str(result)),
                                "contains_ai_content": True
                            }
                        else:
                            return {
                                "success": False,
                                "message": "Plot Agent响应格式不正确或内容不完整",
                                "ai_response": str(result)
                            }
                    else:
                        return {
                            "success": False,
                            "message": f"Plot Agent请求失败: HTTP {response.status}",
                            "error_code": response.status
                        }
                        
        except Exception as e:
            return {
                "success": False,
                "message": f"Plot Agent AI测试失败: {str(e)}",
                "error": str(e)
            }
    
    async def test_character_agent_ai(self) -> Dict[str, Any]:
        """测试Character Agent的真实AI功能"""
        test_request = "为我的现代都市小说创建一个霸道总裁男主角，要求性格立体，有成长弧线"
        
        try:
            async with aiohttp.ClientSession() as session:
                request_data = {
                    "message": test_request,
                    "context_id": f"test_char_{int(time.time())}",
                    "task_id": f"char_task_{int(time.time())}"
                }
                
                async with session.post(
                    f"{self.agent_urls['character_agent']}/execute",
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        
                        if self.validate_character_ai_response(result):
                            return {
                                "success": True,
                                "message": "Character Agent AI功能正常，创建了详细的角色档案",
                                "ai_response": str(result),
                                "response_length": len(str(result)),
                                "contains_ai_content": True
                            }
                        else:
                            return {
                                "success": False,
                                "message": "Character Agent响应格式不正确或内容不完整",
                                "ai_response": str(result)
                            }
                    else:
                        return {
                            "success": False,
                            "message": f"Character Agent请求失败: HTTP {response.status}",
                            "error_code": response.status
                        }
                        
        except Exception as e:
            return {
                "success": False,
                "message": f"Character Agent AI测试失败: {str(e)}",
                "error": str(e)
            }
    
    async def test_content_agent_ai(self) -> Dict[str, Any]:
        """测试Content Agent的真实AI功能"""
        test_request = "写一个现代都市小说中男女主角在咖啡厅初次相遇的场景，要求有环境描写和对话"
        
        try:
            async with aiohttp.ClientSession() as session:
                request_data = {
                    "message": test_request,
                    "context_id": f"test_content_{int(time.time())}",
                    "task_id": f"content_task_{int(time.time())}"
                }
                
                async with session.post(
                    f"{self.agent_urls['content_agent']}/execute",
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        
                        if self.validate_content_ai_response(result):
                            return {
                                "success": True,
                                "message": "Content Agent AI功能正常，生成了精彩的场景内容",
                                "ai_response": str(result),
                                "response_length": len(str(result)),
                                "contains_ai_content": True
                            }
                        else:
                            return {
                                "success": False,
                                "message": "Content Agent响应格式不正确或内容不完整",
                                "ai_response": str(result)
                            }
                    else:
                        return {
                            "success": False,
                            "message": f"Content Agent请求失败: HTTP {response.status}",
                            "error_code": response.status
                        }
                        
        except Exception as e:
            return {
                "success": False,
                "message": f"Content Agent AI测试失败: {str(e)}",
                "error": str(e)
            }
    
    async def test_host_agent_coordination(self) -> Dict[str, Any]:
        """测试Host Agent的协调功能"""
        test_request = "帮我创作一个现代都市爱情小说的开头，需要故事大纲、主要角色和第一章内容"
        
        try:
            async with aiohttp.ClientSession() as session:
                request_data = {
                    "message": test_request,
                    "context_id": f"test_host_{int(time.time())}",
                    "task_id": f"host_task_{int(time.time())}"
                }
                
                async with session.post(
                    f"{self.agent_urls['host_agent']}/execute",
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=120)  # Host Agent需要更长时间
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        
                        if self.validate_host_coordination_response(result):
                            return {
                                "success": True,
                                "message": "Host Agent协调功能正常，成功协调多个Agent完成复合任务",
                                "ai_response": str(result),
                                "response_length": len(str(result)),
                                "coordination_success": True
                            }
                        else:
                            return {
                                "success": False,
                                "message": "Host Agent协调功能异常或响应不完整",
                                "ai_response": str(result)
                            }
                    else:
                        return {
                            "success": False,
                            "message": f"Host Agent请求失败: HTTP {response.status}",
                            "error_code": response.status
                        }
                        
        except Exception as e:
            return {
                "success": False,
                "message": f"Host Agent协调测试失败: {str(e)}",
                "error": str(e)
            }
    
    async def test_end_to_end_writing(self) -> Dict[str, Any]:
        """测试端到端写作流程"""
        try:
            # 步骤1: 使用Plot Agent生成大纲
            plot_result = await self.call_agent('plot_agent', "设计一个科幻爱情故事大纲")
            
            # 步骤2: 使用Character Agent创建角色
            char_result = await self.call_agent('character_agent', "创建一个科幻小说的女主角，聪明的科学家")
            
            # 步骤3: 使用Content Agent生成内容
            content_result = await self.call_agent('content_agent', "写一个科幻实验室的场景描写")
            
            # 验证所有步骤都成功
            all_success = all([
                plot_result.get('success', False),
                char_result.get('success', False), 
                content_result.get('success', False)
            ])
            
            if all_success:
                return {
                    "success": True,
                    "message": "端到端写作流程测试成功，所有Agent协作正常",
                    "plot_success": True,
                    "character_success": True,
                    "content_success": True,
                    "workflow_complete": True
                }
            else:
                return {
                    "success": False,
                    "message": "端到端写作流程部分失败",
                    "plot_success": plot_result.get('success', False),
                    "character_success": char_result.get('success', False),
                    "content_success": content_result.get('success', False)
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"端到端写作流程测试失败: {str(e)}",
                "error": str(e)
            }
    
    async def test_ai_error_handling(self) -> Dict[str, Any]:
        """测试AI API错误处理"""
        try:
            # 测试空请求
            empty_result = await self.call_agent('plot_agent', "")
            
            # 测试无效请求
            invalid_result = await self.call_agent('character_agent', "这是一个无意义的请求，不包含任何创作需求")
            
            # 测试超长请求
            long_request = "请帮我创作" + "一个非常详细的故事" * 100
            long_result = await self.call_agent('content_agent', long_request)
            
            # 评估错误处理效果
            error_handling_score = 0
            if not empty_result.get('success') and 'error' in str(empty_result).lower():
                error_handling_score += 1
            if 'suggestion' in str(invalid_result).lower() or 'clarification' in str(invalid_result).lower():
                error_handling_score += 1
            if not long_result.get('success') or 'length' in str(long_result).lower():
                error_handling_score += 1
            
            return {
                "success": error_handling_score >= 2,
                "message": f"AI错误处理测试: {error_handling_score}/3 场景处理正确",
                "empty_request_handled": not empty_result.get('success'),
                "invalid_request_handled": 'suggestion' in str(invalid_result).lower(),
                "long_request_handled": 'length' in str(long_result).lower() or not long_result.get('success'),
                "error_handling_score": error_handling_score
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"AI错误处理测试失败: {str(e)}",
                "error": str(e)
            }
    
    async def test_performance_stress(self) -> Dict[str, Any]:
        """测试性能压力"""
        try:
            # 并发测试
            concurrent_tasks = []
            for i in range(3):  # 3个并发请求
                task = self.call_agent('plot_agent', f"设计第{i+1}个故事大纲")
                concurrent_tasks.append(task)
            
            start_time = time.time()
            results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)
            end_time = time.time()
            
            # 统计结果
            successful_results = sum(1 for r in results if isinstance(r, dict) and r.get('success'))
            total_time = end_time - start_time
            avg_time = total_time / len(results)
            
            return {
                "success": successful_results >= 2,  # 至少2个成功
                "message": f"性能压力测试: {successful_results}/3 请求成功",
                "concurrent_requests": len(concurrent_tasks),
                "successful_requests": successful_results,
                "total_time": total_time,
                "average_time": avg_time,
                "performance_acceptable": avg_time < 30  # 平均30秒内完成
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"性能压力测试失败: {str(e)}",
                "error": str(e)
            }
    
    async def call_agent(self, agent_name: str, request: str) -> Dict[str, Any]:
        """调用指定Agent的通用方法"""
        try:
            async with aiohttp.ClientSession() as session:
                request_data = {
                    "message": request,
                    "context_id": f"test_{agent_name}_{int(time.time())}",
                    "task_id": f"task_{agent_name}_{int(time.time())}"
                }
                
                async with session.post(
                    f"{self.agent_urls[agent_name]}/execute",
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        return {"success": True, "result": result}
                    else:
                        return {"success": False, "error": f"HTTP {response.status}"}
                        
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def validate_plot_ai_response(self, response: Dict[str, Any]) -> bool:
        """验证Plot Agent AI响应"""
        try:
            # 检查响应是否包含实际的AI生成内容
            response_str = str(response).lower()
            
            # 检查是否包含故事相关关键词
            story_keywords = ['故事', '大纲', '情节', '角色', '冲突', '主题', '结构']
            has_story_content = any(keyword in response_str for keyword in story_keywords)
            
            # 检查响应长度（AI生成的内容通常较长）
            has_sufficient_length = len(response_str) > 200
            
            # 检查是否不是错误响应
            is_not_error = 'error' not in response_str and 'failed' not in response_str
            
            return has_story_content and has_sufficient_length and is_not_error
            
        except Exception:
            return False
    
    def validate_character_ai_response(self, response: Dict[str, Any]) -> bool:
        """验证Character Agent AI响应"""
        try:
            response_str = str(response).lower()
            
            # 检查角色相关关键词
            character_keywords = ['角色', '性格', '外貌', '背景', '职业', '年龄', '特点']
            has_character_content = any(keyword in response_str for keyword in character_keywords)
            
            has_sufficient_length = len(response_str) > 200
            is_not_error = 'error' not in response_str and 'failed' not in response_str
            
            return has_character_content and has_sufficient_length and is_not_error
            
        except Exception:
            return False
    
    def validate_content_ai_response(self, response: Dict[str, Any]) -> bool:
        """验证Content Agent AI响应"""
        try:
            response_str = str(response).lower()
            
            # 检查内容生成相关关键词
            content_keywords = ['场景', '描写', '对话', '环境', '氛围', '情节']
            has_content = any(keyword in response_str for keyword in content_keywords)
            
            has_sufficient_length = len(response_str) > 150
            is_not_error = 'error' not in response_str and 'failed' not in response_str
            
            return has_content and has_sufficient_length and is_not_error
            
        except Exception:
            return False
    
    def validate_host_coordination_response(self, response: Dict[str, Any]) -> bool:
        """验证Host Agent协调响应"""
        try:
            response_str = str(response).lower()
            
            # 检查协调相关关键词
            coordination_keywords = ['协调', '完成', '任务', '结果', '成功']
            has_coordination_content = any(keyword in response_str for keyword in coordination_keywords)
            
            has_sufficient_length = len(response_str) > 100
            is_not_error = 'error' not in response_str and 'failed' not in response_str
            
            return has_coordination_content and has_sufficient_length and is_not_error
            
        except Exception:
            return False
    
    def print_test_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("[SUMMARY] 真实AI API测试总结")
        print("=" * 60)

        success_rate = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0

        print(f"总测试数: {self.total_tests}")
        print(f"通过测试: {self.passed_tests}")
        print(f"失败测试: {self.failed_tests}")
        print(f"成功率: {success_rate:.1f}%")

        print("\n[DETAILS] 详细结果:")
        for test_name, result in self.test_results.items():
            status = result["status"]
            duration = result["duration"]
            print(f"  {test_name}: {status} ({duration:.2f}s)")
            
            if "error" in result:
                print(f"    错误: {result['error']}")
            elif result.get("details", {}).get("message"):
                print(f"    详情: {result['details']['message']}")
        
        # AI功能评估
        ai_tests = [name for name in self.test_results.keys() if 'AI' in name]
        ai_passed = sum(1 for name in ai_tests if self.test_results[name]["status"] == "[PASS] 通过")

        print(f"\n[AI-EVAL] AI功能评估:")
        print(f"  AI测试通过率: {ai_passed}/{len(ai_tests)} ({ai_passed/len(ai_tests)*100:.1f}%)" if ai_tests else "  无AI测试")

        # 总体评价
        if success_rate >= 90:
            print(f"\n[EXCELLENT] 测试结果优秀！AI集成系统运行状态良好。")
        elif success_rate >= 70:
            print(f"\n[GOOD] 测试结果良好，AI系统基本正常。")
        elif success_rate >= 50:
            print(f"\n[AVERAGE] 测试结果一般，AI系统需要优化。")
        else:
            print(f"\n[POOR] 测试结果较差，AI系统需要修复。")
        
        # 保存测试报告
        self.save_test_report()
    
    def save_test_report(self):
        """保存测试报告"""
        try:
            report = {
                "test_type": "real_ai_integration",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "summary": {
                    "total_tests": self.total_tests,
                    "passed_tests": self.passed_tests,
                    "failed_tests": self.failed_tests,
                    "success_rate": (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
                },
                "results": self.test_results,
                "agent_urls": self.agent_urls
            }
            
            report_file = project_root / "tests" / "real_ai_test_report.json"
            report_file.parent.mkdir(exist_ok=True)
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            print(f"\n[REPORT] 真实AI测试报告已保存到: {report_file}")

        except Exception as e:
            logger.error(f"保存测试报告失败: {e}")

async def main():
    """主函数"""
    tester = RealAIAgentTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())