# A2A Agent启动问题修复需求文档

## 介绍

当前A2A多Agent系统中的4个Agent（plot_agent、character_agent、content_agent、host_agent）无法正常启动，测试脚本显示所有Agent都启动了进程但无法建立HTTP连接。通过对比参考实现（travel_planner_agent），发现我们的Agent实现过于复杂，需要简化并标准化Agent结构以确保正常启动。

## 需求

### 需求1：简化Agent结构

**用户故事：** 作为系统管理员，我希望Agent能够快速可靠地启动，以便进行系统测试和部署。

#### 验证标准
1. WHEN 启动Agent进程 THEN Agent应该在10秒内成功启动HTTP服务器
2. WHEN 向Agent发送HTTP GET请求到根路径 THEN 应该返回200状态码和Agent卡片信息
3. WHEN Agent初始化失败 THEN 应该输出清晰的错误信息并优雅退出
4. WHEN Agent启动成功 THEN 应该输出启动成功的确认信息

### 需求2：标准化Agent入口点

**用户故事：** 作为开发者，我希望所有Agent都遵循相同的启动模式，以便于维护和调试。

#### 验证标准
1. WHEN 查看Agent的__main__.py文件 THEN 应该遵循参考实现的简洁结构
2. WHEN Agent启动 THEN 应该使用标准的uvicorn.run()方法启动服务器
3. WHEN 配置Agent THEN 应该支持命令行参数覆盖默认配置
4. WHEN Agent初始化 THEN 应该最小化依赖和复杂的初始化逻辑

### 需求3：简化Agent执行器

**用户故事：** 作为系统架构师，我希望Agent执行器逻辑简单可靠，避免复杂的异步处理导致启动失败。

#### 验证标准
1. WHEN 实现AgentExecutor THEN 应该遵循参考实现的简单模式
2. WHEN 处理用户请求 THEN 应该使用简单的流式响应模式
3. WHEN 发生错误 THEN 应该有适当的错误处理和状态更新
4. WHEN Agent执行任务 THEN 应该正确发送TaskArtifactUpdateEvent和TaskStatusUpdateEvent

### 需求4：优化AI客户端初始化

**用户故事：** 作为用户，我希望Agent能够正确使用我配置的OpenAI兼容API，而不会因为AI客户端初始化失败导致Agent无法启动。

#### 验证标准
1. WHEN Agent初始化AI客户端 THEN 应该使用环境变量中的OPENAI_API_KEY
2. WHEN AI客户端初始化失败 THEN Agent应该仍能启动但使用基础模式
3. WHEN 使用AI功能 THEN 应该正确调用OpenAI兼容的API端点
4. WHEN API调用失败 THEN 应该有适当的降级处理

### 需求5：移除复杂依赖

**用户故事：** 作为部署工程师，我希望Agent启动时不依赖复杂的模板加载、MCP工具等可能失败的组件。

#### 验证标准
1. WHEN Agent启动 THEN 不应该依赖外部模板文件的加载
2. WHEN Agent初始化 THEN 不应该依赖MCP工具的可用性
3. WHEN 缺少可选依赖 THEN Agent应该仍能正常启动
4. WHEN Agent运行 THEN 应该能够提供基本功能而不依赖所有高级特性

### 需求6：改进错误处理和日志

**用户故事：** 作为运维人员，我希望能够快速诊断Agent启动失败的原因。

#### 验证标准
1. WHEN Agent启动失败 THEN 应该输出详细的错误信息
2. WHEN Agent初始化组件 THEN 应该记录每个步骤的状态
3. WHEN 发生异常 THEN 应该捕获并记录异常详情
4. WHEN Agent正常运行 THEN 应该输出关键的状态信息

### 需求7：验证Agent连通性

**用户故事：** 作为测试工程师，我希望能够验证Agent是否正确启动并可以接受请求。

#### 验证标准
1. WHEN 测试脚本检查Agent状态 THEN 应该能够成功连接到Agent的HTTP端点
2. WHEN 发送GET请求到Agent根路径 THEN 应该返回有效的Agent卡片JSON
3. WHEN 发送POST请求到Agent THEN 应该能够处理简单的测试请求
4. WHEN 多个Agent同时运行 THEN 不应该有端口冲突或资源竞争问题