#!/usr/bin/env python3
"""
Plot Agent 功能测试脚本 - 直接测试函数逻辑
"""

import sys
import os
import yaml
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 获取模板路径
TEMPLATES_DIR = project_root / "templates" / "story_structures"

def load_template_file(file_path: Path) -> str:
    """加载模板文件内容"""
    try:
        if file_path.suffix == '.yaml':
            with open(file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
                return yaml.dump(data, default_flow_style=False, allow_unicode=True, indent=2)
        else:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
    except Exception as e:
        return f"无法读取模板文件 {file_path}: {str(e)}"

def test_template_loading():
    """测试模板加载功能"""
    print("📚 测试模板加载功能...")
    
    # 测试YAML模板
    hero_journey = TEMPLATES_DIR / "hero_journey.yaml"
    if hero_journey.exists():
        content = load_template_file(hero_journey)
        print(f"✅ 成功加载英雄之旅模板 ({len(content)} 字符)")
        print(content[:200] + "..." if len(content) > 200 else content)
    else:
        print("❌ 英雄之旅模板不存在")
    
    # 测试文本模板
    plot_dir = TEMPLATES_DIR / "plot" / "剧情类型"
    if plot_dir.exists():
        love_story = plot_dir / "爱情故事.txt"
        if love_story.exists():
            content = load_template_file(love_story)
            print(f"✅ 成功加载爱情故事模板 ({len(content)} 字符)")
            print(content[:200] + "..." if len(content) > 200 else content)
        else:
            print("❌ 爱情故事模板不存在")
    else:
        print("❌ 剧情类型目录不存在")
    
    print()

def test_story_outline_generation():
    """测试故事大纲生成逻辑"""
    print("🎯 测试故事大纲生成逻辑...")
    
    genre = "现代都市"
    theme = "爱情"
    target_length = "中篇"
    structure_type = "三幕式"
    additional_requirements = "包含职场元素"
    
    # 加载对应的结构模板
    template_file = TEMPLATES_DIR / "three_act.yaml"
    template_content = ""
    if template_file.exists():
        template_content = load_template_file(template_file)
    
    outline = f"""# 故事大纲

## 基本信息
- **类型**: {genre}
- **主题**: {theme}
- **长度**: {target_length}
- **结构**: {structure_type}

## 故事概述
基于{theme}主题的{genre}故事，采用{structure_type}结构展开。

## 结构框架
{template_content}

## 核心冲突
根据{theme}主题，故事的核心冲突围绕主角的内在成长和外在挑战展开。

## 额外考虑
{additional_requirements if additional_requirements else "无特殊要求"}
"""
    
    print("✅ 故事大纲生成成功:")
    print(outline[:500] + "..." if len(outline) > 500 else outline)
    print()

def test_chapter_structure_creation():
    """测试章节结构创建逻辑"""
    print("📖 测试章节结构创建逻辑...")
    
    total_chapters = 15
    pacing_style = "稳步推进"
    
    # 根据节奏风格调整章节分配
    if pacing_style == "稳步推进":
        opening_ratio = 0.25
        development_ratio = 0.50
        climax_ratio = 0.20
        ending_ratio = 0.05
    
    opening_chapters = max(1, int(total_chapters * opening_ratio))
    development_chapters = max(1, int(total_chapters * development_ratio))
    climax_chapters = max(1, int(total_chapters * climax_ratio))
    ending_chapters = max(1, total_chapters - opening_chapters - development_chapters - climax_chapters)
    
    structure = f"""# 章节结构规划

## 基本设置
- **总章节数**: {total_chapters}章
- **节奏风格**: {pacing_style}

## 章节分配
- **开端**: {opening_chapters}章
- **发展**: {development_chapters}章  
- **高潮**: {climax_chapters}章
- **结局**: {ending_chapters}章
"""
    
    print("✅ 章节结构创建成功:")
    print(structure)
    print()

def test_conflict_analysis():
    """测试冲突分析逻辑"""
    print("⚔️ 测试冲突分析逻辑...")
    
    story_context = """
    故事背景：现代都市，主角李小雅是一名刚毕业的设计师，进入了一家知名广告公司。
    她遇到了公司的CEO陈浩然，两人因为工作产生了矛盾，但逐渐产生了感情。
    然而，陈浩然有一个未婚妻，是商业联姻的对象。
    """
    
    conflict_types = ["人与人", "人与自我", "人与社会"]
    
    # 加载冲突模板
    conflicts_dir = TEMPLATES_DIR / "plot" / "剧情冲突"
    conflict_templates = {}
    
    if conflicts_dir.exists():
        for conflict_file in conflicts_dir.glob("*.txt"):
            conflict_name = conflict_file.stem
            conflict_templates[conflict_name] = load_template_file(conflict_file)
    
    analysis = f"""# 情节冲突分析

## 故事背景
{story_context}

## 冲突类型分析
找到 {len(conflict_templates)} 个冲突模板
分析 {len(conflict_types)} 种冲突类型
"""
    
    print("✅ 冲突分析逻辑正常:")
    print(analysis)
    print()

def test_plot_twist_suggestions():
    """测试情节转折建议逻辑"""
    print("🌪️ 测试情节转折建议逻辑...")
    
    twist_type = "反转与惊喜"
    target_chapter = 12
    
    # 加载转折模板
    twists_dir = TEMPLATES_DIR / "plot" / "剧情转折"
    twist_template = ""
    
    if twists_dir.exists():
        template_file = twists_dir / f"{twist_type}.txt"
        if template_file.exists():
            twist_template = load_template_file(template_file)
    
    suggestions = f"""# 情节转折建议

## 转折类型：{twist_type}
## 目标章节：第{target_chapter}章

### 模板状态
{"✅ 找到对应模板" if twist_template else "❌ 未找到对应模板"}

### 转折建议
根据{twist_type}类型，提供相应的转折建议...
"""
    
    print("✅ 转折建议逻辑正常:")
    print(suggestions)
    print()

def test_template_discovery():
    """测试模板发现功能"""
    print("🔍 测试模板发现功能...")
    
    templates = {
        "story_structures": [],
        "plot_types": [],
        "conflicts": [],
        "twists": []
    }
    
    # 基础故事结构
    basic_templates = TEMPLATES_DIR.glob("*.yaml")
    for template in basic_templates:
        templates["story_structures"].append(template.stem)
    
    # 详细剧情模板
    plot_dir = TEMPLATES_DIR / "plot"
    if plot_dir.exists():
        # 剧情类型
        plot_types_dir = plot_dir / "剧情类型"
        if plot_types_dir.exists():
            for template in plot_types_dir.glob("*.txt"):
                templates["plot_types"].append(template.stem)
        
        # 剧情冲突
        conflicts_dir = plot_dir / "剧情冲突"
        if conflicts_dir.exists():
            for template in conflicts_dir.glob("*.txt"):
                templates["conflicts"].append(template.stem)
        
        # 剧情转折
        twists_dir = plot_dir / "剧情转折"
        if twists_dir.exists():
            for template in twists_dir.glob("*.txt"):
                templates["twists"].append(template.stem)
    
    print("✅ 模板发现结果:")
    print(f"- 故事结构模板: {len(templates['story_structures'])} 个")
    print(f"- 剧情类型模板: {len(templates['plot_types'])} 个")
    print(f"- 冲突类型模板: {len(templates['conflicts'])} 个")
    print(f"- 转折技巧模板: {len(templates['twists'])} 个")
    print()

def main():
    """运行所有测试"""
    print("🧪 开始测试Plot Agent核心功能...")
    print("=" * 50)
    
    try:
        test_template_loading()
        test_story_outline_generation()
        test_chapter_structure_creation()
        test_conflict_analysis()
        test_plot_twist_suggestions()
        test_template_discovery()
        
        print("🎉 所有功能测试完成！Plot Agent核心逻辑运行正常。")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()