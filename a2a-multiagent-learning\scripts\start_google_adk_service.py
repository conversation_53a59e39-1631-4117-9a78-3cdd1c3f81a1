#!/usr/bin/env python3
"""
启动Google ADK写作助手服务的便捷脚本
"""

import sys
import subprocess
import os
from pathlib import Path

def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    # 检查Google API密钥
    google_api_key = os.getenv('GOOGLE_API_KEY')
    if google_api_key:
        print("✅ GOOGLE_API_KEY: 已配置")
    else:
        print("⚠️ GOOGLE_API_KEY: 未配置")
        print("   请在.env文件中设置GOOGLE_API_KEY")
    
    # 检查其他必要的环境变量
    openai_api_key = os.getenv('OPENAI_API_KEY')
    if openai_api_key:
        print("✅ OPENAI_API_KEY: 已配置")
    else:
        print("⚠️ OPENAI_API_KEY: 未配置")
    
    # 检查Python依赖
    try:
        import google.genai
        print("✅ google-genai: 已安装")
    except ImportError:
        print("❌ google-genai: 未安装")
        print("   请运行: pip install google-genai")
        return False
    
    return True

def start_google_adk_service():
    """启动Google ADK服务"""
    print("🤖 启动Google ADK写作助手服务...")
    
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    
    # 检查环境
    if not check_environment():
        print("❌ 环境检查失败，请先配置必要的环境变量和依赖")
        return False
    
    try:
        # 启动Host Agent服务
        cmd = [sys.executable, "-m", "host_agent"]
        print(f"🚀 执行命令: {' '.join(cmd)}")
        print("📍 工作目录:", project_root)
        print()
        
        subprocess.run(cmd, cwd=project_root, check=True)
        
    except KeyboardInterrupt:
        print("\n🤖 Google ADK服务已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 启动异常: {e}")
        return False
    
    return True

def show_usage():
    """显示使用说明"""
    print("📖 Google ADK写作助手服务使用说明:")
    print()
    print("🔧 环境配置:")
    print("   1. 在.env文件中配置GOOGLE_API_KEY")
    print("   2. 确保已安装google-genai: pip install google-genai")
    print("   3. 配置其他必要的API密钥")
    print()
    print("🚀 启动服务:")
    print("   python scripts/start_google_adk_service.py")
    print("   或")
    print("   python -m host_agent")
    print()
    print("💡 功能特性:")
    print("   - 基于Google ADK的智能写作助手")
    print("   - 多Agent协调和任务分发")
    print("   - 智能会话管理和进度追踪")
    print("   - 支持复杂的写作工作流程")
    print()
    print("🎯 使用示例:")
    print("   - 创建一个现代都市爱情小说项目")
    print("   - 帮我设计一个霸道总裁的故事大纲")
    print("   - 为我的小说创建一个女主角")
    print("   - 写一个咖啡厅相遇的浪漫场景")

if __name__ == "__main__":
    print("🤖 Google ADK智能写作助手服务启动器")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h', 'help']:
        show_usage()
    else:
        success = start_google_adk_service()
        if not success:
            print("\n💡 使用 --help 查看详细说明")
            sys.exit(1)