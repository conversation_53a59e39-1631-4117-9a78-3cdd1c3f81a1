#!/usr/bin/env python3
"""
测试写作协调器功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_writing_coordinator_basic():
    """测试写作协调器基础功能"""
    print("📝 测试写作协调器基础功能...")
    print("=" * 60)
    
    try:
        from host_agent.writing_coordinator import (
            WritingCoordinator,
            WritingTaskType,
            TaskPriority,
            WritingTask,
            WritingSession,
            get_writing_coordinator,
            shutdown_writing_coordinator
        )
        print("✅ 写作协调器模块导入成功")
        
        # 测试数据类
        print("\n📋 测试数据类...")
        
        # 测试WritingTask
        task = WritingTask(
            task_id="test_task_001",
            task_type=WritingTaskType.PLOT_PLANNING,
            content="帮我设计一个现代都市爱情故事的大纲"
        )
        print(f"✅ WritingTask创建成功: {task.task_id}")
        print(f"   任务类型: {task.task_type.value}")
        print(f"   优先级: {task.priority.value}")
        print(f"   状态: {task.status}")
        
        # 测试WritingSession
        session = WritingSession(
            session_id="test_session_001",
            project_name="测试小说项目",
            genre="现代都市"
        )
        print(f"✅ WritingSession创建成功: {session.session_id}")
        print(f"   项目名称: {session.project_name}")
        print(f"   类型: {session.genre}")
        print(f"   状态: {session.status}")
        
        # 测试协调器初始化
        print("\n🚀 测试协调器初始化...")
        coordinator = WritingCoordinator()
        
        # 测试路由规则
        print(f"✅ 路由规则数量: {len(coordinator.routing_rules)}")
        for task_type, agents in coordinator.routing_rules.items():
            print(f"   {task_type.value}: {agents}")
        
        # 测试请求分析
        print("\n🔍 测试请求分析...")
        test_requests = [
            "帮我设计一个现代都市爱情故事的大纲",
            "为我的小说创建一个霸道总裁男主角",
            "根据大纲写第一章的开头场景",
            "优化这段对话，使其更自然",
            "我想写一个关于时间旅行的科幻小说"
        ]
        
        for request in test_requests:
            task_type = coordinator.analyze_writing_request(request)
            print(f"✅ '{request[:20]}...' → {task_type.value}")
        
        # 测试Agent选择
        print("\n🤖 测试Agent选择...")
        available_agents = ["plot_agent", "character_agent", "content_agent"]
        
        for task_type in WritingTaskType:
            selected = coordinator.select_best_agent(task_type, available_agents)
            print(f"✅ {task_type.value} → {selected}")
        
        print("\n🎉 写作协调器基础功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_writing_coordinator_advanced():
    """测试写作协调器高级功能"""
    print("\n📝 测试写作协调器高级功能...")
    print("=" * 60)
    
    try:
        from host_agent.writing_coordinator import get_writing_coordinator, shutdown_writing_coordinator
        
        # 获取协调器实例
        coordinator = await get_writing_coordinator()
        print("✅ 获取写作协调器实例成功")
        
        # 测试会话创建
        print("\n📝 测试会话创建...")
        session = await coordinator.create_writing_session(
            session_id="test_session_advanced",
            project_name="高级测试项目",
            genre="玄幻修仙",
            context={"setting": "修仙世界", "protagonist": "天才少年"}
        )
        print(f"✅ 会话创建成功: {session.session_id}")
        print(f"   项目: {session.project_name}")
        print(f"   类型: {session.genre}")
        
        # 测试任务添加
        print("\n📋 测试任务添加...")
        test_tasks = [
            "设计一个修仙世界的背景设定",
            "创建一个天才少年主角",
            "写一个主角初入修仙门派的场景"
        ]
        
        added_tasks = []
        for i, task_content in enumerate(test_tasks, 1):
            task = await coordinator.add_writing_task(
                session_id="test_session_advanced",
                content=task_content,
                context={"step": i}
            )
            added_tasks.append(task)
            print(f"✅ 任务 {i} 添加成功: {task.task_id}")
            print(f"   类型: {task.task_type.value}")
        
        # 测试会话状态查询
        print("\n📊 测试会话状态查询...")
        status = await coordinator.get_session_status("test_session_advanced")
        if status:
            print(f"✅ 会话状态查询成功:")
            print(f"   总任务数: {status['total_tasks']}")
            print(f"   已完成: {status['completed_tasks']}")
            print(f"   待处理: {status['pending_tasks']}")
        
        # 测试Agent工作负载
        print("\n🤖 测试Agent工作负载...")
        workload = await coordinator.get_agent_workload()
        print(f"✅ Agent工作负载查询成功:")
        for agent_id, load in workload.items():
            print(f"   {agent_id}: {load['status']} - {load['assigned_tasks']}个任务")
        
        # 测试工作流程（模拟，不实际执行）
        print("\n🔄 测试工作流程设计...")
        workflow_steps = [
            "分析小说类型和目标读者",
            "设计故事大纲和主要情节",
            "创建主要角色和关系网络",
            "写作第一章内容",
            "优化和润色内容"
        ]
        
        print(f"✅ 工作流程设计完成: {len(workflow_steps)}个步骤")
        for i, step in enumerate(workflow_steps, 1):
            print(f"   步骤 {i}: {step}")
        
        # 清理测试资源
        print("\n🧹 清理测试资源...")
        await shutdown_writing_coordinator()
        print("✅ 资源清理完成")
        
        print("\n🎉 写作协调器高级功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 高级功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_writing_scenarios():
    """测试各种写作场景"""
    print("\n📚 测试各种写作场景...")
    print("=" * 40)
    
    try:
        from host_agent.writing_coordinator import WritingCoordinator
        
        coordinator = WritingCoordinator()
        
        # 测试各种写作场景的请求分析
        scenarios = [
            {
                "name": "情节规划场景",
                "requests": [
                    "帮我设计一个悬疑推理小说的大纲",
                    "这个故事需要什么样的冲突和转折",
                    "如何安排三幕式的故事结构"
                ]
            },
            {
                "name": "角色创建场景", 
                "requests": [
                    "创建一个聪明的女侦探角色",
                    "设计主角和反派的关系网络",
                    "这个角色需要什么样的成长弧线"
                ]
            },
            {
                "name": "内容生成场景",
                "requests": [
                    "写一个紧张的追逐场景",
                    "生成角色之间的对话",
                    "描写一个神秘的犯罪现场"
                ]
            },
            {
                "name": "内容优化场景",
                "requests": [
                    "优化这段描写，让它更生动",
                    "润色这个对话，使其更自然",
                    "改进这个情节的逻辑性"
                ]
            }
        ]
        
        for scenario in scenarios:
            print(f"\n📖 {scenario['name']}:")
            for request in scenario['requests']:
                task_type = coordinator.analyze_writing_request(request)
                recommended_agents = coordinator.routing_rules.get(task_type, [])
                print(f"   '{request}' → {task_type.value} → {recommended_agents}")
        
        print("\n✅ 写作场景测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 写作场景测试失败: {e}")
        return False

if __name__ == "__main__":
    async def main():
        print("🚀 开始写作协调器测试...")
        print()
        
        success1 = await test_writing_coordinator_basic()
        success2 = await test_writing_coordinator_advanced()
        success3 = await test_writing_scenarios()
        
        if success1 and success2 and success3:
            print("\n🎉 所有写作协调器测试通过！")
            print("\n💡 提示:")
            print("   - 写作协调器核心逻辑正常工作")
            print("   - 智能路由和任务分析功能有效")
            print("   - 会话管理和工作流程设计完善")
            print("   - 要测试实际协调功能，需要启动Agent服务器")
        else:
            print("\n❌ 部分测试失败")
            sys.exit(1)
    
    asyncio.run(main())