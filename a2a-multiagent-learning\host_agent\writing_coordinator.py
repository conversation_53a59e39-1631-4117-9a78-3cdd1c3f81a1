#!/usr/bin/env python3
"""
写作协调器
实现智能写作路由逻辑，协调多个Agent完成复杂写作任务
"""

import asyncio
import logging
import re
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
import json
from datetime import datetime

from .remote_agent_connection import get_connection_manager, RemoteAgentConnections

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class WritingTaskType(Enum):
    """写作任务类型枚举"""
    PLOT_PLANNING = "plot_planning"          # 情节规划
    CHARACTER_CREATION = "character_creation" # 角色创建
    CONTENT_GENERATION = "content_generation" # 内容生成
    STORY_OUTLINE = "story_outline"          # 故事大纲
    CHAPTER_WRITING = "chapter_writing"      # 章节写作
    DIALOGUE_CREATION = "dialogue_creation"  # 对话创作
    SCENE_DESCRIPTION = "scene_description"  # 场景描写
    CONTENT_REFINEMENT = "content_refinement" # 内容优化
    MIXED_TASK = "mixed_task"               # 混合任务


class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class WritingTask:
    """写作任务数据类"""
    task_id: str
    task_type: WritingTaskType
    content: str
    priority: TaskPriority = TaskPriority.NORMAL
    context: Dict[str, Any] = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)
    assigned_agent: Optional[str] = None
    status: str = "pending"
    result: Optional[Dict[str, Any]] = None
    created_at: datetime = field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None


@dataclass
class WritingSession:
    """写作会话数据类"""
    session_id: str
    project_name: str
    genre: str = "现代都市"
    context: Dict[str, Any] = field(default_factory=dict)
    tasks: List[WritingTask] = field(default_factory=list)
    completed_tasks: List[WritingTask] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    last_activity: datetime = field(default_factory=datetime.now)
    status: str = "active"


class WritingCoordinator:
    """写作协调器"""
    
    def __init__(self):
        self.connection_manager: Optional[RemoteAgentConnections] = None
        self.active_sessions: Dict[str, WritingSession] = {}
        self.task_queue: List[WritingTask] = []
        self.agent_capabilities: Dict[str, List[str]] = {}
        self.routing_rules: Dict[WritingTaskType, List[str]] = {}
        self._initialize_routing_rules()
    
    def _initialize_routing_rules(self):
        """初始化路由规则"""
        self.routing_rules = {
            WritingTaskType.PLOT_PLANNING: ["plot_agent"],
            WritingTaskType.STORY_OUTLINE: ["plot_agent"],
            WritingTaskType.CHARACTER_CREATION: ["character_agent"],
            WritingTaskType.CONTENT_GENERATION: ["content_agent"],
            WritingTaskType.CHAPTER_WRITING: ["content_agent"],
            WritingTaskType.DIALOGUE_CREATION: ["content_agent"],
            WritingTaskType.SCENE_DESCRIPTION: ["content_agent"],
            WritingTaskType.CONTENT_REFINEMENT: ["content_agent"],
            WritingTaskType.MIXED_TASK: ["plot_agent", "character_agent", "content_agent"]
        }
    
    async def initialize(self) -> bool:
        """初始化写作协调器"""
        logger.info("🚀 初始化写作协调器...")
        
        try:
            # 获取连接管理器
            self.connection_manager = await get_connection_manager()
            
            # 更新Agent能力信息
            await self._update_agent_capabilities()
            
            logger.info("✅ 写作协调器初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 写作协调器初始化失败: {e}")
            return False
    
    async def _update_agent_capabilities(self):
        """更新Agent能力信息"""
        if not self.connection_manager:
            return
        
        available_agents = await self.connection_manager.get_available_agents()
        
        for agent_id in available_agents:
            capabilities = await self.connection_manager.get_agent_capabilities(agent_id)
            skills = await self.connection_manager.get_agent_skills(agent_id)
            
            self.agent_capabilities[agent_id] = {
                'capabilities': capabilities,
                'skills': [skill.get('id', '') for skill in skills],
                'skill_names': [skill.get('name', '') for skill in skills]
            }
            
            logger.info(f"📋 更新Agent能力: {agent_id} - {len(skills)}个技能")
    
    def analyze_writing_request(self, request: str) -> WritingTaskType:
        """分析写作请求，确定任务类型"""
        request_lower = request.lower()
        
        # 优化关键词和权重系统
        # 情节规划相关关键词（权重2）
        plot_keywords = ['大纲', '情节', '剧情', '故事结构', '三幕', '冲突', '转折', '悬念', '故事线', '框架', '构思', '分析']
        # 角色创建相关关键词（权重2）
        character_keywords = ['角色', '人物', '主角', '配角', '性格', '背景', '关系', '人设', '男主', '女主', '总裁']
        # 内容生成相关关键词（权重1）
        content_keywords = ['写', '创作', '生成', '章节', '场景', '对话', '描写', '内容', '开头', '相遇']
        # 优化相关关键词（权重3，最高优先级）
        refinement_keywords = ['优化', '润色', '修改', '完善', '改进', '提升', '让', '使其', '更']
        
        # 特殊模式匹配（高优先级）
        # 检查是否是优化请求
        if any(keyword in request_lower for keyword in ['优化', '润色', '修改', '完善', '让', '使其更']):
            return WritingTaskType.CONTENT_REFINEMENT
        
        # 检查是否是明确的写作请求
        if any(keyword in request_lower for keyword in ['写一个', '写第', '根据', '生成']):
            return WritingTaskType.CONTENT_GENERATION
        
        # 检查是否是角色创建请求
        if any(keyword in request_lower for keyword in ['创建', '设计']) and any(keyword in request_lower for keyword in character_keywords):
            return WritingTaskType.CHARACTER_CREATION
        
        # 检查是否是情节规划请求
        if any(keyword in request_lower for keyword in ['设计', '构思', '分析']) and any(keyword in request_lower for keyword in plot_keywords):
            return WritingTaskType.PLOT_PLANNING
        
        # 计算关键词匹配分数（带权重）
        plot_score = sum(2 for keyword in plot_keywords if keyword in request_lower)
        character_score = sum(2 for keyword in character_keywords if keyword in request_lower)
        content_score = sum(1 for keyword in content_keywords if keyword in request_lower)
        refinement_score = sum(3 for keyword in refinement_keywords if keyword in request_lower)
        
        # 根据分数确定任务类型
        scores = {
            WritingTaskType.PLOT_PLANNING: plot_score,
            WritingTaskType.CHARACTER_CREATION: character_score,
            WritingTaskType.CONTENT_GENERATION: content_score,
            WritingTaskType.CONTENT_REFINEMENT: refinement_score
        }
        
        # 找到最高分的任务类型
        max_score = max(scores.values())
        if max_score == 0:
            return WritingTaskType.MIXED_TASK
        
        # 如果有多个相同最高分，按优先级选择
        if refinement_score == max_score:
            return WritingTaskType.CONTENT_REFINEMENT
        elif plot_score == max_score:
            return WritingTaskType.PLOT_PLANNING
        elif character_score == max_score:
            return WritingTaskType.CHARACTER_CREATION
        elif content_score == max_score:
            return WritingTaskType.CONTENT_GENERATION
        
        return WritingTaskType.MIXED_TASK
    
    def select_best_agent(self, task_type: WritingTaskType, available_agents: List[str]) -> Optional[str]:
        """选择最适合的Agent"""
        # 获取任务类型对应的推荐Agent列表
        recommended_agents = self.routing_rules.get(task_type, [])
        
        # 找到可用且推荐的Agent
        for agent_id in recommended_agents:
            if agent_id in available_agents:
                return agent_id
        
        # 如果没有推荐的Agent可用，返回第一个可用的Agent
        return available_agents[0] if available_agents else None
    
    async def create_writing_session(self, session_id: str, project_name: str, 
                                   genre: str = "现代都市", context: Dict[str, Any] = None) -> WritingSession:
        """创建写作会话"""
        if context is None:
            context = {}
        
        session = WritingSession(
            session_id=session_id,
            project_name=project_name,
            genre=genre,
            context=context
        )
        
        self.active_sessions[session_id] = session
        
        # 在连接管理器中创建会话
        if self.connection_manager:
            await self.connection_manager.create_writing_session(session_id, {
                'project_name': project_name,
                'genre': genre,
                **context
            })
        
        logger.info(f"📝 创建写作会话: {session_id} ({project_name})")
        return session
    
    async def add_writing_task(self, session_id: str, content: str, 
                             task_type: Optional[WritingTaskType] = None,
                             priority: TaskPriority = TaskPriority.NORMAL,
                             context: Dict[str, Any] = None) -> WritingTask:
        """添加写作任务"""
        if session_id not in self.active_sessions:
            raise ValueError(f"会话 {session_id} 不存在")
        
        if context is None:
            context = {}
        
        # 自动分析任务类型
        if task_type is None:
            task_type = self.analyze_writing_request(content)
        
        # 生成任务ID
        task_id = f"{session_id}_task_{len(self.active_sessions[session_id].tasks) + 1}"
        
        task = WritingTask(
            task_id=task_id,
            task_type=task_type,
            content=content,
            priority=priority,
            context=context
        )
        
        # 添加到会话和任务队列
        self.active_sessions[session_id].tasks.append(task)
        self.task_queue.append(task)
        
        logger.info(f"📋 添加写作任务: {task_id} ({task_type.value})")
        return task
    
    async def execute_writing_task(self, task: WritingTask) -> Dict[str, Any]:
        """执行写作任务"""
        if not self.connection_manager:
            raise RuntimeError("连接管理器未初始化")
        
        logger.info(f"🎯 执行写作任务: {task.task_id} ({task.task_type.value})")
        
        try:
            # 更新任务状态
            task.status = "executing"
            
            # 获取可用的Agent
            available_agents = await self.connection_manager.get_available_agents()
            if not available_agents:
                raise RuntimeError("没有可用的Agent")
            
            # 选择最适合的Agent
            selected_agent = self.select_best_agent(task.task_type, available_agents)
            if not selected_agent:
                raise RuntimeError(f"没有适合的Agent处理任务类型: {task.task_type.value}")
            
            task.assigned_agent = selected_agent
            logger.info(f"🤖 选择Agent: {selected_agent}")
            
            # 构造任务数据
            task_data = {
                "message": task.content,
                "context": {
                    "task_id": task.task_id,
                    "task_type": task.task_type.value,
                    "priority": task.priority.value,
                    **task.context
                }
            }
            
            # 发送任务到Agent
            result = await self.connection_manager.send_task_to_agent(selected_agent, task_data)
            
            if result:
                task.status = "completed"
                task.result = result
                task.completed_at = datetime.now()
                logger.info(f"✅ 任务完成: {task.task_id}")
                return result
            else:
                raise RuntimeError("Agent返回空结果")
                
        except Exception as e:
            task.status = "failed"
            task.error_message = str(e)
            logger.error(f"❌ 任务执行失败: {task.task_id} - {e}")
            raise
    
    async def coordinate_writing(self, session_id: str, request: str, 
                               context: Dict[str, Any] = None) -> Dict[str, Any]:
        """协调写作任务"""
        logger.info(f"📝 协调写作任务: {session_id}")
        logger.info(f"📋 请求内容: {request}")
        
        try:
            # 确保会话存在
            if session_id not in self.active_sessions:
                await self.create_writing_session(session_id, f"项目_{session_id}")
            
            # 分析请求并创建任务
            task = await self.add_writing_task(session_id, request, context=context)
            
            # 执行任务
            result = await self.execute_writing_task(task)
            
            # 更新会话活动时间
            self.active_sessions[session_id].last_activity = datetime.now()
            
            return {
                "success": True,
                "task_id": task.task_id,
                "task_type": task.task_type.value,
                "assigned_agent": task.assigned_agent,
                "result": result,
                "session_info": {
                    "session_id": session_id,
                    "total_tasks": len(self.active_sessions[session_id].tasks),
                    "completed_tasks": len([t for t in self.active_sessions[session_id].tasks if t.status == "completed"])
                }
            }
            
        except Exception as e:
            logger.error(f"❌ 写作协调失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "session_id": session_id
            }
    
    async def writing_workflow(self, session_id: str, workflow_steps: List[str]) -> List[Dict[str, Any]]:
        """管理完整的写作工作流程"""
        logger.info(f"🔄 开始写作工作流程: {session_id}")
        logger.info(f"📋 工作流步骤: {len(workflow_steps)}个")
        
        results = []
        
        try:
            for i, step in enumerate(workflow_steps, 1):
                logger.info(f"📝 执行步骤 {i}/{len(workflow_steps)}: {step}")
                
                # 协调单个步骤
                step_result = await self.coordinate_writing(session_id, step)
                results.append({
                    "step": i,
                    "content": step,
                    "result": step_result
                })
                
                # 如果步骤失败，记录但继续执行
                if not step_result.get("success", False):
                    logger.warning(f"⚠️ 步骤 {i} 执行失败，继续下一步")
                
                # 短暂延迟，避免过快请求
                await asyncio.sleep(1)
            
            logger.info(f"✅ 写作工作流程完成: {session_id}")
            return results
            
        except Exception as e:
            logger.error(f"❌ 写作工作流程失败: {e}")
            results.append({
                "error": str(e),
                "failed_at_step": len(results) + 1
            })
            return results
    
    async def get_session_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话状态"""
        if session_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[session_id]
        
        return {
            "session_id": session_id,
            "project_name": session.project_name,
            "genre": session.genre,
            "status": session.status,
            "created_at": session.created_at.isoformat(),
            "last_activity": session.last_activity.isoformat(),
            "total_tasks": len(session.tasks),
            "completed_tasks": len([t for t in session.tasks if t.status == "completed"]),
            "failed_tasks": len([t for t in session.tasks if t.status == "failed"]),
            "pending_tasks": len([t for t in session.tasks if t.status == "pending"]),
            "tasks": [
                {
                    "task_id": task.task_id,
                    "task_type": task.task_type.value,
                    "status": task.status,
                    "assigned_agent": task.assigned_agent,
                    "created_at": task.created_at.isoformat(),
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None
                }
                for task in session.tasks
            ]
        }
    
    async def get_agent_workload(self) -> Dict[str, Dict[str, Any]]:
        """获取Agent工作负载"""
        if not self.connection_manager:
            return {}
        
        agent_status = await self.connection_manager.get_all_agent_status()
        workload = {}
        
        for agent_id, status in agent_status.items():
            # 计算分配给该Agent的任务数
            assigned_tasks = [task for task in self.task_queue if task.assigned_agent == agent_id]
            
            workload[agent_id] = {
                "status": status["status"],
                "assigned_tasks": len(assigned_tasks),
                "completed_tasks": len([t for t in assigned_tasks if t.status == "completed"]),
                "failed_tasks": len([t for t in assigned_tasks if t.status == "failed"]),
                "capabilities": status.get("capabilities", []),
                "skills_count": status.get("skills_count", 0)
            }
        
        return workload
    
    async def cleanup_completed_sessions(self, max_age_hours: int = 24):
        """清理已完成的会话"""
        current_time = datetime.now()
        sessions_to_remove = []
        
        for session_id, session in self.active_sessions.items():
            # 计算会话年龄
            age_hours = (current_time - session.last_activity).total_seconds() / 3600
            
            # 检查是否需要清理
            if age_hours > max_age_hours and session.status == "completed":
                sessions_to_remove.append(session_id)
        
        # 移除过期会话
        for session_id in sessions_to_remove:
            del self.active_sessions[session_id]
            logger.info(f"🧹 清理过期会话: {session_id}")
        
        return len(sessions_to_remove)


# 全局协调器实例
_coordinator: Optional[WritingCoordinator] = None


async def get_writing_coordinator() -> WritingCoordinator:
    """获取全局写作协调器实例"""
    global _coordinator
    
    if _coordinator is None:
        _coordinator = WritingCoordinator()
        await _coordinator.initialize()
    
    return _coordinator


async def shutdown_writing_coordinator():
    """关闭全局写作协调器"""
    global _coordinator
    
    if _coordinator:
        # 清理资源
        _coordinator.active_sessions.clear()
        _coordinator.task_queue.clear()
        _coordinator = None