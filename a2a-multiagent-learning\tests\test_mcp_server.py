#!/usr/bin/env python3
"""
Plot Agent MCP服务器启动测试
"""

import sys
import os
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_mcp_server_import():
    """测试MCP服务器模块导入"""
    print("🔧 测试MCP服务器模块导入...")
    
    try:
        from plot_agent.plot_mcp import mcp
        print("✅ MCP服务器模块导入成功")
        
        # 检查工具注册（FastMCP的工具通过装饰器注册）
        print("✅ MCP工具通过装饰器注册，无法直接列出")
        print("✅ 预期工具: generate_story_outline, create_chapter_structure, analyze_plot_conflicts, suggest_plot_twists, get_plot_templates")
        
        return True
        
    except Exception as e:
        print(f"❌ MCP服务器模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mcp_tools():
    """测试MCP工具功能"""
    print("\n🛠️ 测试MCP工具功能...")
    
    try:
        # 直接导入工具函数来验证它们存在
        from plot_agent.plot_mcp import (
            generate_story_outline,
            create_chapter_structure,
            analyze_plot_conflicts,
            suggest_plot_twists,
            get_plot_templates
        )
        
        expected_tools = [
            "generate_story_outline",
            "create_chapter_structure", 
            "analyze_plot_conflicts",
            "suggest_plot_twists",
            "get_plot_templates"
        ]
        
        for tool_name in expected_tools:
            print(f"✅ 工具 {tool_name} 已注册")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试MCP工具时出错: {e}")
        return False

def test_server_startup():
    """测试服务器启动（不实际启动，只检查配置）"""
    print("\n🚀 测试服务器启动配置...")
    
    try:
        from plot_agent.plot_mcp import mcp
        
        # 检查服务器配置
        print(f"✅ 服务器名称: {mcp.name}")
        print("✅ 服务器配置正常")
        
        # 注意：这里不实际启动服务器，因为会阻塞测试
        print("ℹ️ 服务器启动测试跳过（避免阻塞）")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务器启动配置测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("🧪 开始测试Plot Agent MCP服务器...")
    print("=" * 50)
    
    tests = [
        test_mcp_server_import,
        test_mcp_tools,
        test_server_startup
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有MCP服务器测试通过！")
        print("\n💡 启动服务器命令:")
        print("   python -m plot_agent")
        print("   或者")
        print("   python plot_agent/__main__.py")
    else:
        print("❌ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()