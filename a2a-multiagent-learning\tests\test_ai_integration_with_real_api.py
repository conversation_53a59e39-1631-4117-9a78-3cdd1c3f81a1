#!/usr/bin/env python3
"""
测试AI集成与真实API的完整功能
使用第三方AI API验证Google ADK集成
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_ai_integration_with_real_api():
    """测试AI集成与真实API的完整功能"""
    print("🤖 测试AI集成与真实API的完整功能...")
    print("=" * 60)
    
    try:
        from host_agent.google_adk_integration import get_writing_assistant, shutdown_writing_assistant
        
        # 获取写作助手
        assistant = await get_writing_assistant()
        
        # 检查AI客户端是否初始化成功
        if not assistant.client:
            print("⚠️ AI客户端未初始化，将测试基础功能")
        else:
            print(f"✅ AI客户端初始化成功，模型: {assistant.model_name}")
        
        # 测试案例1: 创建写作项目
        print("\n📝 测试案例1: 创建写作项目")
        print("-" * 40)
        
        request1 = "创建一个现代都市爱情小说项目，男主是霸道总裁，女主是普通白领"
        print(f"🎯 用户请求: {request1}")
        
        response1 = await assistant.process_user_request(request1)
        print(f"🤖 AI助手回复:")
        print(response1)
        
        # 短暂延迟
        await asyncio.sleep(2)
        
        # 测试案例2: 查看项目状态
        print("\n📝 测试案例2: 查看项目状态")
        print("-" * 40)
        
        request2 = "查看当前项目的状态和进度"
        print(f"🎯 用户请求: {request2}")
        
        response2 = await assistant.process_user_request(request2)
        print(f"🤖 AI助手回复:")
        print(response2)
        
        # 短暂延迟
        await asyncio.sleep(2)
        
        # 测试案例3: 写作任务协调
        print("\n📝 测试案例3: 写作任务协调")
        print("-" * 40)
        
        request3 = "帮我设计这个爱情故事的大纲，要有三幕结构"
        print(f"🎯 用户请求: {request3}")
        
        response3 = await assistant.process_user_request(request3)
        print(f"🤖 AI助手回复:")
        print(response3)
        
        # 测试进度报告
        print("\n📊 测试进度报告...")
        if assistant.active_sessions:
            session_id = list(assistant.active_sessions.keys())[0]
            report = await assistant.get_progress_report(session_id)
            
            if "error" not in report:
                print(f"✅ 进度报告生成成功:")
                print(f"   项目: {report['project_name']}")
                print(f"   类型: {report['genre']}")
                print(f"   操作数: {report['statistics']['total_operations']}")
                print(f"   成功率: {report['statistics']['success_rate']:.1f}%")
            else:
                print(f"❌ 进度报告生成失败: {report['error']}")
        
        # 清理资源
        await shutdown_writing_assistant()
        
        print("\n🎉 AI集成与真实API测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_writing_workflow_with_ai():
    """测试AI驱动的写作工作流程"""
    print("\n🔄 测试AI驱动的写作工作流程...")
    print("=" * 60)
    
    try:
        from host_agent.google_adk_integration import WritingAssistantAgent
        
        # 创建写作助手
        assistant = WritingAssistantAgent()
        await assistant.initialize()
        
        # 定义一个完整的写作工作流程
        workflow_requests = [
            "创建一个现代都市小说项目",
            "设计故事的基本大纲",
            "创建男女主角的人物设定",
            "写一个开头场景"
        ]
        
        print(f"📋 工作流程: {len(workflow_requests)}个步骤")
        
        successful_steps = 0
        
        for i, request in enumerate(workflow_requests, 1):
            print(f"\n📝 步骤 {i}: {request}")
            
            try:
                response = await assistant.process_user_request(request)
                
                # 检查响应质量
                if "❌" not in response and len(response) > 50:
                    print(f"✅ 步骤 {i} 执行成功")
                    print(f"📄 响应预览: {response[:100]}...")
                    successful_steps += 1
                else:
                    print(f"⚠️ 步骤 {i} 执行有问题")
                    print(f"📄 响应: {response}")
                
            except Exception as e:
                print(f"❌ 步骤 {i} 执行失败: {e}")
            
            # 步骤间延迟
            await asyncio.sleep(1)
        
        # 工作流程总结
        success_rate = (successful_steps / len(workflow_requests)) * 100
        print(f"\n📊 工作流程执行结果:")
        print(f"   总步骤: {len(workflow_requests)}")
        print(f"   成功步骤: {successful_steps}")
        print(f"   成功率: {success_rate:.1f}%")
        
        if success_rate >= 75:
            print("✅ AI驱动的写作工作流程表现优秀")
        elif success_rate >= 50:
            print("⚠️ AI驱动的写作工作流程表现一般")
        else:
            print("❌ AI驱动的写作工作流程需要改进")
        
        print("🎉 AI驱动的写作工作流程测试完成!")
        return success_rate >= 50
        
    except Exception as e:
        print(f"❌ 工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_session_management_with_ai():
    """测试AI驱动的会话管理"""
    print("\n📝 测试AI驱动的会话管理...")
    print("=" * 60)
    
    try:
        from host_agent.google_adk_integration import WritingAssistantAgent
        
        assistant = WritingAssistantAgent()
        await assistant.initialize()
        
        # 测试多会话管理
        session_requests = [
            ("创建一个现代都市爱情小说项目", "现代都市"),
            ("开始一个玄幻修仙小说项目", "玄幻修仙"),
            ("新建一个悬疑推理小说项目", "悬疑推理")
        ]
        
        created_sessions = []
        
        for request, expected_genre in session_requests:
            print(f"\n🎯 请求: {request}")
            
            try:
                response = await assistant.process_user_request(request)
                print(f"🤖 响应: {response[:100]}...")
                
                # 检查是否创建了新会话
                current_sessions = len(assistant.active_sessions)
                if current_sessions > len(created_sessions):
                    new_session_id = list(assistant.active_sessions.keys())[-1]
                    created_sessions.append(new_session_id)
                    print(f"✅ 创建新会话: {new_session_id}")
                
            except Exception as e:
                print(f"❌ 请求处理失败: {e}")
        
        # 测试会话状态查询
        print(f"\n📊 会话管理测试结果:")
        print(f"   活动会话数: {len(assistant.active_sessions)}")
        
        for session_id, session_info in assistant.active_sessions.items():
            print(f"   - {session_id}: {session_info.get('project_name')} ({session_info.get('genre')})")
        
        # 测试会话切换和上下文维护
        if len(assistant.active_sessions) > 1:
            print("\n🔄 测试会话上下文维护...")
            
            # 模拟用户在不同会话间切换
            context_test = await assistant.before_model_callback({
                "messages": [{"role": "user", "content": "继续当前项目的开发"}]
            })
            
            if "session_context" in context_test:
                print("✅ 会话上下文维护正常")
                print(f"   当前会话: {context_test['session_context']['session_id']}")
            else:
                print("⚠️ 会话上下文维护有问题")
        
        print("🎉 AI驱动的会话管理测试完成!")
        return len(assistant.active_sessions) > 0
        
    except Exception as e:
        print(f"❌ 会话管理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    async def main():
        print("🚀 开始AI集成与真实API测试...")
        print("📋 验证第三方AI API与写作助手的完整集成")
        print()
        
        # 执行各项AI集成测试
        test_results = {}
        
        test_results["AI集成功能"] = await test_ai_integration_with_real_api()
        test_results["AI驱动工作流程"] = await test_writing_workflow_with_ai()
        test_results["AI会话管理"] = await test_session_management_with_ai()
        
        # 显示测试总结
        print("\n" + "="*60)
        print("📊 AI集成与真实API测试总结:")
        
        passed_tests = 0
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed_tests += 1
        
        success_rate = (passed_tests / len(test_results)) * 100
        print(f"\n📈 总体通过率: {passed_tests}/{len(test_results)} ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            print("\n🎉 AI集成与真实API测试优秀！")
            print("💡 系统具备:")
            print("   - 完整的第三方AI API集成能力")
            print("   - 智能的写作任务理解和执行")
            print("   - 有效的AI驱动工作流程管理")
            print("   - 强大的多会话AI交互能力")
        elif success_rate >= 60:
            print("\n✅ AI集成与真实API基本正常")
            print("💡 系统基本具备AI集成能力，部分功能需要优化")
        else:
            print("\n⚠️ AI集成与真实API需要改进")
            print("💡 建议检查API配置和网络连接")
    
    asyncio.run(main())