# Plot Agent MCP工具服务器

## 概述

Plot Agent是一个基于FastMCP的情节规划工具服务器，为网文写作助手系统提供专业的情节规划功能。

## 功能特性

### 🎯 核心工具

1. **analyze_classic_plot** - 经典情节深度分析 ⭐ **新增**
   - 基于19个经典情节模板的专业分析
   - 包含：探寻、爱情故事、复仇、成长、变形记等
   - 提供现代化改编建议和结构指导
   - 支持故事背景适配和个性化要求

2. **generate_story_outline** - 故事大纲生成
   - 支持多种类型：现代都市、古代言情、玄幻修仙等
   - 多种结构：三幕式、五幕式、英雄之旅等
   - 可定制长度和特殊要求
   - 集成经典情节理论指导

3. **create_chapter_structure** - 章节结构创建
   - 智能章节分配
   - 多种节奏风格：快节奏、稳步推进、慢热型
   - 详细的章节规划和字数目标

4. **analyze_plot_conflicts** - 情节冲突分析
   - 多维度冲突分析：人与人、人与自我、人与社会等
   - 张力点识别
   - 冲突解决建议

5. **suggest_plot_twists** - 情节转折建议
   - 多种转折类型：反转与惊喜、伏笔与悬念等
   - 时机把握建议
   - 悬念设置技巧

6. **get_plot_templates** - 模板列表获取
   - 完整的模板目录
   - 分类清晰的资源索引

### 📚 模板资源 ⭐ **大幅增强**

#### 🏛️ 经典情节模板 (19个)
基于专业文学理论的完整经典情节体系：
- **探寻类**: 探寻、探险、追逐、解救、逃跑
- **情感类**: 爱情故事、不伦之恋、牺牲
- **成长类**: 成长、转变、变形记、自我发现之旅
- **冲突类**: 复仇、对手戏、落魄之人
- **心理类**: 诱惑、可悲的无节制行为、盛衰沉浮
- **悬疑类**: 推理故事

#### 🏗️ 故事结构模板
- **英雄之旅**: 约瑟夫·坎贝尔的经典模式
- **三幕式结构**: 传统戏剧结构

#### 🎭 专业写作模板
- **剧情类型模板**: 爱情故事等经典类型
- **冲突类型模板**: 人与人的冲突等
- **转折技巧模板**: 反转与惊喜等

## 安装和使用

### 环境要求

```bash
pip install fastmcp pyyaml
```

### 启动服务器

```bash
# 方式1：作为模块启动
python -m plot_agent

# 方式2：直接运行
python plot_agent/__main__.py
```

### 测试功能

```bash
# 测试核心功能
python tests/test_plot_functions.py

# 测试MCP服务器
python tests/test_mcp_server.py
```

## 项目结构

```
plot_agent/
├── __init__.py
├── __main__.py          # 服务器启动脚本
├── plot_mcp.py          # MCP工具服务器实现
└── README.md            # 本文档

templates/story_structures/
├── hero_journey.yaml    # 英雄之旅模板
├── three_act.yaml       # 三幕式结构模板
└── plot/
    ├── 剧情模板说明.txt
    ├── 剧情类型/
    │   └── 爱情故事.txt
    ├── 剧情冲突/
    │   └── 人与人的冲突.txt
    └── 剧情转折/
        └── 反转与惊喜.txt

tests/
├── test_plot_functions.py  # 功能测试
└── test_mcp_server.py      # 服务器测试
```

## 使用示例

### 生成故事大纲

```python
result = generate_story_outline(
    genre="现代都市",
    theme="爱情",
    target_length="中篇",
    structure_type="三幕式",
    additional_requirements="包含职场元素"
)
```

### 创建章节结构

```python
result = create_chapter_structure(
    story_outline="故事大纲内容...",
    total_chapters=15,
    chapter_length="4000字",
    pacing_style="稳步推进"
)
```

### 经典情节深度分析 ⭐ **新功能**

```python
result = analyze_classic_plot(
    plot_type="探寻",
    story_context="一个年轻的考古学家寻找失落的古代文明遗迹，希望证明自己的理论",
    adaptation_requirements="现代背景，加入科技元素"
)
```

### 分析情节冲突

```python
result = analyze_plot_conflicts(
    story_context="故事背景描述...",
    conflict_types=["人与人", "人与自我", "人与社会"]
)
```

## 技术特点

- **模块化设计**: 每个工具功能独立，易于维护
- **模板驱动**: 基于丰富的模板资源，提供专业建议
- **错误处理**: 完善的异常处理机制
- **中文支持**: 全面支持中文内容处理
- **可扩展性**: 易于添加新的工具和模板

## 开发状态

✅ **已完成**:
- MCP服务器框架搭建
- 5个核心工具实现
- 模板资源系统
- 错误处理机制
- 测试用例编写
- 文档编写

🔄 **下一步**:
- 集成到A2A协议系统
- 与其他Agent的协作
- 性能优化
- 更多模板资源

## 注意事项

1. 确保模板文件路径正确
2. YAML文件需要使用UTF-8编码
3. 服务器启动后会监听MCP协议端口
4. 建议在虚拟环境中运行

## 贡献指南

欢迎贡献新的模板资源和功能改进：

1. 添加新的情节模板到 `templates/story_structures/plot/` 目录
2. 扩展现有工具的功能
3. 优化错误处理和用户体验
4. 添加更多测试用例

---

*Plot Agent是A2A多Agent协调系统的重要组成部分，专注于为网文创作提供专业的情节规划支持。*