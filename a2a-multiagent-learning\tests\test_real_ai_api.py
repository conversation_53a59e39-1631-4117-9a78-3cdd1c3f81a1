#!/usr/bin/env python3
"""
直接测试真实AI API的写作能力
验证AI集成是否正常工作
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_plot_agent_ai_directly():
    """直接测试Plot Agent的AI功能"""
    print("🎭 直接测试Plot Agent的AI功能...")
    print("=" * 60)
    
    try:
        # 导入Plot Agent
        from plot_agent.agent import PlotAgent
        
        # 创建Plot Agent实例
        agent = PlotAgent()
        
        # 检查AI客户端是否初始化成功
        if not agent.ai_client:
            print("❌ AI客户端未初始化，请检查环境变量配置")
            print("需要配置:")
            print("  OPENAI_API_KEY=your_api_key")
            print("  OPENAI_BASE_URL=your_base_url")
            print("  OPENAI_MODEL=your_model")
            return False
        
        print(f"✅ AI客户端初始化成功，模型: {agent.model}")
        
        # 测试案例1: 现代都市爱情故事大纲
        print("\n📝 测试案例1: 现代都市爱情故事大纲")
        print("-" * 40)
        
        query1 = "帮我设计一个现代都市爱情故事的大纲，男主是霸道总裁，女主是普通白领，要有误会、分离、重逢的情节"
        
        print(f"🎯 查询: {query1}")
        print("⏳ 正在生成...")
        
        result_chunks = []
        async for chunk in agent.stream(query1):
            content = chunk.get('content', '')
            if content:
                result_chunks.append(content)
                # 显示实时生成过程
                if len(result_chunks) % 5 == 0:  # 每5个chunk显示一次进度
                    print(f"📄 已生成 {len(result_chunks)} 个片段...")
            
            if chunk.get('done', False):
                break
        
        # 合并结果
        full_result = ''.join(result_chunks)
        
        if full_result:
            print("✅ 大纲生成成功!")
            print(f"📊 生成长度: {len(full_result)} 字符")
            print(f"📄 生成内容预览:")
            print("-" * 40)
            print(full_result[:500] + "..." if len(full_result) > 500 else full_result)
            print("-" * 40)
        else:
            print("❌ 大纲生成失败，没有返回内容")
            return False
        
        # 短暂延迟
        await asyncio.sleep(2)
        
        # 测试案例2: 情节冲突分析
        print("\n📝 测试案例2: 情节冲突分析")
        print("-" * 40)
        
        query2 = "分析一个悬疑推理小说的主要冲突类型，包括人与人的冲突、人与环境的冲突，以及如何安排冲突的升级"
        
        print(f"🎯 查询: {query2}")
        print("⏳ 正在分析...")
        
        result_chunks = []
        async for chunk in agent.stream(query2):
            content = chunk.get('content', '')
            if content:
                result_chunks.append(content)
            
            if chunk.get('done', False):
                break
        
        # 合并结果
        full_result = ''.join(result_chunks)
        
        if full_result:
            print("✅ 冲突分析成功!")
            print(f"📊 分析长度: {len(full_result)} 字符")
            print(f"📄 分析内容预览:")
            print("-" * 40)
            print(full_result[:500] + "..." if len(full_result) > 500 else full_result)
            print("-" * 40)
        else:
            print("❌ 冲突分析失败，没有返回内容")
            return False
        
        # 短暂延迟
        await asyncio.sleep(2)
        
        # 测试案例3: 情节转折建议
        print("\n📝 测试案例3: 情节转折建议")
        print("-" * 40)
        
        query3 = "我的玄幻修仙小说进展到中期，主角刚突破到筑基期，现在剧情有点平淡，需要一些意想不到的转折和悬念"
        
        print(f"🎯 查询: {query3}")
        print("⏳ 正在生成建议...")
        
        result_chunks = []
        async for chunk in agent.stream(query3):
            content = chunk.get('content', '')
            if content:
                result_chunks.append(content)
            
            if chunk.get('done', False):
                break
        
        # 合并结果
        full_result = ''.join(result_chunks)
        
        if full_result:
            print("✅ 转折建议成功!")
            print(f"📊 建议长度: {len(full_result)} 字符")
            print(f"📄 建议内容预览:")
            print("-" * 40)
            print(full_result[:500] + "..." if len(full_result) > 500 else full_result)
            print("-" * 40)
        else:
            print("❌ 转折建议失败，没有返回内容")
            return False
        
        print("\n🎉 Plot Agent AI功能测试完成!")
        print("✅ 所有测试案例都成功生成了高质量的内容")
        print("✅ AI API集成正常工作")
        
        return True
        
    except Exception as e:
        print(f"❌ AI功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ai_api_configuration():
    """测试AI API配置"""
    print("\n🔧 测试AI API配置...")
    print("=" * 60)
    
    try:
        # 检查环境变量
        api_key = os.getenv('OPENAI_API_KEY')
        base_url = os.getenv('OPENAI_BASE_URL')
        model = os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo')
        
        print("📋 环境变量检查:")
        print(f"   OPENAI_API_KEY: {'✅ 已设置' if api_key else '❌ 未设置'}")
        print(f"   OPENAI_BASE_URL: {base_url if base_url else '❌ 未设置'}")
        print(f"   OPENAI_MODEL: {model}")
        
        if not api_key:
            print("\n❌ 缺少必要的API配置")
            return False
        
        # 测试AI客户端创建
        try:
            from openai import AsyncOpenAI
            
            client = AsyncOpenAI(
                api_key=api_key,
                base_url=base_url
            )
            
            print("✅ AI客户端创建成功")
            
            # 测试简单的API调用
            print("🔍 测试API连接...")
            
            response = await client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": "请简单介绍一下你自己"}
                ],
                max_tokens=100
            )
            
            if response.choices:
                content = response.choices[0].message.content
                print("✅ API连接测试成功")
                print(f"📄 响应内容: {content[:100]}...")
                return True
            else:
                print("❌ API响应为空")
                return False
                
        except Exception as e:
            print(f"❌ AI客户端测试失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

async def test_writing_coordinator_with_real_ai():
    """测试写作协调器与真实AI的集成"""
    print("\n🤖 测试写作协调器与真实AI的集成...")
    print("=" * 60)
    
    try:
        # 先启动一个简化的Plot Agent服务器用于测试
        print("📝 创建测试用的Plot Agent...")
        
        from plot_agent.agent import PlotAgent
        
        # 创建Plot Agent实例
        plot_agent = PlotAgent()
        
        if not plot_agent.ai_client:
            print("❌ Plot Agent AI客户端未初始化")
            return False
        
        # 模拟写作协调器的请求
        test_requests = [
            {
                "type": "情节规划",
                "query": "设计一个现代都市小说的三幕结构",
                "expected_keywords": ["第一幕", "第二幕", "第三幕", "冲突", "高潮"]
            },
            {
                "type": "冲突分析", 
                "query": "分析古代言情小说中常见的情节冲突类型",
                "expected_keywords": ["冲突", "矛盾", "误会", "阻碍"]
            }
        ]
        
        successful_requests = 0
        
        for i, request in enumerate(test_requests, 1):
            print(f"\n📝 测试请求 {i}: {request['type']}")
            print(f"🎯 查询: {request['query']}")
            
            try:
                # 使用Plot Agent处理请求
                result_chunks = []
                async for chunk in plot_agent.stream(request['query']):
                    content = chunk.get('content', '')
                    if content:
                        result_chunks.append(content)
                    
                    if chunk.get('done', False):
                        break
                
                full_result = ''.join(result_chunks)
                
                if full_result:
                    # 检查结果质量
                    keyword_matches = sum(1 for keyword in request['expected_keywords'] 
                                        if keyword in full_result)
                    
                    print(f"✅ 请求处理成功")
                    print(f"📊 生成长度: {len(full_result)} 字符")
                    print(f"🎯 关键词匹配: {keyword_matches}/{len(request['expected_keywords'])}")
                    print(f"📄 内容预览: {full_result[:200]}...")
                    
                    if keyword_matches >= len(request['expected_keywords']) // 2:
                        successful_requests += 1
                        print("✅ 内容质量良好")
                    else:
                        print("⚠️ 内容质量一般")
                else:
                    print("❌ 请求处理失败，无返回内容")
                
            except Exception as e:
                print(f"❌ 请求处理异常: {e}")
            
            # 请求间延迟
            await asyncio.sleep(1)
        
        # 评估整体表现
        success_rate = (successful_requests / len(test_requests)) * 100
        print(f"\n📊 写作协调器AI集成测试结果:")
        print(f"   成功请求: {successful_requests}/{len(test_requests)}")
        print(f"   成功率: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("🎉 写作协调器与AI集成优秀!")
            return True
        elif success_rate >= 50:
            print("✅ 写作协调器与AI集成基本正常")
            return True
        else:
            print("❌ 写作协调器与AI集成需要改进")
            return False
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    async def main():
        print("🚀 开始真实AI API测试...")
        print("📋 验证AI集成是否正常工作")
        print()
        
        # 执行各项AI测试
        test_results = {}
        
        test_results["AI配置测试"] = await test_ai_api_configuration()
        test_results["Plot Agent AI功能"] = await test_plot_agent_ai_directly()
        test_results["写作协调器AI集成"] = await test_writing_coordinator_with_real_ai()
        
        # 显示测试总结
        print("\n" + "="*60)
        print("📊 真实AI API测试总结:")
        
        passed_tests = 0
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed_tests += 1
        
        success_rate = (passed_tests / len(test_results)) * 100
        print(f"\n📈 总体通过率: {passed_tests}/{len(test_results)} ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            print("\n🎉 真实AI API集成测试优秀！")
            print("💡 系统具备:")
            print("   - 正常的AI API连接和调用能力")
            print("   - 高质量的AI内容生成能力")
            print("   - 完整的写作协调器AI集成")
            print("   - 稳定的流式响应处理")
        elif success_rate >= 50:
            print("\n✅ 真实AI API集成基本正常")
            print("💡 系统基本具备AI写作能力，部分功能需要优化")
        else:
            print("\n⚠️ 真实AI API集成需要改进")
            print("💡 建议检查API配置和网络连接")
    
    asyncio.run(main())