#!/usr/bin/env python3
"""
Content Agent A2A服务器
基于A2A SDK的标准实现
"""

# 抑制websockets相关的DeprecationWarning
import warnings
warnings.filterwarnings("ignore", message="websockets.legacy is deprecated.*", category=DeprecationWarning)
warnings.filterwarnings("ignore", message="websockets.server.WebSocketServerProtocol is deprecated.*", category=DeprecationWarning)

import argparse
import os
import sys
from pathlib import Path
import uvicorn

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入配置管理器
try:
    from config.config_manager import get_config_manager
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False

from a2a.server.apps import A2AStarletteApplication
from a2a.server.request_handlers import DefaultRequestHandler
from a2a.server.tasks import InMemoryTaskStore
from a2a.types import (
    Agent<PERSON>apabilities,
    AgentCard,
    AgentSkill,
)
from .agent_executor import ContentAgentExecutor


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="Content Agent A2A服务器")
    parser.add_argument('--host', type=str, default='localhost', help='服务器主机地址')
    parser.add_argument('--port', type=int, default=10004, help='服务器端口号')
    parser.add_argument('--model', type=str, help='AI模型名称')
    parser.add_argument('--writing-mode', type=str, default='ai', help='写作模式')
    parser.add_argument('--config', type=str, help='配置文件路径')
    return parser.parse_args()


if __name__ == '__main__':
    # 解析命令行参数
    args = parse_arguments()
    
    # 加载配置
    config = None
    if CONFIG_AVAILABLE:
        config_manager = get_config_manager()
        config = config_manager.get_agent_config('content_agent')
        
        # 应用命令行覆盖
        if config:
            if args.host != 'localhost':
                config.host = args.host
            if args.port != 10004:
                config.port = args.port
            if args.model:
                config.model_name = args.model
            if args.writing_mode != 'ai':
                config.writing_mode = args.writing_mode
    
    # 确定最终配置
    host = config.host if config else args.host
    port = config.port if config else args.port
    model = config.model_name if config else (args.model or 'gemini-2.5-pro-preview-06-05')
    
    # 定义Content Agent的技能
    write_scene_skill = AgentSkill(
        id='write_scene',
        name='场景描写生成',
        description='根据用户需求生成生动详细的场景描写',
        tags=['场景描写', '环境描述', '氛围营造'],
        examples=[
            '生成一个现代都市咖啡厅的场景描写，营造浪漫氛围',
            '描写一个古代宫廷的花园场景，要有古典美感',
            '写一个玄幻修仙世界的洞府场景描写'
        ],
    )
    
    write_dialogue_skill = AgentSkill(
        id='write_dialogue',
        name='对话内容生成',
        description='生成符合角色性格的自然对话内容',
        tags=['对话生成', '人物对白', '角色交流'],
        examples=[
            '生成古代言情小说中男女主角的对话，表现初次相遇的情景',
            '创建一段现代都市小说中总裁和女秘书的办公室对话',
            '写一段玄幻小说中师徒之间的传功对话'
        ],
    )
    
    write_chapter_skill = AgentSkill(
        id='write_chapter',
        name='章节内容生成',
        description='根据情节要求生成完整的章节内容',
        tags=['章节生成', '情节创作', '内容创作'],
        examples=[
            '写一个现代都市小说的第一章，主角初到大城市',
            '创作一个古代言情小说的重逢章节',
            '生成一个玄幻小说的突破境界章节'
        ],
    )
    
    refine_content_skill = AgentSkill(
        id='refine_content',
        name='内容优化润色',
        description='优化和润色已有的文本内容',
        tags=['内容优化', '文本润色', '质量提升'],
        examples=[
            '优化这段场景描写，使其更加生动',
            '润色这段对话，使其更符合角色性格',
            '完善这个章节的情节连贯性'
        ],
    )
    
    get_writing_templates_skill = AgentSkill(
        id='get_writing_templates',
        name='写作风格模板',
        description='提供不同类型的写作风格模板和指导',
        tags=['写作风格', '模板参考', '创作指导'],
        examples=[
            '显示可用的写作风格模板',
            '提供现代都市风格的写作指导',
            '查看玄幻修仙类型的写作技巧'
        ],
    )
    
    analyze_content_skill = AgentSkill(
        id='analyze_content',
        name='内容分析评估',
        description='分析评估文本内容的质量和特点',
        tags=['内容分析', '质量评估', '风格分析'],
        examples=[
            '分析这段文本的写作风格',
            '评估这个章节的情节结构',
            '检查这段对话的自然度'
        ],
    )

    # 创建Agent卡片
    agent_card = AgentCard(
        name='Content Agent - 内容创作专家',
        description='专业的网文内容生成Agent，集成AI智能分析和写作风格模板，提供场景描写、对话内容、章节创作等全方位的内容创作服务',
        url=f'http://{host}:{port}/',
        version='1.0.0',
        defaultInputModes=['text'],
        defaultOutputModes=['text'],
        capabilities=AgentCapabilities(streaming=True),
        skills=[
            write_scene_skill,
            write_dialogue_skill,
            write_chapter_skill,
            refine_content_skill,
            get_writing_templates_skill,
            analyze_content_skill
        ],
    )

    # 创建请求处理器
    request_handler = DefaultRequestHandler(
        agent_executor=ContentAgentExecutor(),
        task_store=InMemoryTaskStore(),
    )

    # 创建A2A服务器应用
    server = A2AStarletteApplication(
        agent_card=agent_card,
        http_handler=request_handler
    )

    # 启动服务器
    print("启动Content Agent A2A服务器...")
    print(f"服务器地址: http://{host}:{port}")
    print(f"AI模型: {model}")
    print(f"写作模式: {config.writing_mode if config else args.writing_mode}")
    print("Agent技能:")
    for skill in agent_card.skills:
        print(f"   - {skill.name}: {skill.description}")
    
    uvicorn.run(server.build(), host=host, port=port)