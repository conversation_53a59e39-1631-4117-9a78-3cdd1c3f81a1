#!/usr/bin/env python3
"""
启动Character Agent A2A服务器的便捷脚本
"""

import sys
import subprocess
from pathlib import Path

def start_character_agent():
    """启动Character Agent服务器"""
    print("👤 启动Character Agent A2A服务器...")
    
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    
    try:
        # 切换到character_agent目录并启动服务器
        cmd = [sys.executable, "-m", "character_agent"]
        subprocess.run(cmd, cwd=project_root, check=True)
    except KeyboardInterrupt:
        print("\n👤 Character Agent服务器已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    start_character_agent()