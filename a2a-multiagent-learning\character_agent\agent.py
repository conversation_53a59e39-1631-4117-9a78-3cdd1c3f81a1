#!/usr/bin/env python3
"""
Character Agent核心逻辑
集成AI API和角色原型模板，提供智能角色设定服务
"""

import os
import sys
import json
import re
import asyncio
from collections.abc import AsyncGenerator
from typing import Any, Dict, List
from pathlib import Path

# 导入AI API
try:
    from openai import AsyncOpenAI
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False
    print("OpenAI库未安装，将使用基础模式")

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("python-dotenv库未安装")

# 导入角色数据模型
from .character_models import (
    Character, CharacterResponseFormat, RelationshipNetwork,
    CharacterDevelopmentPlan, GenderType, AgeGroup
)

# 加载角色原型模板
def load_character_archetypes():
    """加载角色原型模板"""
    try:
        template_path = Path(__file__).parent.parent / 'templates' / 'character_archetypes' / 'character'
        archetypes = {
            'male': {},
            'female': {},
            'supporting': {},
            'description': ''
        }
        
        # 读取角色原型说明
        desc_file = template_path / '角色原型说明.txt'
        if desc_file.exists():
            with open(desc_file, 'r', encoding='utf-8') as f:
                archetypes['description'] = f.read()
        
        # 读取男性角色原型
        male_path = template_path / 'male character'
        if male_path.exists():
            for file in male_path.glob('*.txt'):
                archetype_name = file.stem
                with open(file, 'r', encoding='utf-8') as f:
                    archetypes['male'][archetype_name] = f.read()
        
        # 读取女性角色原型
        female_path = template_path / 'female character'
        if female_path.exists():
            for file in female_path.glob('*.txt'):
                archetype_name = file.stem
                with open(file, 'r', encoding='utf-8') as f:
                    archetypes['female'][archetype_name] = f.read()
        
        # 读取配角原型
        supporting_path = template_path / 'supporting role'
        if supporting_path.exists():
            for file in supporting_path.glob('*.txt'):
                archetype_name = file.stem
                with open(file, 'r', encoding='utf-8') as f:
                    archetypes['supporting'][archetype_name] = f.read()
        
        return archetypes
        
    except Exception as e:
        print(f"加载角色原型模板失败: {e}")
        return {'male': {}, 'female': {}, 'supporting': {}, 'description': ''}

# 全局角色原型模板
CHARACTER_ARCHETYPES = load_character_archetypes()


class CharacterAgent:
    """Character Agent核心类 - 集成AI API和角色原型模板的智能角色设定"""

    def __init__(self):
        """初始化Character Agent"""
        self.name = "Character Agent"
        self.description = "专业的网文角色设定Agent，集成AI智能分析和角色原型模板"
        
        # 初始化AI客户端
        self._init_ai_client()
        
        # 加载角色原型模板
        self.character_archetypes = CHARACTER_ARCHETYPES
        if self.character_archetypes:
            male_count = len(self.character_archetypes.get('male', {}))
            female_count = len(self.character_archetypes.get('female', {}))
            supporting_count = len(self.character_archetypes.get('supporting', {}))
            print(f"已加载角色原型模板: 男性{male_count}个, 女性{female_count}个, 配角{supporting_count}个")
        else:
            print("角色原型模板加载失败")
        
        # 技能映射
        self.skills_map = {
            'create_character': self._handle_create_character,
            'get_character_archetypes': self._handle_character_archetypes,
            'build_relationship_network': self._handle_relationship_network,
            'character_development': self._handle_character_development,
            'analyze_character_conflicts': self._handle_character_conflicts,
            'suggest_character_improvements': self._handle_character_improvements,
        }

    def _init_ai_client(self):
        """初始化AI客户端"""
        self.ai_client = None
        
        if not AI_AVAILABLE:
            print("AI功能不可用，将使用基础模式")
            return
            
        try:
            # 从环境变量获取配置
            api_key = os.getenv('OPENAI_API_KEY')
            base_url = os.getenv('OPENAI_BASE_URL')
            self.model = os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo')
            
            if not api_key:
                print("未找到OPENAI_API_KEY，将使用基础模式")
                return
            
            # 创建AI客户端
            self.ai_client = AsyncOpenAI(
                api_key=api_key,
                base_url=base_url
            )
            print(f"Character Agent AI客户端初始化成功，模型: {self.model}")
            
        except Exception as e:
            print(f"Character Agent AI客户端初始化失败: {e}，将使用基础模式")
            self.ai_client = None

    async def stream(self, query: str) -> AsyncGenerator[dict[str, Any], None]:
        """流式处理用户查询"""
        try:
            # 分析用户意图并选择合适的处理方法
            skill_id, params = self._analyze_intent(query)
            
            if skill_id in self.skills_map:
                # 调用对应的技能处理函数
                async for chunk in self.skills_map[skill_id](params):
                    yield chunk
            else:
                # 通用角色设定建议
                async for chunk in self._handle_general_character_advice(query):
                    yield chunk
                    
        except Exception as e:
            yield {
                'content': f'处理请求时出错: {str(e)}',
                'done': True,
            }

    def _analyze_intent(self, query: str) -> tuple[str, dict]:
        """分析用户意图，返回技能ID和参数"""
        query_lower = query.lower()
        
        # 角色原型查询
        if any(keyword in query_lower for keyword in ['原型', '模板', '类型', '参考']):
            params = self._extract_archetype_params(query)
            return 'get_character_archetypes', params
            
        # 角色创建
        elif any(keyword in query_lower for keyword in ['创建角色', '设计角色', '角色设定', '新角色']):
            params = self._extract_character_params(query)
            return 'create_character', params
            
        # 关系网络
        elif any(keyword in query_lower for keyword in ['关系', '人物关系', '角色关系', '关系网']):
            params = self._extract_relationship_params(query)
            return 'build_relationship_network', params
            
        # 角色发展
        elif any(keyword in query_lower for keyword in ['成长', '发展', '角色弧线', '角色成长']):
            params = self._extract_development_params(query)
            return 'character_development', params
            
        # 角色冲突
        elif any(keyword in query_lower for keyword in ['冲突', '矛盾', '对立', '角色冲突']):
            params = self._extract_conflict_params(query)
            return 'analyze_character_conflicts', params
            
        # 角色优化
        elif any(keyword in query_lower for keyword in ['优化', '改进', '完善', '建议']):
            params = self._extract_improvement_params(query)
            return 'suggest_character_improvements', params
            
        else:
            # 默认为角色创建
            params = self._extract_character_params(query)
            return 'create_character', params

    def _extract_archetype_params(self, query: str) -> dict:
        """从查询中提取原型参数"""
        params = {
            'gender': '',
            'archetype_type': '',
            'story_context': query,
            'enhance': True
        }
        
        # 提取性别
        if any(word in query for word in ['男性', '男主', '男角色', '男人']):
            params['gender'] = 'male'
        elif any(word in query for word in ['女性', '女主', '女角色', '女人']):
            params['gender'] = 'female'
        elif any(word in query for word in ['配角', '支持角色', '次要角色']):
            params['gender'] = 'supporting'
        
        # 提取原型类型关键词
        archetype_patterns = {
            '国王': ['国王', '君主', '统治者', '领导者'],
            '救世主': ['救世主', '英雄', '拯救者'],
            '父亲的女儿': ['父亲的女儿', '职场女性', '独立女性'],
            '亚马逊女子': ['亚马逊', '女战士', '强势女性'],
            '养育者': ['养育者', '母亲', '照顾者'],
            '少女': ['少女', '纯真', '青春'],
        }
        
        for archetype, keywords in archetype_patterns.items():
            if any(keyword in query for keyword in keywords):
                params['archetype_type'] = archetype
                break
        
        return params

    def _extract_character_params(self, query: str) -> dict:
        """从查询中提取角色参数"""
        params = {
            'character_type': '',
            'gender': '',
            'age_group': '',
            'role_type': '',
            'story_context': query,
            'special_requirements': '',
            'use_archetype': True
        }
        
        # 提取角色类型
        character_patterns = {
            '霸道总裁': ['霸道总裁', '总裁', 'CEO'],
            '校园学霸': ['学霸', '学生会长', '校草'],
            '古代王爷': ['王爷', '皇子', '世子'],
            '现代医生': ['医生', '大夫', '医师'],
            '军人': ['军人', '军官', '特种兵'],
            '明星': ['明星', '演员', '歌手'],
        }
        
        for char_type, keywords in character_patterns.items():
            if any(keyword in query for keyword in keywords):
                params['character_type'] = char_type
                break
        
        # 提取性别
        if any(word in query for word in ['男主', '男性', '男人', '他']):
            params['gender'] = '男'
        elif any(word in query for word in ['女主', '女性', '女人', '她']):
            params['gender'] = '女'
        
        return params

    def _extract_relationship_params(self, query: str) -> dict:
        """从查询中提取关系参数"""
        return {
            'characters': [],
            'relationship_context': query,
            'focus_type': '全面分析'
        }

    def _extract_development_params(self, query: str) -> dict:
        """从查询中提取发展参数"""
        return {
            'character_name': '',
            'development_context': query,
            'growth_focus': '全面成长'
        }

    def _extract_conflict_params(self, query: str) -> dict:
        """从查询中提取冲突参数"""
        return {
            'characters': [],
            'conflict_context': query,
            'conflict_type': '人物冲突'
        }

    def _extract_improvement_params(self, query: str) -> dict:
        """从查询中提取优化参数"""
        return {
            'character_info': query,
            'improvement_focus': '全面优化'
        }

    async def _handle_character_archetypes(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """处理角色原型查询"""
        yield {'content': '正在获取角色原型模板...\n\n', 'done': False}
        
        try:
            gender = params.get('gender', '')
            archetype_type = params.get('archetype_type', '')
            
            if gender and gender in self.character_archetypes:
                archetypes = self.character_archetypes[gender]
                
                if archetype_type:
                    # 查找特定原型
                    matching_archetypes = {}
                    for name, content in archetypes.items():
                        if archetype_type in name or archetype_type in content:
                            matching_archetypes[name] = content
                    
                    if matching_archetypes:
                        result = f"## {archetype_type}相关原型\n\n"
                        for name, content in matching_archetypes.items():
                            result += f"### {name}\n\n"
                            # 提取前500字符作为预览
                            preview = content[:500] + "..." if len(content) > 500 else content
                            result += f"{preview}\n\n---\n\n"
                    else:
                        result = f"未找到与'{archetype_type}'相关的原型。\n\n可用原型: {', '.join(archetypes.keys())}"
                else:
                    # 返回该性别的所有原型概述
                    result = f"## {gender.title()}角色原型概述\n\n"
                    for name in archetypes.keys():
                        result += f"- **{name}**\n"
                    result += f"\n共{len(archetypes)}个原型。请指定具体原型名称以获取详细信息。"
            else:
                # 返回所有类型概述
                result = "## 角色原型模板概述\n\n"
                result += f"### 男性角色原型 ({len(self.character_archetypes.get('male', {}))}个)\n"
                result += ", ".join(self.character_archetypes.get('male', {}).keys()) + "\n\n"
                result += f"### 女性角色原型 ({len(self.character_archetypes.get('female', {}))}个)\n"
                result += ", ".join(self.character_archetypes.get('female', {}).keys()) + "\n\n"
                result += f"### 配角原型 ({len(self.character_archetypes.get('supporting', {}))}个)\n"
                result += ", ".join(self.character_archetypes.get('supporting', {}).keys()) + "\n\n"
                result += "\n请指定性别和原型类型以获取详细信息。"
                
            # 如果有AI客户端，使用AI增强原型建议
            if self.ai_client and params.get('enhance', True):
                async for chunk in self._ai_enhanced_archetype_advice(result, params):
                    yield chunk
            else:
                # 直接返回模板结果
                chunks = self._split_content(result)
                for chunk in chunks:
                    yield {'content': chunk, 'done': False}
                
        except Exception as e:
            yield {'content': f'获取角色原型时出错: {str(e)}', 'done': False}
        
        yield {'content': '', 'done': True}

    async def _ai_enhanced_archetype_advice(self, template_data: str, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """AI增强的角色原型建议"""
        try:
            story_context = params.get('story_context', '')
            
            prompt = f"""作为专业的角色设计专家，请基于以下角色原型模板数据，为用户提供个性化的角色设计建议：

【角色原型模板数据】
{template_data}

【用户需求】
{story_context}

请提供：
1. 最适合的2-3个角色原型推荐，并解释为什么这些原型适合用户的需求
2. 每个推荐原型的个性化调整建议，使其更符合用户的具体故事
3. 角色发展建议，包括成长弧线和关键转折点
4. 如何避免该类型角色的常见刻板印象
5. 该角色与其他角色可能的互动模式

要求：
- 建议具体可操作
- 符合故事类型特点
- 避免过于刻板的角色设定
- 注重角色的立体性和成长性"""

            # 调用AI API
            response = await self.ai_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一位专业的角色设计专家，擅长为各类故事创作提供角色设计建议。"},
                    {"role": "user", "content": prompt}
                ],
                stream=True,
                temperature=0.7
            )
            
            yield {'content': '🤖 AI正在分析角色原型并提供个性化建议...\n\n', 'done': False}
            
            # 流式返回AI响应
            async for chunk in response:
                if chunk.choices[0].delta.content:
                    yield {'content': chunk.choices[0].delta.content, 'done': False}
                    
        except Exception as e:
            yield {'content': f'AI增强建议生成失败: {str(e)}\n\n', 'done': False}
            
            # 回退到模板数据
            chunks = self._split_content(f"AI增强失败，返回基础模板数据:\n\n{template_data}")
            for chunk in chunks:
                yield {'content': chunk, 'done': False}

    async def _handle_create_character(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """处理角色创建 - 集成AI增强和原型模板"""
        yield {'content': '👤 正在创建角色...\n\n', 'done': False}
        
        try:
            # 如果有AI客户端，使用AI增强生成
            if self.ai_client:
                async for chunk in self._ai_enhanced_character_creation(params):
                    yield chunk
            else:
                # 使用基础模板生成
                result = await self._basic_character_creation(params)
                chunks = self._split_content(result)
                for chunk in chunks:
                    yield {'content': chunk, 'done': False}
                
        except Exception as e:
            yield {'content': f'创建角色时出错: {str(e)}', 'done': False}
        
        yield {'content': '', 'done': True}

    async def _ai_enhanced_character_creation(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """AI增强的角色创建，结合原型模板"""
        try:
            # 获取相关的角色原型模板
            archetype_context = ""
            if params.get('use_archetype', True):
                gender_key = 'male' if params.get('gender') == '男' else 'female'
                if gender_key in self.character_archetypes:
                    archetypes = self.character_archetypes[gender_key]
                    archetype_context = f"\n\n【可参考的角色原型】\n"
                    for name, content in list(archetypes.items())[:3]:  # 取前3个作为参考
                        archetype_context += f"**{name}**:\n{content[:300]}...\n\n"
            
            # 构建AI提示词
            prompt = f"""作为专业的网文角色设定专家，请为以下要求创建详细的角色档案：

角色类型：{params.get('character_type', '未指定')}
性别：{params.get('gender', '未指定')}
年龄组：{params.get('age_group', '未指定')}
角色定位：{params.get('role_type', '未指定')}
故事背景：{params.get('story_context', '')}
特殊要求：{params.get('special_requirements', '无')}

{archetype_context}

请创建一个包含以下内容的详细角色档案：
1. 基本信息（姓名、年龄、性别、职业等）
2. 外貌描述（身高、体型、发色、眼色、穿衣风格等）
3. 性格特征（主要性格特点、优缺点、行为习惯）
4. 背景故事（家庭背景、教育经历、关键经历）
5. 内在动机（目标、恐惧、价值观）
6. 对话风格（说话特点、口头禅）
7. 角色成长弧线（起点、挑战、转变、终点）
8. 与其他角色的潜在关系

要求：
- 角色设定丰富立体，避免脸谱化
- 符合网文读者喜好和类型特点
- 性格有层次，有优点也有缺点
- 背景合理，有成长空间
- 可以参考提供的角色原型，但要有创新和个性化"""

            # 调用AI API
            response = await self.ai_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一位专业的网文角色设定专家，擅长创造立体丰满的角色形象。你熟悉各种角色原型，能够在参考经典原型的基础上创造出独特的角色。"},
                    {"role": "user", "content": prompt}
                ],
                stream=True,
                temperature=0.7
            )
            
            yield {'content': '🤖 AI正在结合角色原型模板创建角色...\n\n', 'done': False}
            
            # 流式返回AI响应
            async for chunk in response:
                if chunk.choices[0].delta.content:
                    yield {'content': chunk.choices[0].delta.content, 'done': False}
                    
        except Exception as e:
            yield {'content': f'AI增强创建失败，使用基础模式: {str(e)}\n\n', 'done': False}
            
            # 回退到基础模式
            result = await self._basic_character_creation(params)
            chunks = self._split_content(result)
            for chunk in chunks:
                yield {'content': chunk, 'done': False}

    async def _basic_character_creation(self, params: dict) -> str:
        """基础角色创建（无AI时使用）"""
        result = f"## 角色档案\n\n"
        result += f"**角色类型**: {params.get('character_type', '未指定')}\n"
        result += f"**性别**: {params.get('gender', '未指定')}\n"
        result += f"**故事背景**: {params.get('story_context', '无')}\n\n"
        
        # 根据性别推荐相关原型
        gender_key = 'male' if params.get('gender') == '男' else 'female'
        if gender_key in self.character_archetypes:
            archetypes = self.character_archetypes[gender_key]
            result += f"### 推荐参考原型\n\n"
            for name in list(archetypes.keys())[:3]:
                result += f"- **{name}**\n"
            result += f"\n请查看具体原型详情以获得更多灵感。"
        
        return result

    def _split_content(self, content: str, chunk_size: int = 200) -> List[str]:
        """将长内容分割成小块"""
        if len(content) <= chunk_size:
            return [content]
        
        chunks = []
        for i in range(0, len(content), chunk_size):
            chunks.append(content[i:i + chunk_size])
        return chunks

    # 其他处理函数的占位符实现
    async def _handle_relationship_network(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """处理关系网络分析"""
        yield {'content': '🔗 关系网络分析功能开发中...', 'done': True}

    async def _handle_character_development(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """处理角色发展规划"""
        yield {'content': '📈 角色发展规划功能开发中...', 'done': True}

    async def _handle_character_conflicts(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """处理角色冲突分析"""
        yield {'content': '⚔️ 角色冲突分析功能开发中...', 'done': True}

    async def _handle_character_improvements(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """处理角色优化建议"""
        yield {'content': '💡 角色优化建议功能开发中...', 'done': True}

    async def _handle_general_character_advice(self, query: str) -> AsyncGenerator[dict[str, Any], None]:
        """处理通用角色建议"""
        yield {'content': f'💭 收到查询: {query}\n\n通用角色建议功能开发中...', 'done': True}