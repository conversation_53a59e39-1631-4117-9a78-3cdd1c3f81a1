{"timestamp": "2025-07-17T06:59:18.513501", "system_stats": {"timestamp": "2025-07-17T06:59:18.511501", "agents": {"total": 4, "online": 4, "offline": 0}, "tasks": {"total": 5, "completed": 4, "failed": 1, "success_rate": 80.0}, "communication": {"total_requests": 10, "total_errors": 4, "error_rate": 40.0, "avg_response_time": 910.3980274502467}, "events": {"total": 57, "window_hours": 24.0}}, "agent_metrics": {"host_agent": {"name": "host_agent", "status": "online", "total_requests": 2, "successful_requests": 1, "failed_requests": 1, "error_count": 1, "avg_response_time": 297.5625896535414, "last_activity": "2025-07-17T06:59:04.007935"}, "content_agent": {"name": "content_agent", "status": "online", "total_requests": 5, "successful_requests": 2, "failed_requests": 2, "error_count": 2, "avg_response_time": 468.8980210956423, "last_activity": "2025-07-17T06:59:03.831355"}, "character_agent": {"name": "character_agent", "status": "online", "total_requests": 2, "successful_requests": 2, "failed_requests": 1, "error_count": 1, "avg_response_time": 1378.054377642633, "last_activity": "2025-07-17T06:59:03.502741"}, "plot_agent": {"name": "plot_agent", "status": "online", "total_requests": 1, "successful_requests": 1, "failed_requests": 0, "error_count": 0, "avg_response_time": 1497.0771214091703, "last_activity": "2025-07-17T06:59:02.265980"}}, "recent_events": [{"timestamp": "2025-07-17T06:59:18.507503", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_4", "status": "completed", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:18.507503", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_4", "status": "completed", "duration_ms": 3839.813, "error_message": null}, {"timestamp": "2025-07-17T06:59:17.808449", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_4", "status": "working", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:17.295669", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_4", "status": "working", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:16.598995", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_4", "status": "working", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:16.252930", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_4", "status": "working", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:15.573189", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_4", "status": "working", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:14.875704", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_4", "status": "working", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:14.667690", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_4", "status": "started", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:14.666689", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_3", "status": "completed", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:14.666689", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_3", "status": "completed", "duration_ms": 2266.9649999999997, "error_message": null}, {"timestamp": "2025-07-17T06:59:13.885237", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_3", "status": "working", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:13.432995", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_3", "status": "working", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:13.028109", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_3", "status": "working", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:12.399724", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_2", "status": "completed", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:12.399724", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_2", "status": "completed", "duration_ms": 3653.926, "error_message": null}, {"timestamp": "2025-07-17T06:59:12.399724", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_3", "status": "started", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:12.025827", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_2", "status": "working", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:11.626858", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_2", "status": "working", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:10.995128", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_2", "status": "working", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:10.307558", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_2", "status": "working", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:09.556265", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_2", "status": "working", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:09.299127", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_2", "status": "working", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:08.745798", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_1", "status": "completed", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:08.745798", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_1", "status": "failed", "duration_ms": 1874.49, "error_message": null}, {"timestamp": "2025-07-17T06:59:08.745798", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_2", "status": "started", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:08.040975", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_1", "status": "working", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:07.451001", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_1", "status": "working", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:06.871308", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_1", "status": "started", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:06.870308", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_0", "status": "completed", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:06.870308", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_0", "status": "completed", "duration_ms": 2862.373, "error_message": null}, {"timestamp": "2025-07-17T06:59:06.503148", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_0", "status": "working", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:06.121969", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_0", "status": "working", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:05.552575", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_0", "status": "working", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:04.887222", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_0", "status": "working", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:04.646634", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_0", "status": "working", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:04.007935", "event_type": "error", "source_agent": "host_agent", "target_agent": "character_agent", "task_id": "test_task_9", "status": "error", "duration_ms": null, "error_message": "测试错误 9"}, {"timestamp": "2025-07-17T06:59:04.007935", "event_type": "status_change", "source_agent": "system", "target_agent": null, "task_id": "complex_task_0", "status": "started", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:03.831355", "event_type": "response", "source_agent": "content_agent", "target_agent": "character_agent", "task_id": "test_task_8", "status": "success", "duration_ms": 1684.687480740324, "error_message": null}, {"timestamp": "2025-07-17T06:59:03.831355", "event_type": "request", "source_agent": "host_agent", "target_agent": "character_agent", "task_id": "test_task_9", "status": "sent", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:03.502741", "event_type": "error", "source_agent": "character_agent", "target_agent": "host_agent", "task_id": "test_task_7", "status": "error", "duration_ms": null, "error_message": "测试错误 7"}, {"timestamp": "2025-07-17T06:59:03.502741", "event_type": "request", "source_agent": "character_agent", "target_agent": "content_agent", "task_id": "test_task_8", "status": "sent", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:03.006985", "event_type": "response", "source_agent": "host_agent", "target_agent": "content_agent", "task_id": "test_task_6", "status": "success", "duration_ms": 297.5625896535414, "error_message": null}, {"timestamp": "2025-07-17T06:59:03.006985", "event_type": "request", "source_agent": "character_agent", "target_agent": "host_agent", "task_id": "test_task_7", "status": "sent", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:02.629474", "event_type": "response", "source_agent": "character_agent", "target_agent": "plot_agent", "task_id": "test_task_5", "status": "success", "duration_ms": 1006.8176259028688, "error_message": null}, {"timestamp": "2025-07-17T06:59:02.629474", "event_type": "request", "source_agent": "content_agent", "target_agent": "host_agent", "task_id": "test_task_6", "status": "sent", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:02.265980", "event_type": "response", "source_agent": "plot_agent", "target_agent": "content_agent", "task_id": "test_task_4", "status": "success", "duration_ms": 1497.0771214091703, "error_message": null}, {"timestamp": "2025-07-17T06:59:02.265980", "event_type": "request", "source_agent": "plot_agent", "target_agent": "character_agent", "task_id": "test_task_5", "status": "sent", "duration_ms": null, "error_message": null}, {"timestamp": "2025-07-17T06:59:01.943123", "event_type": "error", "source_agent": "content_agent", "target_agent": "character_agent", "task_id": "test_task_3", "status": "error", "duration_ms": null, "error_message": "测试错误 3"}, {"timestamp": "2025-07-17T06:59:01.943123", "event_type": "request", "source_agent": "content_agent", "target_agent": "plot_agent", "task_id": "test_task_4", "status": "sent", "duration_ms": null, "error_message": null}]}