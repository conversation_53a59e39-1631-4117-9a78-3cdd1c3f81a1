# A2A多Agent协调系统依赖包 - 网文写作助手

# A2A核心依赖
a2a-sdk>=0.2.12
google-adk>=1.0.0
google-genai>=1.9.0

# LangGraph和Lang<PERSON><PERSON><PERSON> (用于Character和Content Agent)
langgraph>=0.4.1
langchain-google-genai>=2.1.4
langchain-core>=0.3.0
langchain-openai>=0.1.0

# OpenAI兼容API支持
openai>=1.0.0

# MCP协议支持 (用于Plot Agent工具)
mcp>=1.0.0
fastmcp>=0.1.0

# HTTP客户端和服务器
httpx>=0.28.1
uvicorn>=0.34.2
starlette>=0.41.0

# 数据处理和验证
pydantic>=2.11.4
python-dotenv>=1.1.0

# 文本处理和NLP (写作相关)
nltk>=3.8.0
spacy>=3.7.0
jieba>=0.42.1  # 中文分词 (如果需要中文写作支持)

# 异步支持
asyncio-mqtt>=0.16.0
nest-asyncio>=1.6.0

# 开发和测试工具
pytest>=8.0.0
pytest-asyncio>=0.24.0
black>=24.0.0
ruff>=0.7.0

# 日志和监控
structlog>=24.0.0
rich>=13.0.0

# CLI工具
click>=8.1.8
asyncclick>=8.1.8

# 文件处理 (用于写作模板和内容管理)
pyyaml>=6.0.0
jinja2>=3.1.0

# 可选：Jupyter支持 (用于交互式学习)
jupyter>=1.1.0
ipykernel>=6.29.0

# 可选：Web UI支持
streamlit>=1.28.0
gradio>=4.0.0