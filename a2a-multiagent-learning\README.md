# A2A多Agent协调系统学习项目 - 网文写作助手

## 项目概述

本项目用于学习和实现Google A2A (Agent-to-Agent) 协议中的多agent协调模式，构建一个网文写作助手系统。系统包含情节规划Agent、角色设定Agent和内容生成Agent，通过Host Agent智能协调来帮助作者创作网络小说。

## 学习目标

通过实际编码和运行，深入理解：
- A2A协议的多agent通信机制
- Host Agent的智能路由和协调逻辑
- MCP (Model Context Protocol) 工具集成
- 不同AI框架 (ADK, LangGraph) 的A2A适配方法
- 多agent协作完成复杂创作任务的模式

## 项目结构

```
a2a-multiagent-learning/
├── README.md                 # 项目说明文档
├── requirements.txt          # Python依赖包列表
├── .env.example             # 环境变量模板
├── plot_agent/              # 情节规划Agent
│   ├── __main__.py
│   ├── plot_agent.py
│   ├── plot_mcp.py
│   └── plot_executor.py
├── character_agent/         # 角色设定Agent
│   ├── __main__.py
│   ├── character_agent.py
│   └── character_executor.py
├── content_agent/           # 内容生成Agent
│   ├── __main__.py
│   ├── content_agent.py
│   └── content_executor.py
├── host_agent/             # 写作协调Host Agent
│   ├── __main__.py
│   ├── writing_coordinator.py
│   └── remote_agent_connection.py
├── templates/              # 写作模板和资源
│   ├── story_structures/   # 故事结构模板
│   ├── character_archetypes/ # 角色原型
│   └── writing_styles/     # 文体风格
├── tests/                  # 测试用例
│   ├── test_plot.py
│   ├── test_character.py
│   ├── test_content.py
│   └── test_integration.py
├── docs/                   # 学习笔记和文档
│   ├── learning_notes.md
│   ├── architecture.md
│   └── troubleshooting.md
└── scripts/               # 辅助脚本
    ├── start_all.sh
    ├── test_writing_system.py
    └── monitor.py
```

## 快速开始

### 1. 环境准备
```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入你的API密钥
```

### 2. 启动系统
```bash
# 启动Plot Agent (终端1)
cd plot_agent
python -m plot_agent

# 启动Character Agent (终端2)  
cd character_agent
python -m character_agent

# 启动Content Agent (终端3)
cd content_agent
python -m content_agent

# 启动Host Agent (终端4)
cd host_agent
python -m host_agent
```

### 3. 测试系统

#### 真实AI API测试（推荐）
```bash
# 一键运行完整的真实AI测试套件
python scripts/run_real_ai_tests.py

# 或手动运行各种测试
python tests/test_real_ai_integration.py    # 真实AI集成测试
python scripts/test_writing_system.py       # 端到端系统测试
python tests/test_cases.py                  # 具体测试用例
```

#### CLI客户端测试
```bash
# 使用CLI客户端测试
cd ../a2a-samples/samples/python/hosts/cli
uv run . --agent http://localhost:10001

# 测试示例：
# "帮我设计一个现代都市爱情故事的大纲"
# "为我的小说创建一个霸道总裁男主角"
# "根据大纲写第一章的开头场景"
```

**注意**: 真实AI测试需要配置API密钥，详见 [真实AI测试指南](docs/REAL_AI_TESTING_GUIDE.md)

## 学习进度追踪

- [x] 任务1: 环境准备和项目结构搭建
- [x] 任务2: 实现Plot Agent的MCP工具服务器  
- [x] 任务3: 构建Plot Agent的A2A服务器 ✅ **已完成并AI增强**
- [ ] 任务4: 开发Character Agent的核心逻辑
- [ ] 任务5: 构建Character Agent的A2A适配器
- [ ] 任务6: 开发Content Agent的文本生成能力
- [ ] 任务7: 构建Content Agent的A2A适配器
- [ ] 任务8: 实现Host Agent的Remote Agent连接管理
- [ ] 任务9: 开发Host Agent的智能写作路由逻辑
- [ ] 任务10: 集成Host Agent与Google ADK
- [ ] 任务11: 实现系统启动和配置管理
- [ ] 任务12: 开发端到端写作测试用例
- [ ] 任务13: 实现A2A协议通信监控
- [ ] 任务14: 优化错误处理和容错机制
- [ ] 任务15: 创建写作助手Web UI
- [ ] 任务16: 性能优化和扩展性改进
- [ ] 任务17: 文档和部署指南编写

## 参考资源

- [A2A协议规范](https://github.com/a2aproject/A2A)
- [A2A Python SDK](https://github.com/a2aproject/a2a-python)
- [Google ADK文档](https://google.github.io/adk-docs/)
- [原始示例代码](../a2a-samples/samples/python/agents/airbnb_planner_multiagent/)

## 学习笔记

详细的学习笔记和心得记录在 `docs/learning_notes.md` 文件中。

## 问题和解决方案

遇到的问题和解决方案记录在 `docs/troubleshooting.md` 文件中。