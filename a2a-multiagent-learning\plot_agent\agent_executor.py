#!/usr/bin/env python3
"""
Plot Agent执行器
基于A2A SDK的AgentExecutor实现
"""

try:
    from typing import override
except ImportError:
    from typing_extensions import override
from a2a.server.agent_execution import AgentExecutor, RequestContext
from a2a.server.events import EventQueue
from a2a.types import (
    TaskArtifactUpdateEvent,
    TaskState,
    TaskStatus,
    TaskStatusUpdateEvent,
)
from a2a.utils import new_text_artifact
from .agent import PlotAgent


class PlotAgentExecutor(AgentExecutor):
    """Plot Agent执行器实现"""

    def __init__(self):
        self.agent = PlotAgent()

    @override
    async def execute(
        self,
        context: RequestContext,
        event_queue: EventQueue,
    ) -> None:
        """执行Plot Agent任务"""
        query = context.get_user_input()
        if not context.message:
            raise Exception('No message provided')

        try:
            # 使用Plot Agent处理查询并流式返回结果
            async for event in self.agent.stream(query):
                # 发送中间结果
                message = TaskArtifactUpdateEvent(
                    contextId=context.context_id, # type: ignore
                    taskId=context.task_id, # type: ignore
                    artifact=new_text_artifact(
                        name='plot_result',
                        text=event['content'],
                    ),
                )
                await event_queue.enqueue_event(message)
                
                # 检查是否完成
                if event['done']:
                    break

            # 发送完成状态
            status = TaskStatusUpdateEvent(
                contextId=context.context_id, # type: ignore
                taskId=context.task_id, # type: ignore
                status=TaskStatus(state=TaskState.completed),
                final=True
            )
            await event_queue.enqueue_event(status)
            
        except Exception as e:
            # 发送错误状态
            error_message = TaskArtifactUpdateEvent(
                contextId=context.context_id, # type: ignore
                taskId=context.task_id, # type: ignore
                artifact=new_text_artifact(
                    name='error',
                    text=f'处理请求时出错: {str(e)}',
                ),
            )
            await event_queue.enqueue_event(error_message)
            
            status = TaskStatusUpdateEvent(
                contextId=context.context_id, # type: ignore
                taskId=context.task_id, # type: ignore
                status=TaskStatus(state=TaskState.failed),
                final=True
            )
            await event_queue.enqueue_event(status)

    @override
    async def cancel(
        self, context: RequestContext, event_queue: EventQueue
    ) -> None:
        """取消任务（暂不支持）"""
        raise Exception('cancel not supported')