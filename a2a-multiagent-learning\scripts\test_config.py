#!/usr/bin/env python3
"""
测试配置管理器
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.config_manager import get_config_manager

def test_config_manager():
    """测试配置管理器"""
    print("🔧 测试配置管理器...")
    print("=" * 50)
    
    try:
        # 获取配置管理器
        cm = get_config_manager()
        
        # 打印配置摘要
        cm.print_config_summary()
        
        # 测试Agent配置获取
        print("\n📋 测试Agent配置获取:")
        for agent_name in ['plot_agent', 'character_agent', 'content_agent', 'host_agent']:
            config = cm.get_agent_config(agent_name)
            if config:
                print(f"  ✅ {agent_name}: {config.host}:{config.port} ({config.writing_mode})")
            else:
                print(f"  ❌ {agent_name}: 配置不存在")
        
        # 测试配置验证
        print("\n🔍 配置验证:")
        errors = cm.validate_config()
        if errors:
            print("  ⚠️ 发现问题:")
            for error in errors:
                print(f"    - {error}")
        else:
            print("  ✅ 配置验证通过")
        
        # 测试启用的Agent列表
        print("\n📋 启用的Agent:")
        enabled_agents = cm.get_enabled_agents()
        for agent in enabled_agents:
            print(f"  ✅ {agent}")
        
        print("\n🎉 配置管理器测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_config_manager()
    if not success:
        sys.exit(1)