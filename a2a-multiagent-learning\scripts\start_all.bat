@echo off
echo 🏠 A2A多Agent写作助手启动器
echo ================================

cd /d "%~dp0\.."

echo 📋 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python未安装或不在PATH中
    pause
    exit /b 1
)

echo 📋 检查依赖包...
python -c "import a2a; print('✅ A2A SDK已安装')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ A2A SDK未安装，请运行: pip install a2a-sdk
    pause
    exit /b 1
)

echo 🚀 启动所有Agent...
python scripts/start_all_agents.py

pause