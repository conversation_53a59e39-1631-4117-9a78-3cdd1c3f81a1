#!/usr/bin/env python3
"""
启动A2A通信监控系统
"""

import sys
import time
import signal
import threading
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from monitoring.communication_monitor import get_communication_monitor, shutdown_communication_monitor
from monitoring.monitoring_dashboard import MonitoringDashboard
from monitoring.agent_monitor_integration import create_monitoring_report

class MonitoringSystem:
    """监控系统管理器"""
    
    def __init__(self):
        self.monitor = get_communication_monitor()
        self.dashboard = MonitoringDashboard(refresh_interval=3)
        self.running = False
        self.report_thread = None
        
    def start(self, enable_dashboard: bool = True, report_interval: int = 300):
        """启动监控系统"""
        print("🚀 启动A2A通信监控系统...")
        
        # 启动通信监控
        self.monitor.start_monitoring()
        
        # 启动监控面板
        if enable_dashboard:
            self.dashboard.start()
        
        # 启动定期报告
        if report_interval > 0:
            self.running = True
            self.report_thread = threading.Thread(
                target=self._report_loop,
                args=(report_interval,),
                daemon=True
            )
            self.report_thread.start()
        
        print("✅ 监控系统启动完成")
        print(f"📊 监控面板: {'已启用' if enable_dashboard else '已禁用'}")
        print(f"📄 报告间隔: {report_interval}秒" if report_interval > 0 else "📄 定期报告: 已禁用")
        
    def stop(self):
        """停止监控系统"""
        print("🛑 停止监控系统...")
        
        self.running = False
        
        # 停止监控面板
        self.dashboard.stop()
        
        # 停止通信监控
        shutdown_communication_monitor()
        
        # 等待报告线程结束
        if self.report_thread:
            self.report_thread.join(timeout=2)
        
        print("✅ 监控系统已停止")
    
    def _report_loop(self, interval: int):
        """定期报告循环"""
        while self.running:
            try:
                time.sleep(interval)
                if self.running:
                    self._generate_periodic_report()
            except Exception as e:
                print(f"报告生成异常: {e}")
    
    def _generate_periodic_report(self):
        """生成定期报告"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = project_root / "monitoring" / "reports" / f"monitoring_report_{timestamp}.json"
            
            # 确保报告目录存在
            report_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 生成报告
            create_monitoring_report(str(report_file))
            
            # 显示简要统计
            stats = self.monitor.get_system_stats()
            print(f"\n📊 [{datetime.now().strftime('%H:%M:%S')}] 系统状态:")
            print(f"   Agent: {stats['agents']['online']}/{stats['agents']['total']} 在线")
            print(f"   任务: {stats['tasks']['completed']} 完成, 成功率: {stats['tasks']['success_rate']:.1f}%")
            print(f"   通信: {stats['communication']['total_requests']} 请求, 错误率: {stats['communication']['error_rate']:.1f}%")
            
        except Exception as e:
            print(f"生成定期报告失败: {e}")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n📡 收到信号 {signum}，正在关闭监控系统...")
        self.stop()
        sys.exit(0)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="A2A通信监控系统")
    parser.add_argument("--no-dashboard", action="store_true", help="禁用监控面板")
    parser.add_argument("--report-interval", type=int, default=300, help="报告生成间隔(秒), 0表示禁用")
    parser.add_argument("--interactive", action="store_true", help="交互式模式")
    
    args = parser.parse_args()
    
    # 创建监控系统
    monitoring_system = MonitoringSystem()
    
    # 设置信号处理
    signal.signal(signal.SIGINT, monitoring_system.signal_handler)
    signal.signal(signal.SIGTERM, monitoring_system.signal_handler)
    
    try:
        # 启动监控系统
        monitoring_system.start(
            enable_dashboard=not args.no_dashboard,
            report_interval=args.report_interval
        )
        
        if args.interactive:
            # 交互式模式
            print("\n📋 交互式监控模式")
            print("可用命令:")
            print("  stats - 显示系统统计")
            print("  report - 生成即时报告")
            print("  agents - 显示Agent状态")
            print("  export <file> - 导出监控数据")
            print("  quit - 退出")
            
            while True:
                try:
                    command = input("\n监控> ").strip().lower()
                    
                    if command in ["quit", "exit"]:
                        break
                    elif command == "stats":
                        stats = monitoring_system.monitor.get_system_stats()
                        import json
                        print(json.dumps(stats, indent=2, ensure_ascii=False))
                    elif command == "report":
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        report_file = f"monitoring_report_{timestamp}.json"
                        create_monitoring_report(report_file)
                        print(f"📄 报告已生成: {report_file}")
                    elif command == "agents":
                        agent_metrics = monitoring_system.monitor.get_agent_metrics()
                        print("\n🤖 Agent状态:")
                        for name, metrics in agent_metrics.items():
                            status_icon = {"online": "🟢", "offline": "🔴", "idle": "🟡"}.get(metrics.status, "⚪")
                            print(f"  {status_icon} {name}: {metrics.status} ({metrics.total_requests} 请求)")
                    elif command.startswith("export "):
                        filepath = command[7:].strip()
                        create_monitoring_report(filepath)
                        print(f"📄 数据已导出到: {filepath}")
                    elif command:
                        print("❌ 未知命令")
                        
                except KeyboardInterrupt:
                    break
                except EOFError:
                    break
                except Exception as e:
                    print(f"命令执行错误: {e}")
        else:
            # 非交互式模式，持续运行
            print("\n💡 监控系统正在运行...")
            print("按 Ctrl+C 停止监控")
            
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass
    
    finally:
        monitoring_system.stop()

if __name__ == "__main__":
    main()