#!/usr/bin/env python3
"""
Plot Agent MCP工具服务器
提供情节规划相关的工具，包括故事大纲生成、章节结构创建、情节冲突分析和转折建议
"""

import json
import os
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional
from fastmcp import FastMCP

# 初始化MCP服务器
mcp = FastMCP("Plot Agent MCP Server")

# 获取模板路径
TEMPLATES_DIR = Path(__file__).parent.parent / "templates" / "story_structures"

def load_template_file(file_path: Path) -> str:
    """加载模板文件内容"""
    try:
        if file_path.suffix == '.yaml':
            with open(file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
                return yaml.dump(data, default_flow_style=False, allow_unicode=True, indent=2)
        else:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
    except Exception as e:
        return f"无法读取模板文件 {file_path}: {str(e)}"

def get_available_templates() -> Dict[str, List[str]]:
    """获取可用的故事模板"""
    templates = {
        "story_structures": [],
        "classic_plots": [],
        "plot_types": [],
        "conflicts": [],
        "twists": []
    }
    
    # 基础故事结构
    basic_templates = TEMPLATES_DIR.glob("*.yaml")
    for template in basic_templates:
        templates["story_structures"].append(template.stem)
    
    # 详细剧情模板
    plot_dir = TEMPLATES_DIR / "plot"
    if plot_dir.exists():
        # 经典情节模板（19个经典情节）
        classic_plots = list(plot_dir.glob("经典情节*.txt"))
        for plot_file in sorted(classic_plots):
            templates["classic_plots"].append(plot_file.stem)
        
        # 剧情类型
        plot_types_dir = plot_dir / "剧情类型"
        if plot_types_dir.exists():
            for template in plot_types_dir.glob("*.txt"):
                templates["plot_types"].append(template.stem)
        
        # 剧情冲突
        conflicts_dir = plot_dir / "剧情冲突"
        if conflicts_dir.exists():
            for template in conflicts_dir.glob("*.txt"):
                templates["conflicts"].append(template.stem)
        
        # 剧情转折
        twists_dir = plot_dir / "剧情转折"
        if twists_dir.exists():
            for template in twists_dir.glob("*.txt"):
                templates["twists"].append(template.stem)
    
    return templates

@mcp.tool()
def generate_story_outline(
    genre: str,
    theme: str,
    target_length: str = "中篇",
    structure_type: str = "三幕式",
    additional_requirements: str = ""
) -> str:
    """
    生成故事大纲和三幕结构
    
    Args:
        genre: 故事类型 (如: 现代都市, 古代言情, 玄幻修仙, 科幻未来等)
        theme: 故事主题 (如: 爱情, 成长, 复仇, 救赎等)
        target_length: 目标长度 (短篇/中篇/长篇)
        structure_type: 结构类型 (三幕式/五幕式/英雄之旅等)
        additional_requirements: 额外要求
    
    Returns:
        生成的故事大纲
    """
    try:
        # 加载对应的结构模板
        template_file = None
        if structure_type == "三幕式":
            template_file = TEMPLATES_DIR / "three_act.yaml"
        elif structure_type == "英雄之旅":
            template_file = TEMPLATES_DIR / "hero_journey.yaml"
        
        template_content = ""
        if template_file and template_file.exists():
            template_content = load_template_file(template_file)
        
        outline = f"""# 故事大纲

## 基本信息
- **类型**: {genre}
- **主题**: {theme}
- **长度**: {target_length}
- **结构**: {structure_type}

## 故事概述
基于{theme}主题的{genre}故事，采用{structure_type}结构展开。

## 结构框架
{template_content}

## 核心冲突
根据{theme}主题，故事的核心冲突围绕主角的内在成长和外在挑战展开。

## 情节发展
### 开端 (25%)
- 建立故事世界和主角
- 引入核心冲突
- 设置故事基调

### 发展 (50%)
- 冲突升级
- 角色成长
- 情节转折

### 高潮 (20%)
- 冲突达到顶点
- 主角面临最大挑战
- 关键选择时刻

### 结局 (5%)
- 冲突解决
- 主题升华
- 故事收尾

## 章节规划
根据{target_length}的设定，建议章节数量和每章重点。

## 额外考虑
{additional_requirements if additional_requirements else "无特殊要求"}

---
*此大纲为初步框架，可根据具体创作需求进行调整和细化。*
"""
        
        return outline
        
    except Exception as e:
        return f"生成故事大纲时出错: {str(e)}"

@mcp.tool()
def create_chapter_structure(
    story_outline: str,
    total_chapters: int = 20,
    chapter_length: str = "3000-5000字",
    pacing_style: str = "稳步推进"
) -> str:
    """
    创建章节结构和节奏安排
    
    Args:
        story_outline: 故事大纲
        total_chapters: 总章节数
        chapter_length: 每章长度
        pacing_style: 节奏风格 (快节奏/稳步推进/慢热型)
    
    Returns:
        详细的章节结构规划
    """
    try:
        # 根据节奏风格调整章节分配
        if pacing_style == "快节奏":
            opening_ratio = 0.15
            development_ratio = 0.60
            climax_ratio = 0.20
            ending_ratio = 0.05
        elif pacing_style == "慢热型":
            opening_ratio = 0.35
            development_ratio = 0.45
            climax_ratio = 0.15
            ending_ratio = 0.05
        else:  # 稳步推进
            opening_ratio = 0.25
            development_ratio = 0.50
            climax_ratio = 0.20
            ending_ratio = 0.05
        
        opening_chapters = max(1, int(total_chapters * opening_ratio))
        development_chapters = max(1, int(total_chapters * development_ratio))
        climax_chapters = max(1, int(total_chapters * climax_ratio))
        ending_chapters = max(1, total_chapters - opening_chapters - development_chapters - climax_chapters)
        
        structure = f"""# 章节结构规划

## 基本设置
- **总章节数**: {total_chapters}章
- **每章长度**: {chapter_length}
- **节奏风格**: {pacing_style}

## 故事大纲参考
{story_outline[:200]}...

## 章节分配

### 第一幕：开端 ({opening_chapters}章)
"""
        
        # 生成开端章节
        for i in range(1, opening_chapters + 1):
            structure += f"""
#### 第{i}章
- **主要任务**: {"世界观建立" if i == 1 else "角色介绍" if i == 2 else "冲突引入"}
- **情节重点**: {"展示故事背景和设定" if i == 1 else "介绍主要角色" if i == 2 else "引出核心矛盾"}
- **节奏**: {"缓慢展开" if pacing_style == "慢热型" else "适中" if pacing_style == "稳步推进" else "快速切入"}
- **字数目标**: {chapter_length}
"""
        
        structure += f"""
### 第二幕：发展 ({development_chapters}章)
"""
        
        # 生成发展章节
        for i in range(opening_chapters + 1, opening_chapters + development_chapters + 1):
            chapter_phase = "前期" if i <= opening_chapters + development_chapters // 3 else "中期" if i <= opening_chapters + development_chapters * 2 // 3 else "后期"
            structure += f"""
#### 第{i}章
- **发展阶段**: {chapter_phase}
- **主要任务**: {"冲突深化" if chapter_phase == "前期" else "情节转折" if chapter_phase == "中期" else "高潮铺垫"}
- **情节重点**: {"角色关系发展" if chapter_phase == "前期" else "重要转折点" if chapter_phase == "中期" else "为高潮做准备"}
- **节奏**: {"逐步加快" if pacing_style == "快节奏" else "稳定推进"}
- **字数目标**: {chapter_length}
"""
        
        structure += f"""
### 第三幕：高潮 ({climax_chapters}章)
"""
        
        # 生成高潮章节
        for i in range(opening_chapters + development_chapters + 1, opening_chapters + development_chapters + climax_chapters + 1):
            structure += f"""
#### 第{i}章
- **主要任务**: {"冲突爆发" if i == opening_chapters + development_chapters + 1 else "决战时刻"}
- **情节重点**: {"最大危机" if i == opening_chapters + development_chapters + 1 else "最终对决"}
- **节奏**: 紧张激烈
- **字数目标**: {chapter_length}
"""
        
        structure += f"""
### 第四幕：结局 ({ending_chapters}章)
"""
        
        # 生成结局章节
        for i in range(opening_chapters + development_chapters + climax_chapters + 1, total_chapters + 1):
            structure += f"""
#### 第{i}章
- **主要任务**: {"冲突解决" if i == total_chapters else "故事收尾"}
- **情节重点**: {"主题升华" if i == total_chapters else "后续安排"}
- **节奏**: 逐渐平缓
- **字数目标**: {chapter_length}
"""
        
        structure += f"""
## 节奏控制建议

### {pacing_style}特点
- 根据选择的节奏风格，合理安排情节密度
- 注意张弛有度，避免读者疲劳
- 在关键转折点增加悬念和冲突

### 章节连接
- 每章结尾设置悬念或转折
- 保持故事连贯性
- 适当使用伏笔和呼应

---
*此章节结构可根据实际写作进度进行调整*
"""
        
        return structure
        
    except Exception as e:
        return f"创建章节结构时出错: {str(e)}"

@mcp.tool()
def analyze_plot_conflicts(
    story_context: str,
    conflict_types: List[str] = None
) -> str:
    """
    分析情节冲突和张力点
    
    Args:
        story_context: 故事背景和情节描述
        conflict_types: 要分析的冲突类型列表
    
    Returns:
        冲突分析报告
    """
    try:
        if conflict_types is None:
            conflict_types = ["人与人", "人与自我", "人与社会", "人与自然", "人与超自然"]
        
        # 加载冲突模板
        conflicts_dir = TEMPLATES_DIR / "plot" / "剧情冲突"
        conflict_templates = {}
        
        if conflicts_dir.exists():
            for conflict_file in conflicts_dir.glob("*.txt"):
                conflict_name = conflict_file.stem
                conflict_templates[conflict_name] = load_template_file(conflict_file)
        
        analysis = f"""# 情节冲突分析

## 故事背景
{story_context}

## 冲突类型分析

"""
        
        for conflict_type in conflict_types:
            # 查找对应的模板
            template_key = f"{conflict_type}的冲突"
            template_content = conflict_templates.get(template_key, "")
            
            analysis += f"""### {conflict_type}的冲突

#### 模板参考
{template_content[:300] if template_content else "暂无对应模板"}

#### 在当前故事中的体现
- **冲突表现**: 根据故事背景分析此类冲突的具体表现
- **张力来源**: 识别产生戏剧张力的关键因素
- **发展趋势**: 预测冲突的发展方向和可能结果
- **解决方式**: 建议的冲突解决路径

"""
        
        analysis += f"""## 冲突层次分析

### 表层冲突
- 直接可见的矛盾和对立
- 推动情节发展的外在冲突

### 深层冲突
- 角色内心的挣扎和矛盾
- 价值观和信念的冲突

### 潜在冲突
- 尚未爆发但可能影响故事的冲突
- 为后续情节发展埋下的伏笔

## 张力点识别

### 高张力时刻
- 冲突最激烈的场景
- 角色面临重大选择的时刻
- 真相揭露或秘密暴露的瞬间

### 张力调节
- 适当的缓解时刻
- 为下一个冲突高潮做准备
- 角色情感的起伏变化

## 冲突解决建议
- 符合角色性格的解决方式
- 与故事主题相呼应的结局
- 为读者提供情感满足的收尾

---
*冲突分析有助于增强故事的戏剧张力和可读性*
"""
        
        return analysis
        
    except Exception as e:
        return f"分析情节冲突时出错: {str(e)}"

@mcp.tool()
def suggest_plot_twists(
    current_plot: str,
    twist_type: str = "反转与惊喜",
    target_chapter: int = None
) -> str:
    """
    建议情节转折和悬念设置
    
    Args:
        current_plot: 当前情节发展
        twist_type: 转折类型 (反转与惊喜/伏笔与悬念/误会与误解/阴谋与揭露)
        target_chapter: 目标章节位置
    
    Returns:
        转折建议和悬念设置方案
    """
    try:
        # 加载转折模板
        twists_dir = TEMPLATES_DIR / "plot" / "剧情转折"
        twist_template = ""
        
        if twists_dir.exists():
            template_file = twists_dir / f"{twist_type}.txt"
            if template_file.exists():
                twist_template = load_template_file(template_file)
        
        suggestions = f"""# 情节转折建议

## 当前情节概述
{current_plot}

## 转折类型：{twist_type}

### 模板参考
{twist_template[:400] if twist_template else "暂无对应模板"}

## 具体转折建议

### 转折点设计
"""
        
        if twist_type == "反转与惊喜":
            suggestions += """
- **身份反转**: 揭示角色的真实身份或隐藏背景
- **动机反转**: 角色的真实目的与表面行为相反
- **情况反转**: 看似有利的情况实际上是陷阱
- **结果反转**: 预期的结果完全相反
"""
        elif twist_type == "伏笔与悬念":
            suggestions += """
- **细节伏笔**: 在早期章节埋下看似无关紧要的细节
- **对话伏笔**: 通过角色对话暗示未来发展
- **环境伏笔**: 利用场景描写暗示情节走向
- **行为伏笔**: 角色的异常行为预示重要转折
"""
        elif twist_type == "误会与误解":
            suggestions += """
- **信息不对称**: 角色掌握的信息不完整或错误
- **沟通障碍**: 关键信息传达过程中的偏差
- **先入为主**: 基于过往经验的错误判断
- **表象迷惑**: 事物的表面现象与真实情况不符
"""
        elif twist_type == "阴谋与揭露":
            suggestions += """
- **幕后黑手**: 揭示真正的操控者身份
- **计划曝光**: 隐秘计划的逐步暴露
- **内部背叛**: 信任的人的背叛行为
- **真相大白**: 长期隐瞒的秘密被揭开
"""
        
        suggestions += f"""
### 时机安排
{f"**目标章节**: 第{target_chapter}章" if target_chapter else "**建议时机**: 根据故事节奏选择合适时机"}

#### 最佳转折时机
- **故事中期**: 为后半段发展提供新动力
- **高潮前夕**: 增加最终冲突的复杂性
- **低潮时刻**: 为故事注入新的活力
- **关键节点**: 在角色做出重要决定时

### 悬念设置技巧

#### 信息控制
- 适度透露信息，保持读者好奇心
- 设置多个可能性，让读者猜测
- 在关键时刻暂停，制造悬念

#### 节奏把控
- 快慢结合，张弛有度
- 在平静中埋下不安因素
- 用细节暗示即将到来的变化

### 实施建议

#### 前期准备
- 在转折前几章开始铺垫
- 通过细节和对话暗示
- 建立读者的预期

#### 转折执行
- 选择合适的揭示方式
- 控制信息披露的节奏
- 确保转折的合理性

#### 后续处理
- 处理转折带来的后果
- 调整角色关系和情节走向
- 为下一个转折做准备

## 注意事项
- 转折要符合故事逻辑，避免为了惊喜而惊喜
- 保持角色行为的一致性
- 确保转折能推动故事发展
- 考虑读者的接受度和情感反应

---
*好的转折能让故事更加引人入胜，但要适度使用*
"""
        
        return suggestions
        
    except Exception as e:
        return f"建议情节转折时出错: {str(e)}"

@mcp.tool()
def analyze_classic_plot(
    plot_type: str,
    story_context: str = "",
    adaptation_requirements: str = ""
) -> str:
    """
    基于经典情节模板进行深度分析和建议
    
    Args:
        plot_type: 经典情节类型 (如: 探寻, 爱情故事, 复仇, 探险等)
        story_context: 当前故事背景
        adaptation_requirements: 改编要求
    
    Returns:
        基于经典情节的详细分析和建议
    """
    try:
        # 查找对应的经典情节模板
        plot_dir = TEMPLATES_DIR / "plot"
        template_content = ""
        
        if plot_dir.exists():
            # 尝试多种匹配方式
            possible_files = [
                f"经典情节1：{plot_type}.txt" if plot_type == "探寻" else None,
                f"经典情节2：{plot_type}.txt" if plot_type == "探险" else None,
                f"经典情节3：{plot_type}.txt" if plot_type == "追逐" else None,
                f"经典情节4：{plot_type}.txt" if plot_type == "解救" else None,
                f"经典情节5：{plot_type}.txt" if plot_type == "逃跑" else None,
                f"经典情节6：{plot_type}.txt" if plot_type == "复仇" else None,
                f"经典情节7：{plot_type}.txt" if plot_type == "推理故事" else None,
                f"经典情节8：{plot_type}.txt" if plot_type == "对手戏" else None,
                f"经典情节9：{plot_type}.txt" if plot_type == "落魄之人" else None,
                f"经典情节10：{plot_type}.txt" if plot_type == "诱惑" else None,
                f"经典情节11：{plot_type}.txt" if plot_type == "变形记" else None,
                f"经典情节12：{plot_type}.txt" if plot_type == "转变" else None,
                f"经典情节13：{plot_type}.txt" if plot_type == "成长" else None,
                f"经典情节14：{plot_type}.txt" if plot_type == "爱情故事" else None,
                f"经典情节15：{plot_type}.txt" if plot_type == "不伦之恋" else None,
                f"经典情节16：{plot_type}.txt" if plot_type == "牺牲" else None,
                f"经典情节17：{plot_type}.txt" if plot_type == "自我发现之旅" else None,
                f"经典情节18：{plot_type}.txt" if plot_type == "可悲的无节制行为" else None,
                f"经典情节19：{plot_type}.txt" if plot_type == "盛衰沉浮" else None,
            ]
            
            # 也尝试直接匹配文件名
            for plot_file in plot_dir.glob("经典情节*.txt"):
                if plot_type in plot_file.stem:
                    template_content = load_template_file(plot_file)
                    break
        
        if not template_content:
            # 如果没找到精确匹配，列出可用的选项
            available_plots = []
            if plot_dir.exists():
                for plot_file in sorted(plot_dir.glob("经典情节*.txt")):
                    available_plots.append(plot_file.stem)
            
            return f"""# 经典情节分析

## 错误信息
未找到匹配的经典情节模板：{plot_type}

## 可用的经典情节类型
{chr(10).join(f"- {plot}" for plot in available_plots)}

## 使用建议
请从上述列表中选择一个具体的情节类型，例如：
- 探寻 (寻找目标的故事)
- 爱情故事 (浪漫情感故事)
- 复仇 (报复主题故事)
- 成长 (角色发展故事)
等等...
"""
        
        analysis = f"""# 经典情节深度分析：{plot_type}

## 经典模板内容
{template_content[:1000]}...

## 故事背景分析
{f"### 当前故事背景{chr(10)}{story_context}" if story_context else "### 故事背景{chr(10)}请提供具体的故事背景以获得更精准的分析"}

## 情节结构适配建议

### 第一幕：设置阶段
- **核心任务**: 建立主角和初始状态
- **关键元素**: 根据{plot_type}的特点设置起始情境
- **读者期待**: 为后续发展埋下伏笔

### 第二幕：发展阶段  
- **核心任务**: 推进{plot_type}的主要冲突
- **关键元素**: 设置障碍和挑战
- **角色成长**: 通过困难展现角色变化

### 第三幕：解决阶段
- **核心任务**: 解决{plot_type}的核心问题
- **关键元素**: 高潮对决和最终结果
- **主题升华**: 体现{plot_type}的深层意义

## 角色设计建议

### 主角特征
- 适合{plot_type}的性格特点
- 具备推动情节发展的动机
- 拥有成长和变化的空间

### 配角功能
- 支持或阻碍主角的角色
- 体现{plot_type}不同侧面的人物
- 增加故事层次和复杂性

## 现代改编要点

### 时代适应
- 将经典{plot_type}融入现代背景
- 保持核心情节逻辑的同时更新表现形式
- 考虑现代读者的价值观和期待

### 创新元素
- 在经典框架基础上增加新颖元素
- 结合其他情节类型创造复合故事
- 运用现代叙事技巧增强表现力

## 具体实施建议

### 开篇策略
- 如何吸引读者进入{plot_type}的世界
- 建立情节期待的有效方法
- 角色出场的最佳时机

### 中段维持
- 保持{plot_type}张力的技巧
- 避免中段拖沓的方法
- 适时引入转折和悬念

### 结尾处理
- {plot_type}的经典结局模式
- 现代读者期待的结局类型
- 开放式vs封闭式结局的选择

## 改编要求处理
{f"### 特殊要求{chr(10)}{adaptation_requirements}" if adaptation_requirements else "### 改编建议{chr(10)}可根据具体需求调整经典情节的现代表达方式"}

## 注意事项
- 尊重{plot_type}的核心精神
- 避免生搬硬套经典模式
- 结合个人创作风格进行改编
- 考虑目标读者群体的偏好

---
*经典情节是创作的基础，但创新是作品成功的关键*
"""
        
        return analysis
        
    except Exception as e:
        return f"分析经典情节时出错: {str(e)}"

@mcp.tool()
def get_plot_templates() -> str:
    """
    获取可用的情节模板列表
    
    Returns:
        可用模板的详细列表
    """
    try:
        templates = get_available_templates()
        
        template_list = f"""# 可用情节模板

## 基础故事结构模板
"""
        for template in templates["story_structures"]:
            template_list += f"- {template}\n"
        
        template_list += f"""
## 剧情类型模板 ({len(templates["plot_types"])}个)
"""
        for template in templates["plot_types"]:
            template_list += f"- {template}\n"
        
        template_list += f"""
## 冲突类型模板 ({len(templates["conflicts"])}个)
"""
        for template in templates["conflicts"]:
            template_list += f"- {template}\n"
        
        template_list += f"""
## 转折技巧模板 ({len(templates["twists"])}个)
"""
        for template in templates["twists"]:
            template_list += f"- {template}\n"
        
        template_list += f"""
## 经典情节模板 ({len(templates["classic_plots"])}个)
"""
        # 列出经典情节模板，按编号排序
        for template in templates["classic_plots"]:
            template_list += f"- {template}\n"
        
        template_list += """
## 使用说明
- 使用 `generate_story_outline` 工具时可指定结构类型
- 使用 `analyze_plot_conflicts` 工具时可选择冲突类型
- 使用 `suggest_plot_twists` 工具时可选择转折类型
- 所有模板都可以根据具体需求进行调整和组合

---
*模板仅供参考，创作时应根据具体故事需求灵活运用*
"""
        
        return template_list
        
    except Exception as e:
        return f"获取模板列表时出错: {str(e)}"

if __name__ == "__main__":
    # 启动MCP服务器
    mcp.run()