#!/usr/bin/env python3
"""
测试AI增强的Plot Agent
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_ai_enhanced_plot_agent():
    """测试AI增强的Plot Agent功能"""
    print("🤖 测试AI增强的Plot Agent...")
    print("=" * 60)
    
    try:
        # 测试导入
        print("📦 测试模块导入...")
        from plot_agent.agent import PlotAgent
        from plot_agent.agent_executor import PlotAgentExecutor
        print("✅ 模块导入成功")
        
        # 创建Plot Agent实例
        print("\n🎭 创建Plot Agent实例...")
        agent = PlotAgent()
        print(f"✅ Agent名称: {agent.name}")
        print(f"✅ Agent描述: {agent.description}")
        
        # 检查AI客户端状态
        if agent.ai_client:
            print(f"🤖 AI客户端: 已启用 (模型: {agent.model})")
        else:
            print("⚠️ AI客户端: 未启用，将使用MCP工具模式")
        
        # 测试1: 故事大纲生成
        print("\n" + "="*60)
        print("🎯 测试1: AI增强故事大纲生成")
        print("="*60)
        
        test_query1 = "帮我生成一个现代都市职场爱情故事的大纲，男主是霸道总裁，女主是新入职的实习生"
        print(f"📝 测试查询: {test_query1}")
        print("\n📄 AI响应:")
        print("-" * 40)
        
        result_parts1 = []
        async for event in agent.stream(test_query1):
            if event['content']:
                print(event['content'], end='', flush=True)
                result_parts1.append(event['content'])
            if event['done']:
                break
        
        result1 = ''.join(result_parts1)
        print(f"\n\n✅ 响应完成 ({len(result1)} 字符)")
        
        # 测试2: 情节冲突分析
        print("\n" + "="*60)
        print("🎯 测试2: AI增强情节冲突分析")
        print("="*60)
        
        test_query2 = "分析一个关于人工智能觉醒的科幻故事中的主要冲突"
        print(f"📝 测试查询: {test_query2}")
        print("\n📄 AI响应:")
        print("-" * 40)
        
        result_parts2 = []
        async for event in agent.stream(test_query2):
            if event['content']:
                print(event['content'], end='', flush=True)
                result_parts2.append(event['content'])
            if event['done']:
                break
        
        result2 = ''.join(result_parts2)
        print(f"\n\n✅ 响应完成 ({len(result2)} 字符)")
        
        # 测试3: 通用建议
        print("\n" + "="*60)
        print("🎯 测试3: AI增强通用建议")
        print("="*60)
        
        test_query3 = "我想写一个关于时间旅行的小说，但不知道从哪里开始"
        print(f"📝 测试查询: {test_query3}")
        print("\n📄 AI响应:")
        print("-" * 40)
        
        result_parts3 = []
        async for event in agent.stream(test_query3):
            if event['content']:
                print(event['content'], end='', flush=True)
                result_parts3.append(event['content'])
            if event['done']:
                break
        
        result3 = ''.join(result_parts3)
        print(f"\n\n✅ 响应完成 ({len(result3)} 字符)")
        
        # 测试AgentExecutor
        print("\n" + "="*60)
        print("🔧 测试AgentExecutor...")
        executor = PlotAgentExecutor()
        print("✅ AgentExecutor创建成功")
        
        print("\n🎉 AI增强Plot Agent测试完成！")
        
        # 显示测试总结
        print("\n" + "="*60)
        print("📊 测试总结")
        print("="*60)
        print(f"✅ 故事大纲生成: {len(result1)} 字符")
        print(f"✅ 情节冲突分析: {len(result2)} 字符") 
        print(f"✅ 通用建议: {len(result3)} 字符")
        print(f"✅ AI状态: {'已启用' if agent.ai_client else 'MCP模式'}")
        
        print("\n🚀 启动A2A服务器命令:")
        print("   python -m plot_agent")
        print("   服务器地址: http://localhost:10002")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_ai_enhanced_plot_agent())
    if not success:
        sys.exit(1)