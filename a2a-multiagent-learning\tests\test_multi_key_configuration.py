#!/usr/bin/env python3
"""
测试多密钥配置功能
验证每个Agent的专属API密钥配置和负载均衡效果
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_api_manager_configuration():
    """测试API管理器配置"""
    print("🔧 测试API管理器配置...")
    print("=" * 60)
    
    try:
        from config.ai_api_config import get_api_manager, get_agent_config
        
        # 获取API管理器
        manager = get_api_manager()
        print("✅ API管理器初始化成功")
        
        # 显示配置统计
        stats = manager.get_usage_stats()
        print(f"\n📊 配置统计:")
        print(f"   总Agent数: {stats['total_agents']}")
        print(f"   总密钥数: {stats['total_keys']}")
        print(f"   密钥分布: {stats['key_distribution']}")
        print(f"   Agent分布: {stats['agents_per_provider']}")
        
        # 测试每个Agent的配置
        agents = ['plot_agent', 'character_agent', 'content_agent', 'host_agent']
        
        print(f"\n🔑 Agent专属配置:")
        for agent_id in agents:
            config = get_agent_config(agent_id)
            if config:
                print(f"\n✅ {agent_id}:")
                print(f"   模型: {config.model}")
                print(f"   主密钥: {config.api_key[:20]}...")
                print(f"   备用密钥: {len(config.backup_keys)}个")
                print(f"   温度: {config.temperature}")
                print(f"   最大令牌: {config.max_tokens}")
                print(f"   超时: {config.timeout}秒")
                
                # 验证配置有效性
                is_valid = manager.validate_config(agent_id)
                print(f"   配置有效: {'✅' if is_valid else '❌'}")
            else:
                print(f"❌ {agent_id}: 配置未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_key_rotation():
    """测试密钥轮换功能"""
    print("\n🔄 测试密钥轮换功能...")
    print("=" * 60)
    
    try:
        from config.ai_api_config import get_api_manager
        
        manager = get_api_manager()
        
        # 测试密钥轮换
        test_agent = 'plot_agent'
        config = manager.get_config(test_agent)
        
        if config and config.backup_keys:
            print(f"🔑 {test_agent} 当前配置:")
            print(f"   主密钥: {config.api_key[:20]}...")
            print(f"   备用密钥: {len(config.backup_keys)}个")
            
            # 执行密钥轮换
            old_key = config.api_key
            success = manager.rotate_key(test_agent)
            
            if success:
                new_config = manager.get_config(test_agent)
                print(f"\n✅ 密钥轮换成功:")
                print(f"   新主密钥: {new_config.api_key[:20]}...")
                print(f"   备用密钥: {len(new_config.backup_keys)}个")
                print(f"   轮换验证: {'✅' if old_key != new_config.api_key else '❌'}")
            else:
                print("❌ 密钥轮换失败")
        else:
            print(f"⚠️ {test_agent} 没有备用密钥，无法测试轮换")
        
        return True
        
    except Exception as e:
        print(f"❌ 密钥轮换测试失败: {e}")
        return False

async def test_agent_specific_configurations():
    """测试Agent专属配置的实际效果"""
    print("\n🎯 测试Agent专属配置的实际效果...")
    print("=" * 60)
    
    try:
        # 测试Plot Agent的专属配置
        print("🎭 测试Plot Agent专属配置...")
        from plot_agent.agent import PlotAgent
        
        plot_agent = PlotAgent()
        
        if plot_agent.ai_client:
            print("✅ Plot Agent AI客户端初始化成功")
            print(f"   使用模型: {plot_agent.model}")
            
            # 测试简单的AI调用
            try:
                test_query = "设计一个简单的现代都市爱情故事大纲"
                print(f"🎯 测试查询: {test_query}")
                
                result_chunks = []
                async for chunk in plot_agent.stream(test_query):
                    if chunk.get('content'):
                        result_chunks.append(chunk['content'])
                    if chunk.get('done'):
                        break
                
                full_result = ''.join(result_chunks)
                if full_result and len(full_result) > 100:
                    print("✅ Plot Agent专属API调用成功")
                    print(f"📊 响应长度: {len(full_result)} 字符")
                    print(f"📄 响应预览: {full_result[:100]}...")
                else:
                    print("⚠️ Plot Agent API调用响应较短")
                    
            except Exception as e:
                print(f"⚠️ Plot Agent API调用测试失败: {e}")
        else:
            print("⚠️ Plot Agent AI客户端未初始化")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_load_balancing_simulation():
    """测试负载均衡模拟"""
    print("\n⚖️ 测试负载均衡模拟...")
    print("=" * 60)
    
    try:
        from config.ai_api_config import get_api_manager
        
        manager = get_api_manager()
        
        # 模拟多个并发请求
        print("🔄 模拟并发请求场景...")
        
        agents = ['plot_agent', 'character_agent', 'content_agent', 'host_agent']
        concurrent_requests = []
        
        for i in range(10):  # 模拟10个并发请求
            agent_id = agents[i % len(agents)]  # 轮询选择Agent
            config = manager.get_config(agent_id)
            
            if config:
                request_info = {
                    'request_id': f'req_{i+1}',
                    'agent_id': agent_id,
                    'api_key': config.api_key[:20] + '...',
                    'model': config.model,
                    'temperature': config.temperature
                }
                concurrent_requests.append(request_info)
        
        # 显示负载分布
        agent_loads = {}
        for req in concurrent_requests:
            agent_id = req['agent_id']
            agent_loads[agent_id] = agent_loads.get(agent_id, 0) + 1
        
        print(f"📊 负载分布模拟结果:")
        for agent_id, load in agent_loads.items():
            print(f"   {agent_id}: {load}个请求")
        
        # 显示密钥使用分布
        key_usage = {}
        for req in concurrent_requests:
            key = req['api_key']
            key_usage[key] = key_usage.get(key, 0) + 1
        
        print(f"\n🔑 密钥使用分布:")
        for key, usage in key_usage.items():
            print(f"   {key}: {usage}次使用")
        
        print(f"\n✅ 负载均衡效果:")
        unique_keys = len(key_usage)
        print(f"   使用了 {unique_keys} 个不同的密钥")
        print(f"   平均每个密钥: {10/unique_keys:.1f} 次请求")
        
        if unique_keys >= 3:
            print("🎉 负载均衡效果优秀！")
        elif unique_keys >= 2:
            print("✅ 负载均衡效果良好")
        else:
            print("⚠️ 负载均衡效果一般")
        
        return True
        
    except Exception as e:
        print(f"❌ 负载均衡测试失败: {e}")
        return False

async def test_backup_key_management():
    """测试备用密钥管理"""
    print("\n🔐 测试备用密钥管理...")
    print("=" * 60)
    
    try:
        from config.ai_api_config import get_api_manager
        
        manager = get_api_manager()
        test_agent = 'plot_agent'
        
        # 获取当前配置
        config = manager.get_config(test_agent)
        if not config:
            print(f"❌ 无法获取 {test_agent} 配置")
            return False
        
        original_backup_count = len(config.backup_keys)
        print(f"📊 {test_agent} 当前备用密钥数: {original_backup_count}")
        
        # 测试添加备用密钥
        test_backup_key = "sk-test-backup-key-12345"
        success = manager.add_backup_key(test_agent, test_backup_key)
        
        if success:
            new_config = manager.get_config(test_agent)
            new_backup_count = len(new_config.backup_keys)
            print(f"✅ 添加备用密钥成功，新数量: {new_backup_count}")
            
            # 测试移除备用密钥
            remove_success = manager.remove_backup_key(test_agent, test_backup_key)
            if remove_success:
                final_config = manager.get_config(test_agent)
                final_backup_count = len(final_config.backup_keys)
                print(f"✅ 移除备用密钥成功，最终数量: {final_backup_count}")
                
                if final_backup_count == original_backup_count:
                    print("✅ 备用密钥管理测试通过")
                    return True
                else:
                    print("⚠️ 备用密钥数量不匹配")
            else:
                print("❌ 移除备用密钥失败")
        else:
            print("❌ 添加备用密钥失败")
        
        return False
        
    except Exception as e:
        print(f"❌ 备用密钥管理测试失败: {e}")
        return False

if __name__ == "__main__":
    async def main():
        print("🚀 开始多密钥配置测试...")
        print("📋 验证每个Agent的专属API密钥配置和负载均衡效果")
        print()
        
        # 执行各项测试
        test_results = {}
        
        test_results["API管理器配置"] = await test_api_manager_configuration()
        test_results["密钥轮换功能"] = await test_key_rotation()
        test_results["Agent专属配置"] = await test_agent_specific_configurations()
        test_results["负载均衡模拟"] = await test_load_balancing_simulation()
        test_results["备用密钥管理"] = await test_backup_key_management()
        
        # 显示测试总结
        print("\n" + "="*60)
        print("📊 多密钥配置测试总结:")
        
        passed_tests = 0
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed_tests += 1
        
        success_rate = (passed_tests / len(test_results)) * 100
        print(f"\n📈 总体通过率: {passed_tests}/{len(test_results)} ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            print("\n🎉 多密钥配置测试优秀！")
            print("💡 系统具备:")
            print("   - 完整的多密钥管理能力")
            print("   - 每个Agent的专属API配置")
            print("   - 有效的负载均衡机制")
            print("   - 灵活的密钥轮换功能")
            print("   - 完善的备用密钥管理")
        elif success_rate >= 60:
            print("\n✅ 多密钥配置基本正常")
            print("💡 系统基本具备多密钥管理能力，部分功能需要优化")
        else:
            print("\n⚠️ 多密钥配置需要改进")
            print("💡 建议检查环境变量配置和密钥有效性")
    
    asyncio.run(main())