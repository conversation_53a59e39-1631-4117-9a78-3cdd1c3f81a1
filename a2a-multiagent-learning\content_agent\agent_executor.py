#!/usr/bin/env python3
"""
Content Agent执行器
基于A2A SDK的AgentExecutor实现
"""

try:
    from typing import override
except ImportError:
    from typing_extensions import override

import asyncio
import logging
from a2a.server.agent_execution import AgentExecutor, RequestContext
from a2a.server.events import EventQueue
from a2a.types import (
    TaskArtifactUpdateEvent,
    TaskState,
    TaskStatus,
    TaskStatusUpdateEvent,
)
from a2a.utils import new_text_artifact

from .agent import ContentAgent

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ContentAgentExecutor(AgentExecutor):
    """Content Agent执行器"""
    
    def __init__(self):
        self.agent = ContentAgent()
        logger.info("[INIT] Content Agent执行器已初始化")
    
    @override
    async def execute(
        self,
        context: RequestContext,
        event_queue: EventQueue,
    ) -> None:
        """执行Content Agent任务"""
        query = context.get_user_input()
        if not context.message:
            raise Exception('No message provided')
        
        task_id = context.task_id or f"content_task_{asyncio.get_event_loop().time()}"
        context_id = context.context_id or f"content_ctx_{asyncio.get_event_loop().time()}"
        
        try:
            # 发送开始状态
            status = TaskStatusUpdateEvent(
                contextId=context_id, # type: ignore
                taskId=task_id, # type: ignore
                status=TaskStatus(state=TaskState.working),
                final=False
            )
            await event_queue.enqueue_event(status)
            
            logger.info(f"[TASK] Content Agent开始处理任务: {task_id}")
            
            # 调用Content Agent处理请求并收集流式结果
            result_parts = []
            async for event in self.agent.stream(query):
                if event['content']:
                    result_parts.append(event['content'])
                if event['done']:
                    break
            result = ''.join(result_parts)
            
            # 发送结果
            message = TaskArtifactUpdateEvent(
                contextId=context_id, # type: ignore
                taskId=task_id, # type: ignore
                artifact=new_text_artifact(
                    name='content_generation_result',
                    text=str(result),
                ),
            )
            await event_queue.enqueue_event(message)
            
            # 发送完成状态
            status = TaskStatusUpdateEvent(
                contextId=context_id, # type: ignore
                taskId=task_id, # type: ignore
                status=TaskStatus(state=TaskState.completed),
                final=True
            )
            await event_queue.enqueue_event(status)
            
            logger.info(f"[OK] Content Agent任务完成: {task_id}")
            
        except Exception as e:
            logger.error(f"[ERROR] Content Agent任务失败: {e}")
            
            # 发送错误消息
            error_message = TaskArtifactUpdateEvent(
                contextId=context_id, # type: ignore
                taskId=task_id, # type: ignore
                artifact=new_text_artifact(
                    name='error',
                    text=f'Content Agent处理失败: {str(e)}',
                ),
            )
            await event_queue.enqueue_event(error_message)
            
            # 发送失败状态
            status = TaskStatusUpdateEvent(
                contextId=context_id, # type: ignore
                taskId=task_id, # type: ignore
                status=TaskStatus(state=TaskState.failed),
                final=True
            )
            await event_queue.enqueue_event(status)
            
            raise
    
    @override
    async def cancel(
        self, context: RequestContext, event_queue: EventQueue
    ) -> None:
        """取消Content Agent任务"""
        task_id = context.task_id or "unknown_task"
        context_id = context.context_id or "unknown_context"
        
        logger.info(f"[CANCEL] 取消Content Agent任务: {task_id}")
        
        # 发送取消状态
        status = TaskStatusUpdateEvent(
            contextId=context_id, # type: ignore
            taskId=task_id, # type: ignore
            status=TaskStatus(state=TaskState.cancelled),
            final=True
        )
        await event_queue.enqueue_event(status)
        
        # 发送取消消息
        message = TaskArtifactUpdateEvent(
            contextId=context_id, # type: ignore
            taskId=task_id, # type: ignore
            artifact=new_text_artifact(
                name='cancel_message',
                text='Content Agent任务已取消',
            ),
        )
        await event_queue.enqueue_event(message)