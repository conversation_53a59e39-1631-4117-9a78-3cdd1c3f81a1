[project]
name = "a2a-github-agent"
version = "0.1.0"
description = "A2A GitHub agent"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "a2a-sdk>=0.2.6",
    "click>=8.1.8",
    "dotenv>=0.9.9",
    "httpx>=0.28.1",
    "openai>=1.57.0",
    "pydantic>=2.11.4",
    "python-dotenv>=1.1.0",
    "uvicorn>=0.34.2",
    "pygithub>=2.5.0",
    "requests>=2.31.0",
]

[tool.hatch.build.targets.wheel]
packages = ["."]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
