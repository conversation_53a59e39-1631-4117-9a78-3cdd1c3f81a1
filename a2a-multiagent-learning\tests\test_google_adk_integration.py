#!/usr/bin/env python3
"""
测试Google ADK集成功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_google_adk_integration_basic():
    """测试Google ADK集成基础功能"""
    print("🤖 测试Google ADK集成基础功能...")
    print("=" * 60)
    
    try:
        from host_agent.google_adk_integration import WritingAssistantAgent
        
        # 创建写作助手实例
        assistant = WritingAssistantAgent()
        print("✅ 写作助手Agent创建成功")
        
        # 测试初始化
        success = await assistant.initialize()
        if success:
            print("✅ 写作助手Agent初始化成功")
        else:
            print("⚠️ 写作助手Agent初始化失败，但基础功能可测试")
        
        # 测试工具注册
        print(f"📋 注册的工具数量: {len(assistant.writing_tools)}")
        for tool in assistant.writing_tools:
            print(f"   - {tool['name']}: {tool['description']}")
        
        # 测试会话管理
        print("\n📝 测试会话管理...")
        
        # 创建写作会话
        result = await assistant.create_writing_session_tool(
            project_name="测试项目",
            genre="现代都市",
            description="这是一个测试项目"
        )
        
        if result.get("success"):
            session_id = result["session_id"]
            print(f"✅ 创建会话成功: {session_id}")
            
            # 获取会话状态
            status = await assistant.get_session_status_tool(session_id)
            print(f"✅ 会话状态查询成功: {status.get('project_name')}")
            
            # 测试进度追踪
            assistant._update_progress_tracking(session_id, "test_operation", {
                "success": True,
                "task_id": "test_task_001",
                "task_type": "test_type"
            })
            
            # 获取进度报告
            report = await assistant.get_progress_report(session_id)
            if "error" not in report:
                print(f"✅ 进度报告生成成功: {report['statistics']['total_operations']}个操作")
            else:
                print(f"❌ 进度报告生成失败: {report['error']}")
        else:
            print(f"❌ 创建会话失败: {result.get('error')}")
        
        print("\n🎉 Google ADK集成基础功能测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_writing_tools():
    """测试写作工具功能"""
    print("\n🛠️ 测试写作工具功能...")
    print("=" * 60)
    
    try:
        from host_agent.google_adk_integration import get_writing_assistant, shutdown_writing_assistant
        
        # 获取写作助手
        assistant = await get_writing_assistant()
        print("✅ 获取写作助手成功")
        
        # 测试工具调用
        test_cases = [
            {
                "name": "创建写作会话",
                "tool": "create_writing_session_tool",
                "args": {
                    "project_name": "AI测试小说",
                    "genre": "科幻",
                    "description": "一个关于AI的科幻小说"
                }
            }
        ]
        
        successful_tests = 0
        
        for test_case in test_cases:
            print(f"\n🎯 测试: {test_case['name']}")
            
            try:
                # 调用工具方法
                tool_method = getattr(assistant, test_case['tool'])
                result = await tool_method(**test_case['args'])
                
                if result.get("success") or "error" not in result:
                    print(f"✅ {test_case['name']} 成功")
                    successful_tests += 1
                    
                    # 显示结果预览
                    if isinstance(result, dict):
                        for key, value in result.items():
                            if key not in ['error']:
                                print(f"   {key}: {value}")
                else:
                    print(f"❌ {test_case['name']} 失败: {result.get('error')}")
                
            except Exception as e:
                print(f"❌ {test_case['name']} 异常: {e}")
        
        # 测试用户请求处理
        print(f"\n🎯 测试用户请求处理...")
        test_requests = [
            "创建一个新的写作项目",
            "查看当前项目状态",
            "帮我设计一个故事大纲"
        ]
        
        for request in test_requests:
            print(f"\n📝 请求: {request}")
            try:
                response = await assistant.process_user_request(request)
                print(f"🤖 响应: {response[:100]}..." if len(response) > 100 else f"🤖 响应: {response}")
            except Exception as e:
                print(f"❌ 处理失败: {e}")
        
        # 清理资源
        await shutdown_writing_assistant()
        
        print(f"\n📊 工具测试结果: {successful_tests}/{len(test_cases)} 成功")
        print("🎉 写作工具功能测试完成!")
        
        return successful_tests > 0
        
    except Exception as e:
        print(f"❌ 工具测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_callback_functions():
    """测试回调函数"""
    print("\n🔄 测试回调函数...")
    print("=" * 60)
    
    try:
        from host_agent.google_adk_integration import WritingAssistantAgent
        
        assistant = WritingAssistantAgent()
        
        # 测试before_model_callback
        print("🔍 测试before_model_callback...")
        
        test_request = {
            "messages": [
                {"role": "user", "content": "创建一个新项目"}
            ]
        }
        
        modified_request = await assistant.before_model_callback(test_request)
        
        if "session_context" in modified_request:
            print("✅ before_model_callback成功添加会话上下文")
            context = modified_request["session_context"]
            print(f"   会话ID: {context['session_id']}")
            print(f"   活动会话数: {context['active_sessions']}")
            print(f"   时间戳: {context['timestamp']}")
        else:
            print("⚠️ before_model_callback未添加会话上下文")
        
        # 测试会话提取逻辑
        print("\n🔍 测试会话提取逻辑...")
        
        test_messages = [
            "创建一个新项目",
            "帮我写一个故事",
            "查看项目状态"
        ]
        
        for message in test_messages:
            session_id = assistant._extract_or_create_session(message)
            print(f"   '{message}' → 会话: {session_id}")
        
        print(f"✅ 活动会话数: {len(assistant.active_sessions)}")
        
        print("🎉 回调函数测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 回调函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_integration_with_coordinator():
    """测试与写作协调器的集成"""
    print("\n🤝 测试与写作协调器的集成...")
    print("=" * 60)
    
    try:
        from host_agent.google_adk_integration import get_writing_assistant, shutdown_writing_assistant
        
        # 获取写作助手
        assistant = await get_writing_assistant()
        
        # 检查协调器连接
        if assistant.coordinator:
            print("✅ 写作协调器连接成功")
            
            # 测试协调器功能
            workload = await assistant.coordinator.get_agent_workload()
            print(f"📊 Agent工作负载: {len(workload)}个Agent")
            
            connected_agents = [agent_id for agent_id, load in workload.items() if load['status'] == 'connected']
            print(f"🤖 已连接Agent: {len(connected_agents)}")
            
            # 测试协调写作工具
            if connected_agents:
                print("\n🎯 测试协调写作工具...")
                
                result = await assistant.coordinate_writing_tool(
                    session_id="integration_test",
                    request="这是一个集成测试请求",
                    context={"test": True}
                )
                
                if result.get("success"):
                    print("✅ 协调写作工具测试成功")
                    print(f"   任务类型: {result.get('task_type')}")
                    print(f"   执行Agent: {result.get('assigned_agent')}")
                else:
                    print(f"⚠️ 协调写作工具测试失败: {result.get('error')}")
            else:
                print("⚠️ 没有可用的Agent，跳过协调测试")
        else:
            print("❌ 写作协调器未连接")
        
        # 清理资源
        await shutdown_writing_assistant()
        
        print("🎉 协调器集成测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 协调器集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    async def main():
        print("🚀 开始Google ADK集成测试...")
        print("📋 测试Google ADK与写作协调器的集成功能")
        print()
        
        # 执行各项测试
        test_results = {}
        
        test_results["基础功能"] = await test_google_adk_integration_basic()
        test_results["写作工具"] = await test_writing_tools()
        test_results["回调函数"] = await test_callback_functions()
        test_results["协调器集成"] = await test_integration_with_coordinator()
        
        # 显示测试总结
        print("\n" + "="*60)
        print("📊 Google ADK集成测试总结:")
        
        passed_tests = 0
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed_tests += 1
        
        success_rate = (passed_tests / len(test_results)) * 100
        print(f"\n📈 总体通过率: {passed_tests}/{len(test_results)} ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            print("\n🎉 Google ADK集成测试优秀！")
            print("💡 系统具备:")
            print("   - 完整的Google ADK集成能力")
            print("   - 智能的写作工具调用")
            print("   - 有效的会话生命周期管理")
            print("   - 与写作协调器的无缝集成")
        elif success_rate >= 60:
            print("\n✅ Google ADK集成基本正常")
            print("💡 系统基本具备ADK集成能力，部分功能需要优化")
        else:
            print("\n⚠️ Google ADK集成需要改进")
            print("💡 建议检查Google ADK配置和依赖")
    
    asyncio.run(main())