#!/usr/bin/env python3
"""
Character Agent A2A服务器
基于A2A SDK的标准实现
"""

# 抑制websockets相关的DeprecationWarning
import warnings
warnings.filterwarnings("ignore", message="websockets.legacy is deprecated.*", category=DeprecationWarning)
warnings.filterwarnings("ignore", message="websockets.server.WebSocketServerProtocol is deprecated.*", category=DeprecationWarning)

import argparse
import os
import sys
from pathlib import Path
import uvicorn

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入配置管理器
try:
    from config.config_manager import get_config_manager
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False

from a2a.server.apps import A2AStarletteApplication
from a2a.server.request_handlers import DefaultRequestHandler
from a2a.server.tasks import InMemoryTaskStore
from a2a.types import (
    Agent<PERSON>apabilities,
    AgentCard,
    AgentSkill,
)
from .agent_executor import CharacterAgentExecutor


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="Character Agent A2A服务器")
    parser.add_argument('--host', type=str, default='localhost', help='服务器主机地址')
    parser.add_argument('--port', type=int, default=10003, help='服务器端口号')
    parser.add_argument('--model', type=str, help='AI模型名称')
    parser.add_argument('--writing-mode', type=str, default='ai', help='写作模式')
    parser.add_argument('--config', type=str, help='配置文件路径')
    return parser.parse_args()


if __name__ == '__main__':
    # 解析命令行参数
    args = parse_arguments()
    
    # 加载配置
    config = None
    if CONFIG_AVAILABLE:
        config_manager = get_config_manager()
        config = config_manager.get_agent_config('character_agent')
        
        # 应用命令行覆盖
        if config:
            if args.host != 'localhost':
                config.host = args.host
            if args.port != 10003:
                config.port = args.port
            if args.model:
                config.model_name = args.model
            if args.writing_mode != 'ai':
                config.writing_mode = args.writing_mode
    
    # 确定最终配置
    host = config.host if config else args.host
    port = config.port if config else args.port
    model = config.model_name if config else (args.model or 'gemini-2.5-pro-preview-06-05')
    
    # 定义Character Agent的技能
    create_character_skill = AgentSkill(
        id='create_character',
        name='创建角色',
        description='根据用户需求创建详细的角色设定，包括外貌、性格、背景等',
        tags=['角色创建', '人物设定', '角色档案'],
        examples=[
            '为我的现代都市小说创建一个霸道总裁男主角',
            '设计一个古代言情小说的聪明女主角',
            '创建一个玄幻小说中的神秘导师角色'
        ],
    )
    
    relationship_network_skill = AgentSkill(
        id='build_relationship_network',
        name='构建关系网络',
        description='设计角色之间的关系网络和互动模式',
        tags=['角色关系', '人物互动', '关系网络'],
        examples=[
            '设计男女主角之间的情感发展线',
            '构建主角与配角的关系网络',
            '分析角色间的冲突和合作关系'
        ],
    )
    
    character_development_skill = AgentSkill(
        id='character_development',
        name='角色成长弧线',
        description='设计角色的成长弧线和性格发展轨迹',
        tags=['角色成长', '性格发展', '人物弧线'],
        examples=[
            '设计主角从懦弱到勇敢的成长过程',
            '规划角色的性格转变和内心历程',
            '分析角色在故事中的发展轨迹'
        ],
    )
    
    personality_analysis_skill = AgentSkill(
        id='analyze_personality',
        name='性格分析',
        description='深入分析角色的性格特点和心理动机',
        tags=['性格分析', '心理动机', '角色深度'],
        examples=[
            '分析这个角色的性格优缺点',
            '解读角色的行为动机和内心想法',
            '评估角色性格的一致性和可信度'
        ],
    )
    
    character_dialogue_skill = AgentSkill(
        id='character_dialogue_style',
        name='角色对话风格',
        description='为不同角色设计独特的对话风格和语言特点',
        tags=['对话风格', '语言特色', '角色声音'],
        examples=[
            '为霸道总裁设计专属的说话风格',
            '创建古代角色的对话语言特色',
            '区分不同角色的语言习惯'
        ],
    )
    
    character_backstory_skill = AgentSkill(
        id='create_backstory',
        name='背景故事创建',
        description='为角色创建丰富的背景故事和人生经历',
        tags=['背景故事', '人生经历', '角色历史'],
        examples=[
            '为主角设计一个悲惨的童年经历',
            '创建角色的职业背景和成长环境',
            '设计影响角色性格的关键事件'
        ],
    )

    # 创建Agent卡片
    agent_card = AgentCard(
        name='Character Agent - 角色设定专家',
        description='专业的网文角色设定Agent，提供角色创建、关系网络构建、性格分析、成长弧线设计等全方位的角色设定服务',
        url=f'http://{host}:{port}/',
        version='1.0.0',
        defaultInputModes=['text'],
        defaultOutputModes=['text'],
        capabilities=AgentCapabilities(streaming=True),
        skills=[
            create_character_skill,
            relationship_network_skill,
            character_development_skill,
            personality_analysis_skill,
            character_dialogue_skill,
            character_backstory_skill
        ],
    )

    # 创建请求处理器
    request_handler = DefaultRequestHandler(
        agent_executor=CharacterAgentExecutor(),
        task_store=InMemoryTaskStore(),
    )

    # 创建A2A服务器应用
    server = A2AStarletteApplication(
        agent_card=agent_card,
        http_handler=request_handler
    )

    # 启动服务器
    print("启动Character Agent A2A服务器...")
    print(f"服务器地址: http://{host}:{port}")
    print(f"AI模型: {model}")
    print(f"写作模式: {config.writing_mode if config else args.writing_mode}")
    print("Agent技能:")
    for skill in agent_card.skills:
        print(f"   - {skill.name}: {skill.description}")
    
    uvicorn.run(server.build(), host=host, port=port)