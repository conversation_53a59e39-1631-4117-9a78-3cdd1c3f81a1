#!/usr/bin/env python3
"""
增强版Plot Agent MCP工具测试脚本 - 测试经典情节分析功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入MCP模块
import plot_agent.plot_mcp as plot_mcp

def test_classic_plot_analysis():
    """测试经典情节分析功能"""
    print("🎭 测试经典情节分析功能...")
    
    # 测试探寻情节
    result = plot_mcp.analyze_classic_plot.__wrapped__(
        plot_type="探寻",
        story_context="一个年轻的考古学家寻找失落的古代文明遗迹，希望证明自己的理论",
        adaptation_requirements="现代背景，加入科技元素"
    )
    
    print("✅ 探寻情节分析结果:")
    print(result[:800] + "..." if len(result) > 800 else result)
    print()

def test_love_story_analysis():
    """测试爱情故事分析功能"""
    print("💕 测试爱情故事分析功能...")
    
    result = plot_mcp.analyze_classic_plot.__wrapped__(
        plot_type="爱情故事",
        story_context="现代都市背景，一个独立的女设计师和一个内向的程序员的爱情故事",
        adaptation_requirements="避免俗套，注重心理描写"
    )
    
    print("✅ 爱情故事分析结果:")
    print(result[:800] + "..." if len(result) > 800 else result)
    print()

def test_revenge_plot_analysis():
    """测试复仇情节分析功能"""
    print("⚔️ 测试复仇情节分析功能...")
    
    result = plot_mcp.analyze_classic_plot.__wrapped__(
        plot_type="复仇",
        story_context="一个正直的警察因为揭露腐败而被陷害，家人受到伤害",
        adaptation_requirements="现代都市背景，避免过度暴力"
    )
    
    print("✅ 复仇情节分析结果:")
    print(result[:800] + "..." if len(result) > 800 else result)
    print()

def test_enhanced_story_outline():
    """测试增强版故事大纲生成"""
    print("📖 测试增强版故事大纲生成...")
    
    result = plot_mcp.generate_story_outline.__wrapped__(
        genre="现代都市",
        theme="成长",
        target_length="长篇",
        structure_type="英雄之旅",
        additional_requirements="结合经典成长情节，主角是刚毕业的大学生"
    )
    
    print("✅ 增强版故事大纲:")
    print(result[:600] + "..." if len(result) > 600 else result)
    print()

def test_enhanced_template_list():
    """测试增强版模板列表"""
    print("📋 测试增强版模板列表...")
    
    result = plot_mcp.get_plot_templates.__wrapped__()
    
    print("✅ 增强版模板列表:")
    print(result[:800] + "..." if len(result) > 800 else result)
    print()

def test_invalid_plot_type():
    """测试无效情节类型的处理"""
    print("❓ 测试无效情节类型处理...")
    
    result = plot_mcp.analyze_classic_plot.__wrapped__(
        plot_type="不存在的情节",
        story_context="测试故事",
        adaptation_requirements="测试要求"
    )
    
    print("✅ 无效情节类型处理结果:")
    print(result[:400] + "..." if len(result) > 400 else result)
    print()

def test_comprehensive_plot_analysis():
    """测试综合情节分析"""
    print("🔍 测试综合情节分析...")
    
    # 测试多个经典情节类型
    plot_types = ["成长", "变形记", "自我发现之旅"]
    
    for plot_type in plot_types:
        result = plot_mcp.analyze_classic_plot.__wrapped__(
            plot_type=plot_type,
            story_context=f"基于{plot_type}的现代故事",
            adaptation_requirements="现代改编"
        )
        
        print(f"✅ {plot_type}情节分析:")
        print(result[:300] + "..." if len(result) > 300 else result)
        print()

def main():
    """运行所有增强功能测试"""
    print("🧪 开始测试增强版Plot Agent功能...")
    print("=" * 60)
    
    try:
        test_classic_plot_analysis()
        test_love_story_analysis()
        test_revenge_plot_analysis()
        test_enhanced_story_outline()
        test_enhanced_template_list()
        test_invalid_plot_type()
        test_comprehensive_plot_analysis()
        
        print("🎉 所有增强功能测试完成！Plot Agent已充分利用经典情节模板。")
        print("\n💡 新增功能:")
        print("   - analyze_classic_plot: 基于19个经典情节的深度分析")
        print("   - 增强的模板系统: 完整利用你提供的丰富资源")
        print("   - 专业的情节结构建议: 基于经典文学理论")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()