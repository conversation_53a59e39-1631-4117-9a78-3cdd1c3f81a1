#!/bin/bash
# 停止所有写作Agent的脚本

echo "停止A2A网文写作助手系统..."

# 停止所有Agent进程
if [ -f "logs/plot_agent.pid" ]; then
    PID=$(cat logs/plot_agent.pid)
    if kill -0 $PID 2>/dev/null; then
        kill $PID
        echo "Plot Agent (PID: $PID) 已停止"
    fi
    rm -f logs/plot_agent.pid
fi

if [ -f "logs/character_agent.pid" ]; then
    PID=$(cat logs/character_agent.pid)
    if kill -0 $PID 2>/dev/null; then
        kill $PID
        echo "Character Agent (PID: $PID) 已停止"
    fi
    rm -f logs/character_agent.pid
fi

if [ -f "logs/content_agent.pid" ]; then
    PID=$(cat logs/content_agent.pid)
    if kill -0 $PID 2>/dev/null; then
        kill $PID
        echo "Content Agent (PID: $PID) 已停止"
    fi
    rm -f logs/content_agent.pid
fi

if [ -f "logs/host_agent.pid" ]; then
    PID=$(cat logs/host_agent.pid)
    if kill -0 $PID 2>/dev/null; then
        kill $PID
        echo "Host Agent (PID: $PID) 已停止"
    fi
    rm -f logs/host_agent.pid
fi

echo "所有Agent已停止！"