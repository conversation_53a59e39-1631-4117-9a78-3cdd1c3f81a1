#!/usr/bin/env python3
"""
真实写作案例测试
测试写作协调器与AI Agent的实际写作能力
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_plot_agent_writing_cases():
    """测试Plot Agent的真实写作案例"""
    print("🎭 测试Plot Agent真实写作案例...")
    print("=" * 60)
    
    try:
        from host_agent.writing_coordinator import get_writing_coordinator, shutdown_writing_coordinator
        
        # 获取写作协调器
        coordinator = await get_writing_coordinator()
        
        # 等待连接稳定
        await asyncio.sleep(2)
        
        # 检查Plot Agent连接状态
        workload = await coordinator.get_agent_workload()
        if 'plot_agent' not in workload or workload['plot_agent']['status'] != 'connected':
            print("❌ Plot Agent未连接，请先启动Plot Agent服务器:")
            print("   python -m plot_agent")
            return False
        
        print("✅ Plot Agent已连接，开始写作案例测试")
        
        # 测试案例1: 现代都市爱情故事大纲
        print("\n📝 案例1: 现代都市爱情故事大纲")
        print("-" * 40)
        
        request1 = "帮我设计一个现代都市爱情故事的大纲，男主是霸道总裁，女主是普通白领，要有误会、分离、重逢的情节"
        
        result1 = await coordinator.coordinate_writing(
            session_id="test_plot_case_1",
            request=request1
        )
        
        if result1.get("success"):
            print("✅ 大纲生成成功!")
            print(f"🤖 执行Agent: {result1['assigned_agent']}")
            print(f"📋 任务类型: {result1['task_type']}")
            
            # 显示生成的大纲内容
            result_content = result1.get('result', {})
            if isinstance(result_content, dict):
                content = result_content.get('content', str(result_content))
            else:
                content = str(result_content)
            
            print(f"📄 生成的大纲:")
            print(content[:500] + "..." if len(content) > 500 else content)
        else:
            print(f"❌ 大纲生成失败: {result1.get('error')}")
        
        # 短暂延迟
        await asyncio.sleep(3)
        
        # 测试案例2: 悬疑推理小说情节冲突分析
        print("\n📝 案例2: 悬疑推理小说情节冲突")
        print("-" * 40)
        
        request2 = "我在写一个悬疑推理小说，主角是私家侦探，现在需要分析故事中的主要冲突和次要冲突，以及如何安排冲突的升级"
        
        result2 = await coordinator.coordinate_writing(
            session_id="test_plot_case_2", 
            request=request2
        )
        
        if result2.get("success"):
            print("✅ 冲突分析成功!")
            print(f"🤖 执行Agent: {result2['assigned_agent']}")
            
            result_content = result2.get('result', {})
            if isinstance(result_content, dict):
                content = result_content.get('content', str(result_content))
            else:
                content = str(result_content)
            
            print(f"📄 冲突分析:")
            print(content[:500] + "..." if len(content) > 500 else content)
        else:
            print(f"❌ 冲突分析失败: {result2.get('error')}")
        
        # 短暂延迟
        await asyncio.sleep(3)
        
        # 测试案例3: 情节转折建议
        print("\n📝 案例3: 玄幻小说情节转折")
        print("-" * 40)
        
        request3 = "我的玄幻修仙小说进展到中期，主角刚突破到筑基期，现在剧情有点平淡，需要一些意想不到的转折和悬念来推动情节发展"
        
        result3 = await coordinator.coordinate_writing(
            session_id="test_plot_case_3",
            request=request3
        )
        
        if result3.get("success"):
            print("✅ 转折建议成功!")
            print(f"🤖 执行Agent: {result3['assigned_agent']}")
            
            result_content = result3.get('result', {})
            if isinstance(result_content, dict):
                content = result_content.get('content', str(result_content))
            else:
                content = str(result_content)
            
            print(f"📄 转折建议:")
            print(content[:500] + "..." if len(content) > 500 else content)
        else:
            print(f"❌ 转折建议失败: {result3.get('error')}")
        
        await shutdown_writing_coordinator()
        
        print("\n🎉 Plot Agent写作案例测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_character_agent_writing_cases():
    """测试Character Agent的真实写作案例"""
    print("\n👥 测试Character Agent真实写作案例...")
    print("=" * 60)
    
    try:
        from host_agent.writing_coordinator import get_writing_coordinator, shutdown_writing_coordinator
        
        # 获取写作协调器
        coordinator = await get_writing_coordinator()
        
        # 等待连接稳定
        await asyncio.sleep(2)
        
        # 检查Character Agent连接状态
        workload = await coordinator.get_agent_workload()
        if 'character_agent' not in workload or workload['character_agent']['status'] != 'connected':
            print("❌ Character Agent未连接，请先启动Character Agent服务器:")
            print("   python -m character_agent")
            return False
        
        print("✅ Character Agent已连接，开始写作案例测试")
        
        # 测试案例1: 霸道总裁男主角创建
        print("\n📝 案例1: 霸道总裁男主角")
        print("-" * 40)
        
        request1 = "为我的现代都市小说创建一个霸道总裁男主角，28岁，有复杂的家庭背景，外冷内热的性格，要有具体的外貌描写和性格特点"
        
        result1 = await coordinator.coordinate_writing(
            session_id="test_char_case_1",
            request=request1
        )
        
        if result1.get("success"):
            print("✅ 角色创建成功!")
            print(f"🤖 执行Agent: {result1['assigned_agent']}")
            
            result_content = result1.get('result', {})
            if isinstance(result_content, dict):
                content = result_content.get('content', str(result_content))
            else:
                content = str(result_content)
            
            print(f"📄 角色设定:")
            print(content[:500] + "..." if len(content) > 500 else content)
        else:
            print(f"❌ 角色创建失败: {result1.get('error')}")
        
        await asyncio.sleep(3)
        
        # 测试案例2: 角色关系网络
        print("\n📝 案例2: 角色关系网络")
        print("-" * 40)
        
        request2 = "帮我设计一个古代言情小说的角色关系网络，包括女主、男主、男二、女二，以及他们之间的复杂关系和情感纠葛"
        
        result2 = await coordinator.coordinate_writing(
            session_id="test_char_case_2",
            request=request2
        )
        
        if result2.get("success"):
            print("✅ 关系网络设计成功!")
            print(f"🤖 执行Agent: {result2['assigned_agent']}")
            
            result_content = result2.get('result', {})
            if isinstance(result_content, dict):
                content = result_content.get('content', str(result_content))
            else:
                content = str(result_content)
            
            print(f"📄 关系网络:")
            print(content[:500] + "..." if len(content) > 500 else content)
        else:
            print(f"❌ 关系网络设计失败: {result2.get('error')}")
        
        await shutdown_writing_coordinator()
        
        print("\n🎉 Character Agent写作案例测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_content_agent_writing_cases():
    """测试Content Agent的真实写作案例"""
    print("\n📝 测试Content Agent真实写作案例...")
    print("=" * 60)
    
    try:
        from host_agent.writing_coordinator import get_writing_coordinator, shutdown_writing_coordinator
        
        # 获取写作协调器
        coordinator = await get_writing_coordinator()
        
        # 等待连接稳定
        await asyncio.sleep(2)
        
        # 检查Content Agent连接状态
        workload = await coordinator.get_agent_workload()
        if 'content_agent' not in workload or workload['content_agent']['status'] != 'connected':
            print("❌ Content Agent未连接，请先启动Content Agent服务器:")
            print("   python -m content_agent")
            return False
        
        print("✅ Content Agent已连接，开始写作案例测试")
        
        # 测试案例1: 咖啡厅相遇场景
        print("\n📝 案例1: 咖啡厅相遇场景")
        print("-" * 40)
        
        request1 = "写一个现代都市小说中男女主角在咖啡厅初次相遇的场景，要有环境描写、人物动作和内心活动，营造浪漫的氛围"
        
        result1 = await coordinator.coordinate_writing(
            session_id="test_content_case_1",
            request=request1
        )
        
        if result1.get("success"):
            print("✅ 场景描写成功!")
            print(f"🤖 执行Agent: {result1['assigned_agent']}")
            
            result_content = result1.get('result', {})
            if isinstance(result_content, dict):
                content = result_content.get('content', str(result_content))
            else:
                content = str(result_content)
            
            print(f"📄 场景内容:")
            print(content[:600] + "..." if len(content) > 600 else content)
        else:
            print(f"❌ 场景描写失败: {result1.get('error')}")
        
        await asyncio.sleep(3)
        
        # 测试案例2: 角色对话生成
        print("\n📝 案例2: 商务谈判对话")
        print("-" * 40)
        
        request2 = "生成一段霸道总裁和商业对手之间的谈判对话，要体现总裁的强势和智慧，对话要自然流畅，有商战的紧张感"
        
        result2 = await coordinator.coordinate_writing(
            session_id="test_content_case_2",
            request=request2
        )
        
        if result2.get("success"):
            print("✅ 对话生成成功!")
            print(f"🤖 执行Agent: {result2['assigned_agent']}")
            
            result_content = result2.get('result', {})
            if isinstance(result_content, dict):
                content = result_content.get('content', str(result_content))
            else:
                content = str(result_content)
            
            print(f"📄 对话内容:")
            print(content[:600] + "..." if len(content) > 600 else content)
        else:
            print(f"❌ 对话生成失败: {result2.get('error')}")
        
        await shutdown_writing_coordinator()
        
        print("\n🎉 Content Agent写作案例测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_complete_writing_workflow():
    """测试完整的写作工作流程"""
    print("\n🔄 测试完整写作工作流程...")
    print("=" * 60)
    
    try:
        from host_agent.writing_coordinator import get_writing_coordinator, shutdown_writing_coordinator
        
        # 获取写作协调器
        coordinator = await get_writing_coordinator()
        
        # 等待连接稳定
        await asyncio.sleep(2)
        
        # 检查所有Agent连接状态
        workload = await coordinator.get_agent_workload()
        connected_agents = [agent_id for agent_id, load in workload.items() if load['status'] == 'connected']
        
        print(f"📊 已连接Agent: {connected_agents}")
        
        if len(connected_agents) == 0:
            print("❌ 没有Agent连接，无法测试完整工作流程")
            return False
        
        # 定义一个完整的小说创作工作流程
        workflow_steps = [
            "设计一个现代都市爱情小说的基本大纲，包含主要情节线",
            "创建男主角：28岁霸道总裁，和女主角：25岁普通白领的基本设定",
            "写一个两人在公司电梯里初次相遇的场景描写"
        ]
        
        print(f"📋 工作流程步骤: {len(workflow_steps)}个")
        
        # 执行工作流程
        session_id = "complete_workflow_test"
        results = []
        
        for i, step in enumerate(workflow_steps, 1):
            print(f"\n📝 执行步骤 {i}/{len(workflow_steps)}: {step}")
            
            # 分析步骤类型
            task_type = coordinator.analyze_writing_request(step)
            print(f"📋 任务类型: {task_type.value}")
            
            # 选择Agent
            available_agents = await coordinator.connection_manager.get_available_agents()
            selected_agent = coordinator.select_best_agent(task_type, available_agents)
            print(f"🤖 选择Agent: {selected_agent}")
            
            if selected_agent not in connected_agents:
                print(f"⚠️ 所需Agent {selected_agent} 未连接，跳过此步骤")
                continue
            
            try:
                # 执行步骤
                result = await coordinator.coordinate_writing(
                    session_id=session_id,
                    request=step,
                    context={"workflow_step": i}
                )
                
                if result.get("success"):
                    print(f"✅ 步骤 {i} 执行成功")
                    
                    # 显示结果预览
                    result_content = result.get('result', {})
                    if isinstance(result_content, dict):
                        content = result_content.get('content', str(result_content))
                    else:
                        content = str(result_content)
                    
                    print(f"📄 结果预览: {content[:200]}...")
                    results.append({"step": i, "success": True, "content": content})
                else:
                    print(f"❌ 步骤 {i} 执行失败: {result.get('error')}")
                    results.append({"step": i, "success": False, "error": result.get('error')})
                
            except Exception as e:
                print(f"❌ 步骤 {i} 执行异常: {e}")
                results.append({"step": i, "success": False, "error": str(e)})
            
            # 步骤间延迟
            await asyncio.sleep(2)
        
        # 显示工作流程总结
        print(f"\n📊 工作流程总结:")
        successful_steps = len([r for r in results if r.get("success", False)])
        print(f"   总步骤: {len(workflow_steps)}")
        print(f"   成功: {successful_steps}")
        print(f"   失败: {len(results) - successful_steps}")
        
        # 显示会话状态
        session_status = await coordinator.get_session_status(session_id)
        if session_status:
            print(f"   会话任务: {session_status['total_tasks']}")
            print(f"   已完成: {session_status['completed_tasks']}")
        
        await shutdown_writing_coordinator()
        
        print("\n🎉 完整写作工作流程测试完成!")
        return successful_steps > 0
        
    except Exception as e:
        print(f"❌ 工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    async def main():
        print("🚀 开始真实写作案例测试...")
        print("📋 请确保已启动需要测试的Agent服务器")
        print("   python -m plot_agent")
        print("   python -m character_agent") 
        print("   python -m content_agent")
        print()
        
        # 测试各个Agent的写作案例
        success1 = await test_plot_agent_writing_cases()
        success2 = await test_character_agent_writing_cases()
        success3 = await test_content_agent_writing_cases()
        success4 = await test_complete_writing_workflow()
        
        print("\n" + "="*60)
        print("📊 测试结果总结:")
        print(f"   Plot Agent写作案例: {'✅ 通过' if success1 else '❌ 失败'}")
        print(f"   Character Agent写作案例: {'✅ 通过' if success2 else '❌ 失败'}")
        print(f"   Content Agent写作案例: {'✅ 通过' if success3 else '❌ 失败'}")
        print(f"   完整写作工作流程: {'✅ 通过' if success4 else '❌ 失败'}")
        
        if any([success1, success2, success3, success4]):
            print("\n🎉 真实AI写作能力验证成功！")
            print("💡 系统能够:")
            print("   - 智能分析写作需求")
            print("   - 生成高质量的写作内容")
            print("   - 协调多Agent完成复杂任务")
            print("   - 支持完整的写作工作流程")
        else:
            print("\n⚠️ 所有测试都未通过，请检查Agent服务器状态")
    
    asyncio.run(main())