#!/usr/bin/env python3
"""
测试单个Agent启动的脚本
"""

import sys
import subprocess
import time
import asyncio
import aiohttp
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_agent_startup():
    """测试Agent启动"""
    print("🧪 测试Plot Agent启动...")
    
    # 启动Plot Agent
    cmd = [sys.executable, '-m', 'plot_agent.__main__', '--port', '10005']
    
    print(f"执行命令: {' '.join(cmd)}")
    
    process = subprocess.Popen(
        cmd,
        cwd=project_root,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        encoding='utf-8',
        errors='ignore'
    )
    
    print(f"进程已启动，PID: {process.pid}")
    
    # 等待一段时间让服务器启动
    print("等待服务器启动...")
    await asyncio.sleep(10)
    
    # 检查进程状态
    if process.poll() is not None:
        stdout, stderr = process.communicate()
        print("❌ 进程已退出")
        print(f"返回码: {process.returncode}")
        if stdout:
            print(f"STDOUT:\n{stdout}")
        if stderr:
            print(f"STDERR:\n{stderr}")
        return False
    else:
        print("✅ 进程仍在运行")
    
    # 尝试连接服务器
    print("尝试连接服务器...")
    try:
        async with aiohttp.ClientSession() as session:
            # Try different endpoints that A2A servers typically support
            endpoints_to_try = [
                'http://127.0.0.1:10005/',
                'http://127.0.0.1:10005/agent',
                'http://127.0.0.1:10005/health',
                'http://127.0.0.1:10005/status'
            ]
            
            success = False
            for endpoint in endpoints_to_try:
                try:
                    async with session.get(endpoint, timeout=aiohttp.ClientTimeout(total=2)) as response:
                        print(f"尝试端点 {endpoint}: {response.status}")
                        if response.status in [200, 404]:  # 200 is good, 404 means server is responding
                            if response.status == 200:
                                try:
                                    data = await response.json()
                                    print(f"✅ 服务器响应成功: {data.get('name', 'Unknown')}")
                                except:
                                    print("✅ 服务器响应成功 (非JSON响应)")
                            else:
                                print("✅ 服务器正在运行 (404响应)")
                            success = True
                            break
                        elif response.status == 405:
                            print("✅ 服务器正在运行 (405 Method Not Allowed)")
                            success = True
                            break
                except Exception as e:
                    print(f"端点 {endpoint} 连接失败: {e}")
                    continue
    except Exception as e:
        print(f"❌ 连接服务器失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        success = False
    
    # 终止进程
    print("终止进程...")
    try:
        process.terminate()
        process.wait(timeout=5)
    except subprocess.TimeoutExpired:
        process.kill()
        process.wait()
    
    return success

if __name__ == "__main__":
    result = asyncio.run(test_agent_startup())
    if result:
        print("🎉 测试成功！")
    else:
        print("❌ 测试失败！")