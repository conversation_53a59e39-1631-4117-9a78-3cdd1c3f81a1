#!/usr/bin/env python3
"""
停止所有Agent服务器的便捷脚本
"""

import subprocess
import sys
import os

def stop_agents_by_port():
    """通过端口停止Agent服务器"""
    ports = [10006, 10003, 10004]  # Plot, Character, Content Agent端口

    print("[SEARCH] 查找并停止Agent服务器...")

    for port in ports:
        try:
            if os.name == 'nt':  # Windows
                # 查找占用端口的进程
                result = subprocess.run(
                    f'netstat -ano | findstr :{port}',
                    shell=True,
                    capture_output=True,
                    text=True
                )
                
                if result.stdout:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if 'LISTENING' in line:
                            parts = line.split()
                            if len(parts) >= 5:
                                pid = parts[-1]
                                try:
                                    # 终止进程
                                    subprocess.run(f'taskkill /F /PID {pid}', shell=True, check=True)
                                    print(f"[OK] 停止端口 {port} 上的进程 (PID: {pid})")
                                except subprocess.CalledProcessError:
                                    print(f"[WARN] 无法停止端口 {port} 上的进程 (PID: {pid})")
                else:
                    print(f"[INFO] 端口 {port} 没有运行的服务")
            else:  # Linux/Mac
                # 查找占用端口的进程
                result = subprocess.run(
                    f'lsof -ti:{port}',
                    shell=True,
                    capture_output=True,
                    text=True
                )
                
                if result.stdout:
                    pids = result.stdout.strip().split('\n')
                    for pid in pids:
                        if pid:
                            try:
                                subprocess.run(f'kill -9 {pid}', shell=True, check=True)
                                print(f"[OK] 停止端口 {port} 上的进程 (PID: {pid})")
                            except subprocess.CalledProcessError:
                                print(f"[WARN] 无法停止端口 {port} 上的进程 (PID: {pid})")
                else:
                    print(f"[INFO] 端口 {port} 没有运行的服务")

        except Exception as e:
            print(f"[ERROR] 检查端口 {port} 时出错: {e}")

def stop_agents_by_name():
    """通过进程名停止Agent服务器"""
    print("\n[SEARCH] 通过进程名查找Agent...")

    try:
        if os.name == 'nt':  # Windows
            # 查找Python进程中包含agent的
            result = subprocess.run(
                'wmic process where "name=\'python.exe\'" get processid,commandline /format:csv',
                shell=True,
                capture_output=True,
                text=True
            )
            
            if result.stdout:
                lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
                for line in lines:
                    if line.strip() and 'agent' in line.lower():
                        parts = line.split(',')
                        if len(parts) >= 3:
                            pid = parts[-1].strip()
                            if pid and pid.isdigit():
                                try:
                                    subprocess.run(f'taskkill /F /PID {pid}', shell=True, check=True)
                                    print(f"[OK] 停止Agent进程 (PID: {pid})")
                                except subprocess.CalledProcessError:
                                    print(f"[WARN] 无法停止进程 (PID: {pid})")
        else:  # Linux/Mac
            result = subprocess.run(
                "ps aux | grep -i agent | grep python",
                shell=True,
                capture_output=True,
                text=True
            )
            
            if result.stdout:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if 'grep' not in line:  # 排除grep命令本身
                        parts = line.split()
                        if len(parts) >= 2:
                            pid = parts[1]
                            try:
                                subprocess.run(f'kill -9 {pid}', shell=True, check=True)
                                print(f"[OK] 停止Agent进程 (PID: {pid})")
                            except subprocess.CalledProcessError:
                                print(f"[WARN] 无法停止进程 (PID: {pid})")

    except Exception as e:
        print(f"[ERROR] 通过进程名停止时出错: {e}")

def main():
    """主函数"""
    print("[STOP] 停止所有Agent服务器...")
    print("=" * 50)

    # 方法1: 通过端口停止
    stop_agents_by_port()

    # 方法2: 通过进程名停止
    stop_agents_by_name()

    print("\n[COMPLETE] Agent停止操作完成")
    print("[TIP] 如果还有Agent在运行，请手动通过任务管理器停止")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n[INTERRUPT] 用户中断")
    except Exception as e:
        print(f"[ERROR] 停止失败: {e}")
        sys.exit(1)