#!/usr/bin/env python3
"""
Plot Agent核心逻辑
集成MCP工具和AI API，提供智能情节规划服务
"""

import os
import sys
import json
import re
import asyncio
from collections.abc import AsyncGenerator
from typing import Any
from pathlib import Path

# 导入AI API
try:
    from openai import AsyncOpenAI
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False
    print("OpenAI库未安装，将使用MCP工具模式")

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("python-dotenv库未安装")

# 导入MCP工具函数
import plot_agent.plot_mcp as plot_mcp_module


class PlotAgent:
    """Plot Agent核心类 - 集成AI API的智能情节规划"""

    def __init__(self):
        """初始化Plot Agent"""
        self.name = "Plot Agent"
        self.description = "专业的网文情节规划Agent，集成AI智能分析"
        
        # 初始化AI客户端
        self._init_ai_client()
        
        # 技能映射
        self.skills_map = {
            'generate_story_outline': self._handle_story_outline,
            'create_chapter_structure': self._handle_chapter_structure,
            'analyze_plot_conflicts': self._handle_plot_conflicts,
            'suggest_plot_twists': self._handle_plot_twists,
            'analyze_classic_plot': self._handle_classic_plot,
            'get_plot_templates': self._handle_plot_templates,
        }

    def _init_ai_client(self):
        """初始化AI客户端"""
        self.ai_client = None
        
        if not AI_AVAILABLE:
            print("AI功能不可用，将使用MCP工具模式")
            return
            
        try:
            # 尝试使用专属AI配置
            try:
                from config.ai_api_config import get_agent_config
                config = get_agent_config('plot_agent')
                
                if config and config.api_key:
                    self.ai_client = AsyncOpenAI(
                        api_key=config.api_key,
                        base_url=config.base_url
                    )
                    self.model = config.model
                    print("Plot Agent专属AI客户端初始化成功")
                    print(f"   模型: {self.model}")
                    print(f"   密钥: {config.api_key[:20]}...")
                    print(f"   备用密钥: {len(config.backup_keys)}个")
                    return
            except ImportError:
                print("AI配置管理器不可用，使用默认配置")
            
            # 回退到默认配置
            api_key = os.getenv('OPENAI_API_KEY')
            base_url = os.getenv('OPENAI_BASE_URL')
            self.model = os.getenv('PLOT_AGENT_MODEL', os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo'))
            
            if not api_key:
                print(" 未找到API密钥，将使用MCP工具模式")
                return
            
            # 创建AI客户端
            self.ai_client = AsyncOpenAI(
                api_key=api_key,
                base_url=base_url
            )
            print(f" Plot Agent AI客户端初始化成功，模型: {self.model}")
            
        except Exception as e:
            print(f" AI客户端初始化失败: {e}，将使用MCP工具模式")
            self.ai_client = None

    async def stream(self, query: str) -> AsyncGenerator[dict[str, Any], None]:
        """流式处理用户查询"""
        try:
            # 分析用户意图并选择合适的处理方法
            skill_id, params = self._analyze_intent(query)
            
            if skill_id in self.skills_map:
                # 调用对应的技能处理函数
                async for chunk in self.skills_map[skill_id](params):
                    yield chunk
            else:
                # 通用情节规划建议
                async for chunk in self._handle_general_plot_advice(query):
                    yield chunk
                    
        except Exception as e:
            yield {
                'content': f'处理请求时出错: {str(e)}',
                'done': True,
            }

    def _analyze_intent(self, query: str) -> tuple[str, dict]:
        """分析用户意图，返回技能ID和参数"""
        query_lower = query.lower()
        
        # 故事大纲生成
        if any(keyword in query_lower for keyword in ['大纲', '故事结构', '情节框架', '故事框架']):
            params = self._extract_story_params(query)
            return 'generate_story_outline', params
            
        # 章节结构
        elif any(keyword in query_lower for keyword in ['章节', '结构', '分章', '章节安排']):
            params = self._extract_chapter_params(query)
            return 'create_chapter_structure', params
            
        # 情节冲突
        elif any(keyword in query_lower for keyword in ['冲突', '矛盾', '对立', '张力']):
            params = self._extract_conflict_params(query)
            return 'analyze_plot_conflicts', params
            
        # 情节转折
        elif any(keyword in query_lower for keyword in ['转折', '反转', '惊喜', '意外', '转机']):
            params = self._extract_twist_params(query)
            return 'suggest_plot_twists', params
            
        # 经典情节分析
        elif any(keyword in query_lower for keyword in ['经典', '模式', '套路', '类型', '参考']):
            params = self._extract_classic_params(query)
            return 'analyze_classic_plot', params
            
        # 模板查询
        elif any(keyword in query_lower for keyword in ['模板', '例子', '示例', '参考']):
            return 'get_plot_templates', {}
            
        else:
            # 默认为故事大纲生成
            params = self._extract_story_params(query)
            return 'generate_story_outline', params

    def _extract_story_params(self, query: str) -> dict:
        """从查询中提取故事参数"""
        params = {
            'genre': '',
            'theme': '',
            'target_length': '中篇',
            'structure_type': '三幕式',
            'additional_requirements': query
        }
        
        # 提取题材
        genre_patterns = {
            '现代都市': ['都市', '现代', '城市'],
            '古代言情': ['古代', '古风', '宫廷'],
            '科幻': ['科幻', '未来', '太空'],
            '玄幻': ['玄幻', '修仙', '仙侠'],
            '悬疑': ['悬疑', '推理', '犯罪'],
            '历史': ['历史', '穿越'],
        }
        
        for genre, keywords in genre_patterns.items():
            if any(keyword in query for keyword in keywords):
                params['genre'] = genre
                break
        
        # 提取主题
        theme_patterns = {
            '爱情': ['爱情', '恋爱', '感情'],
            '成长': ['成长', '励志', '奋斗'],
            '复仇': ['复仇', '报仇'],
            '权谋': ['权谋', '政治', '宫斗'],
            '冒险': ['冒险', '探险', '历险'],
        }
        
        for theme, keywords in theme_patterns.items():
            if any(keyword in query for keyword in keywords):
                params['theme'] = theme
                break
        
        return params

    def _extract_chapter_params(self, query: str) -> dict:
        """从查询中提取章节参数"""
        params = {
            'story_outline': '',
            'total_chapters': 20,
            'chapter_length': '3000-5000字',
            'pacing_style': '稳步推进'
        }
        
        # 提取章节数量
        chapter_match = re.search(r'(\d+)章', query)
        if chapter_match:
            params['total_chapters'] = int(chapter_match.group(1))
        
        return params

    def _extract_conflict_params(self, query: str) -> dict:
        """从查询中提取冲突参数"""
        return {
            'story_context': query,
            'conflict_types': None
        }

    def _extract_twist_params(self, query: str) -> dict:
        """从查询中提取转折参数"""
        return {
            'current_plot': query,
            'twist_type': '反转与惊喜',
            'target_chapter': None
        }

    def _extract_classic_params(self, query: str) -> dict:
        """从查询中提取经典情节参数"""
        return {
            'plot_type': '',
            'story_context': query,
            'adaptation_requirements': ''
        }

    async def _handle_story_outline(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """处理故事大纲生成 - 集成AI增强"""
        yield {'content': ' 正在生成故事大纲...\n\n', 'done': False}
        
        try:
            # 如果有AI客户端，使用AI增强生成
            if self.ai_client:
                async for chunk in self._ai_enhanced_story_outline(params):
                    yield chunk
            else:
                # 使用MCP工具生成
                result = await self._call_mcp_function(
                    'generate_story_outline',
                    genre=params.get('genre', ''),
                    theme=params.get('theme', ''),
                    target_length=params.get('target_length', '中篇'),
                    structure_type=params.get('structure_type', '三幕式'),
                    additional_requirements=params.get('additional_requirements', '')
                )
                
                chunks = self._split_content(result)
                for chunk in chunks:
                    yield {'content': chunk, 'done': False}
                
        except Exception as e:
            yield {'content': f'生成故事大纲时出错: {str(e)}', 'done': False}
        
        yield {'content': '', 'done': True}

    async def _ai_enhanced_story_outline(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """AI增强的故事大纲生成"""
        try:
            # 构建AI提示词
            prompt = f"""作为专业的网文情节规划专家，请为以下要求生成详细的故事大纲：

题材类型：{params.get('genre', '现代都市')}
故事主题：{params.get('theme', '成长')}
目标长度：{params.get('target_length', '中篇')}
结构类型：{params.get('structure_type', '三幕式')}
额外要求：{params.get('additional_requirements', '')}

请生成一个包含以下内容的详细故事大纲：
1. 故事概述和核心冲突
2. 主要角色设定
3. 三幕结构详细规划
4. 关键情节转折点
5. 主题表达和情感线索
6. 章节分配建议

要求：
- 内容丰富，具有可操作性
- 符合网文读者喜好
- 情节紧凑，冲突明确
- 角色鲜明，成长线清晰"""

            # 调用AI API
            response = await self.ai_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一位专业的网文情节规划专家，擅长创作引人入胜的故事大纲。"},
                    {"role": "user", "content": prompt}
                ],
                stream=True,
                temperature=0.7
            )
            
            yield {'content': '🤖 AI正在分析和生成...\n\n', 'done': False}
            
            # 流式返回AI响应
            async for chunk in response:
                if chunk.choices[0].delta.content:
                    yield {'content': chunk.choices[0].delta.content, 'done': False}
                    
        except Exception as e:
            yield {'content': f'AI增强生成失败，使用基础模式: {str(e)}\n\n', 'done': False}
            
            # 回退到MCP工具
            result = await self._call_mcp_function(
                'generate_story_outline',
                genre=params.get('genre', ''),
                theme=params.get('theme', ''),
                target_length=params.get('target_length', '中篇'),
                structure_type=params.get('structure_type', '三幕式'),
                additional_requirements=params.get('additional_requirements', '')
            )
            
            chunks = self._split_content(result)
            for chunk in chunks:
                yield {'content': chunk, 'done': False}

    async def _handle_chapter_structure(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """处理章节结构创建"""
        yield {'content': ' 正在创建章节结构...\n\n', 'done': False}
        
        try:
            result = await self._call_mcp_function(
                'create_chapter_structure',
                story_outline=params.get('story_outline', ''),
                total_chapters=params.get('total_chapters', 20),
                chapter_length=params.get('chapter_length', '3000-5000字'),
                pacing_style=params.get('pacing_style', '稳步推进')
            )
            
            chunks = self._split_content(result)
            for chunk in chunks:
                yield {'content': chunk, 'done': False}
                
        except Exception as e:
            yield {'content': f'创建章节结构时出错: {str(e)}', 'done': False}
        
        yield {'content': '', 'done': True}

    async def _handle_plot_conflicts(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """处理情节冲突分析"""
        yield {'content': ' 正在分析情节冲突...\n\n', 'done': False}
        
        try:
            result = await self._call_mcp_function(
                'analyze_plot_conflicts',
                story_context=params.get('story_context', ''),
                conflict_types=params.get('conflict_types')
            )
            
            chunks = self._split_content(result)
            for chunk in chunks:
                yield {'content': chunk, 'done': False}
                
        except Exception as e:
            yield {'content': f'分析情节冲突时出错: {str(e)}', 'done': False}
        
        yield {'content': '', 'done': True}

    async def _handle_plot_twists(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """处理情节转折建议"""
        yield {'content': ' 正在设计情节转折...\n\n', 'done': False}
        
        try:
            result = await self._call_mcp_function(
                'suggest_plot_twists',
                current_plot=params.get('current_plot', ''),
                twist_type=params.get('twist_type', '反转与惊喜'),
                target_chapter=params.get('target_chapter')
            )
            
            chunks = self._split_content(result)
            for chunk in chunks:
                yield {'content': chunk, 'done': False}
                
        except Exception as e:
            yield {'content': f'设计情节转折时出错: {str(e)}', 'done': False}
        
        yield {'content': '', 'done': True}

    async def _handle_classic_plot(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """处理经典情节分析"""
        yield {'content': ' 正在分析经典情节...\n\n', 'done': False}
        
        try:
            result = await self._call_mcp_function(
                'analyze_classic_plot',
                plot_type=params.get('plot_type', ''),
                story_context=params.get('story_context', ''),
                adaptation_requirements=params.get('adaptation_requirements', '')
            )
            
            chunks = self._split_content(result)
            for chunk in chunks:
                yield {'content': chunk, 'done': False}
                
        except Exception as e:
            yield {'content': f'分析经典情节时出错: {str(e)}', 'done': False}
        
        yield {'content': '', 'done': True}

    async def _handle_plot_templates(self, params: dict) -> AsyncGenerator[dict[str, Any], None]:
        """处理情节模板查询"""
        yield {'content': ' 正在获取情节模板...\n\n', 'done': False}
        
        try:
            result = await self._call_mcp_function('get_plot_templates')
            
            chunks = self._split_content(result)
            for chunk in chunks:
                yield {'content': chunk, 'done': False}
                
        except Exception as e:
            yield {'content': f'获取情节模板时出错: {str(e)}', 'done': False}
        
        yield {'content': '', 'done': True}

    async def _handle_general_plot_advice(self, query: str) -> AsyncGenerator[dict[str, Any], None]:
        """处理通用情节建议 - 集成AI增强"""
        yield {'content': ' 正在分析您的需求...\n\n', 'done': False}
        
        try:
            # 如果有AI客户端，使用AI提供个性化建议
            if self.ai_client:
                async for chunk in self._ai_enhanced_general_advice(query):
                    yield chunk
            else:
                # 使用预设建议
                advice = f"""基于您的问题："{query}"

我建议您可以尝试以下几个方面：

1. **明确故事类型**: 确定您想要创作的题材（如现代都市、古代言情、科幻玄幻等）
2. **确定核心主题**: 思考故事要表达的核心主题（如爱情、成长、复仇等）
3. **设计主要冲突**: 构建推动故事发展的核心矛盾
4. **规划情节节奏**: 安排起承转合的情节发展节奏

如果您需要更具体的帮助，可以告诉我：
- 您想创作什么类型的故事？
- 故事的主要角色是谁？
- 您希望表达什么主题？

我可以为您提供更详细的情节规划建议！"""

                chunks = self._split_content(advice)
                for chunk in chunks:
                    yield {'content': chunk, 'done': False}
                    
        except Exception as e:
            yield {'content': f'生成建议时出错: {str(e)}', 'done': False}
        
        yield {'content': '', 'done': True}

    async def _ai_enhanced_general_advice(self, query: str) -> AsyncGenerator[dict[str, Any], None]:
        """AI增强的通用建议"""
        try:
            prompt = f"""作为专业的网文创作顾问，请针对以下用户问题提供专业的情节规划建议：

用户问题：{query}

请提供：
1. 问题分析和理解
2. 具体的创作建议
3. 可参考的经典案例
4. 实用的创作技巧
5. 下一步行动建议

要求：
- 建议具体可操作
- 符合网文创作特点
- 考虑读者喜好
- 提供创新思路"""

            response = await self.ai_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一位经验丰富的网文创作顾问，擅长提供专业的情节规划建议。"},
                    {"role": "user", "content": prompt}
                ],
                stream=True,
                temperature=0.7
            )
            
            yield {'content': '🤖 AI正在为您定制建议...\n\n', 'done': False}
            
            async for chunk in response:
                if chunk.choices[0].delta.content:
                    yield {'content': chunk.choices[0].delta.content, 'done': False}
                    
        except Exception as e:
            yield {'content': f'AI建议生成失败: {str(e)}\n\n', 'done': False}

    async def _call_mcp_function(self, func_name, **kwargs):
        """调用MCP工具函数的辅助方法"""
        try:
            # 从模块中获取FunctionTool对象，然后调用其fn属性
            if hasattr(plot_mcp_module, func_name):
                func_tool = getattr(plot_mcp_module, func_name)
                if hasattr(func_tool, 'fn'):
                    return func_tool.fn(**kwargs)
                else:
                    return func_tool(**kwargs)
            else:
                return f"未找到MCP工具函数: {func_name}"
        except Exception as e:
            return f"调用MCP工具时出错: {str(e)}"

    def _split_content(self, content: str, chunk_size: int = 100) -> list[str]:
        """将内容分割成小块用于流式输出"""
        if len(content) <= chunk_size:
            return [content]
        
        chunks = []
        for i in range(0, len(content), chunk_size):
            chunks.append(content[i:i + chunk_size])
        
        return chunks