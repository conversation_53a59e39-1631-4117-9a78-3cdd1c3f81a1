[project]
name = "host-agent"
version = "0.1.0"
description = "Multi-agent system with Azure AI Agents integration for routing and task delegation"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "click>=8.2.1",
    "gradio>=5.33.2",
    "mcp>=1.9.4",
    "azure-ai-agents>=1.1.0b1",
    "azure-identity>=1.15.0",
    "semantic-kernel>=1.33.0",
    "httpx>=0.25.0",
    "python-dotenv>=1.0.0",
    "a2a-sdk>=0.2.7",
]
