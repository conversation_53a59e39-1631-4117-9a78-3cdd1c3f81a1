[project]
name = "a2a-sample-headless-agent-auth"
version = "0.1.0"
description = "Example of an agent using Auth0 Client-Initiated Backchannel Authentication (CIBA) from a tool, and agent-level authorization via the OAuth 2.0 Client Credentials flow."
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "a2a-sdk @ git+https://github.com/google/a2a-python.git@main",
    "asyncclick>=8.1.8",
    "auth0-ai-langchain==1.0.0b2",
    "auth0-api-python==1.0.0b3",
    "auth0-fastapi-api>=1.0.0b3",
    "auth0-python>=4.9.0",
    "click>=8.2.0",
    "fastapi>=0.115.12",
    "httpx>=0.28.1",
    "langchain-google-genai>=2.1.4",
    "langgraph>=0.4.3",
    "pydantic>=2.11.4",
    "python-dotenv>=1.1.0",
]