#!/usr/bin/env python3
"""
启动所有Agent的便捷脚本
"""

import sys
import os
import subprocess
import time
import signal
from pathlib import Path
from typing import List, Dict
import threading

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.config_manager import get_config_manager

class MultiAgentLauncher:
    """多Agent启动器"""
    
    def __init__(self):
        self.config_manager = get_config_manager()
        self.processes: Dict[str, subprocess.Popen] = {}
        self.running = False
    
    def start_agent(self, agent_name: str) -> bool:
        """启动单个Agent"""
        config = self.config_manager.get_agent_config(agent_name)
        if not config or not config.enabled:
            print(f"⏸️ {agent_name} 已禁用，跳过启动")
            return True
        
        print(f"🚀 启动 {agent_name}...")
        
        try:
            # 构建启动命令
            cmd = [sys.executable, "-m", f"{agent_name}.__main__"]
            
            # 添加命令行参数
            if config.host != "localhost":
                cmd.extend(["--host", config.host])
            cmd.extend(["--port", str(config.port)])
            if config.model_name != "gemini-2.5-pro-preview-06-05":
                cmd.extend(["--model", config.model_name])
            if config.writing_mode != "ai":
                cmd.extend(["--writing-mode", config.writing_mode])
            
            # 设置环境变量
            env = os.environ.copy()
            env['AGENT_NAME'] = agent_name
            env['AGENT_PORT'] = str(config.port)
            env['WRITING_MODE'] = config.writing_mode
            
            # 启动进程
            process = subprocess.Popen(
                cmd,
                cwd=project_root,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes[agent_name] = process
            print(f"✅ {agent_name} 启动成功 (PID: {process.pid}, 端口: {config.port})")
            
            # 等待一下确保启动成功
            time.sleep(2)
            if process.poll() is not None:
                stdout, stderr = process.communicate()
                print(f"❌ {agent_name} 启动失败:")
                if stdout:
                    print(f"STDOUT: {stdout}")
                if stderr:
                    print(f"STDERR: {stderr}")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ 启动 {agent_name} 时出错: {e}")
            return False
    
    def stop_agent(self, agent_name: str):
        """停止单个Agent"""
        if agent_name in self.processes:
            process = self.processes[agent_name]
            print(f"🛑 停止 {agent_name}...")
            
            try:
                # 尝试优雅关闭
                process.terminate()
                process.wait(timeout=5)
                print(f"✅ {agent_name} 已停止")
            except subprocess.TimeoutExpired:
                # 强制关闭
                process.kill()
                process.wait()
                print(f"⚡ {agent_name} 已强制停止")
            except Exception as e:
                print(f"❌ 停止 {agent_name} 时出错: {e}")
            
            del self.processes[agent_name]
    
    def start_all(self) -> bool:
        """启动所有Agent"""
        print("🏠 A2A多Agent写作助手启动器")
        print("=" * 50)
        
        # 验证配置
        errors = self.config_manager.validate_config()
        if errors:
            print("⚠️ 配置验证失败:")
            for error in errors:
                print(f"  - {error}")
            return False
        
        # 获取启用的Agent列表
        enabled_agents = self.config_manager.get_enabled_agents()
        
        # 按顺序启动Agent (Host Agent最后启动)
        ordered_agents = [name for name in enabled_agents if name != 'host_agent']
        if 'host_agent' in enabled_agents:
            ordered_agents.append('host_agent')
        
        success_count = 0
        
        for agent_name in ordered_agents:
            if self.start_agent(agent_name):
                success_count += 1
                time.sleep(3)  # 给每个Agent一些启动时间
        
        print(f"\n📊 启动结果: {success_count}/{len(ordered_agents)} 个Agent启动成功")
        
        if success_count > 0:
            self.running = True
            print("\n🎉 系统启动完成!")
            print("📋 运行中的Agent:")
            for agent_name in self.processes:
                config = self.config_manager.get_agent_config(agent_name)
                print(f"  - {agent_name}: http://{config.host}:{config.port}")
            
            return True
        else:
            print("\n❌ 没有Agent启动成功")
            return False
    
    def stop_all(self):
        """停止所有Agent"""
        print("\n🛑 停止所有Agent...")
        
        # 先停止Host Agent
        if 'host_agent' in self.processes:
            self.stop_agent('host_agent')
        
        # 再停止其他Agent
        for agent_name in list(self.processes.keys()):
            self.stop_agent(agent_name)
        
        self.running = False
        print("✅ 所有Agent已停止")
    
    def monitor_agents(self):
        """监控Agent状态"""
        while self.running:
            time.sleep(10)
            
            failed_agents = []
            for agent_name, process in list(self.processes.items()):
                if process.poll() is not None:
                    failed_agents.append(agent_name)
            
            if failed_agents:
                print(f"\n⚠️ 检测到Agent异常退出: {', '.join(failed_agents)}")
                for agent_name in failed_agents:
                    del self.processes[agent_name]
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n📡 收到信号 {signum}，正在关闭系统...")
        self.stop_all()
        sys.exit(0)

def main():
    """主函数"""
    launcher = MultiAgentLauncher()
    
    # 设置信号处理
    signal.signal(signal.SIGINT, launcher.signal_handler)
    signal.signal(signal.SIGTERM, launcher.signal_handler)
    
    # 启动所有Agent
    success = launcher.start_all()
    
    if success:
        print("\n按 Ctrl+C 停止系统...")
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=launcher.monitor_agents)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        try:
            while launcher.running:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        
        launcher.stop_all()
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()