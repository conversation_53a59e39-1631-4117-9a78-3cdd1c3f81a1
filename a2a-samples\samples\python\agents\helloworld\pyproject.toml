[project]
name = "helloworld"
version = "0.1.0"
description = "HelloWorld agent example that only returns Messages"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "a2a-sdk>=0.2.12",
    "click>=8.1.8",
    "dotenv>=0.9.9",
    "httpx>=0.28.1",
    "langchain-google-genai>=2.1.4",
    "langgraph>=0.4.1",
    "pydantic>=2.11.4",
    "python-dotenv>=1.1.0",
    "uvicorn>=0.34.2",
]

[tool.hatch.build.targets.wheel]
packages = ["."]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
