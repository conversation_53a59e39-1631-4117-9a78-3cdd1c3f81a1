[project]
name = "a2a-samples"
version = "0.1.0"
description = "Agent2Agent samples"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "a2a-sdk>=0.2.9",
    "httpx>=0.28.1",
    "httpx-sse>=0.4.0",
    "jwcrypto>=1.5.6",
    "pydantic>=2.10.6",
    "pyjwt>=2.10.1",
    "sse-starlette>=2.2.1",
    "starlette>=0.46.1",
    "typing-extensions>=4.12.2",
    "uvicorn>=0.34.0",
    "veo-video-sample-agent",
]

[tool.hatch.build.targets.wheel]
packages = ["common", "hosts"]

[tool.uv.workspace]
members = [
    "agents/crewai",
    "agents/adk_expense_reimbursement",
    "agents/marvin",
    "hosts/cli",
    "hosts/extended_agent_card_cli",
    "hosts/multiagent",
    "agents/airbnb_planner_multiagent",
    "agents/llama_index_file_chat",
    "agents/semantickernel",
    "agents/mindsdb",
    "agents/extended_agent_card_adk",
    "agents/veo_video_gen",
    "agents/dice_agent_grpc",
    "agents/ag2",
]

[tool.uv.sources]
veo-video-sample-agent = { workspace = true }

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[dependency-groups]
dev = ["pytest>=8.3.5", "pytest-mock>=3.14.0", "ruff>=0.11.2"]
