#!/usr/bin/env python3
"""
测试Character Agent的角色原型集成功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_character_agent():
    """测试Character Agent功能"""
    print("👤 测试Character Agent角色原型集成功能...")
    print("=" * 60)
    
    try:
        # 测试导入
        print("📦 测试模块导入...")
        from character_agent.agent import CharacterAgent, CHARACTER_ARCHETYPES
        print("✅ 模块导入成功")
        
        # 检查角色原型模板
        print("\n📋 检查角色原型模板...")
        if CHARACTER_ARCHETYPES:
            male_count = len(CHARACTER_ARCHETYPES.get('male', {}))
            female_count = len(CHARACTER_ARCHETYPES.get('female', {}))
            supporting_count = len(CHARACTER_ARCHETYPES.get('supporting', {}))
            print(f"✅ 已加载角色原型模板: 男性{male_count}个, 女性{female_count}个, 配角{supporting_count}个")
            
            # 显示部分原型名称
            if male_count > 0:
                print(f"   男性原型: {', '.join(list(CHARACTER_ARCHETYPES['male'].keys())[:3])}...")
            if female_count > 0:
                print(f"   女性原型: {', '.join(list(CHARACTER_ARCHETYPES['female'].keys())[:3])}...")
        else:
            print("❌ 角色原型模板加载失败")
            return False
        
        # 创建Character Agent实例
        print("\n👤 创建Character Agent实例...")
        agent = CharacterAgent()
        print(f"✅ Agent名称: {agent.name}")
        
        # 测试1: 获取角色原型概述
        print("\n" + "="*60)
        print("🎯 测试1: 获取角色原型概述")
        print("="*60)
        
        test_query1 = "显示所有角色原型模板"
        print(f"📝 测试查询: {test_query1}")
        print("\n📄 响应:")
        print("-" * 40)
        
        result_parts1 = []
        async for event in agent.stream(test_query1):
            if event['content']:
                print(event['content'], end='', flush=True)
                result_parts1.append(event['content'])
            if event['done']:
                break
        
        result1 = ''.join(result_parts1)
        print(f"\n\n✅ 响应完成 ({len(result1)} 字符)")
        
        # 测试2: 获取特定性别的角色原型
        print("\n" + "="*60)
        print("🎯 测试2: 获取男性角色原型")
        print("="*60)
        
        test_query2 = "显示男性角色原型"
        print(f"📝 测试查询: {test_query2}")
        print("\n📄 响应:")
        print("-" * 40)
        
        result_parts2 = []
        async for event in agent.stream(test_query2):
            if event['content']:
                print(event['content'], end='', flush=True)
                result_parts2.append(event['content'])
            if event['done']:
                break
        
        result2 = ''.join(result_parts2)
        print(f"\n\n✅ 响应完成 ({len(result2)} 字符)")
        
        # 测试3: 创建角色（结合原型）
        print("\n" + "="*60)
        print("🎯 测试3: 创建角色（结合原型模板）")
        print("="*60)
        
        test_query3 = "创建一个霸道总裁男主角色，适合现代都市言情小说"
        print(f"📝 测试查询: {test_query3}")
        print("\n📄 响应:")
        print("-" * 40)
        
        result_parts3 = []
        async for event in agent.stream(test_query3):
            if event['content']:
                print(event['content'], end='', flush=True)
                result_parts3.append(event['content'])
            if event['done']:
                break
        
        result3 = ''.join(result_parts3)
        print(f"\n\n✅ 响应完成 ({len(result3)} 字符)")
        
        print("\n🎉 Character Agent角色原型集成功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_character_agent())
    if not success:
        sys.exit(1)