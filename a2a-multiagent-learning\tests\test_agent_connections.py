#!/usr/bin/env python3
"""
Agent连接测试器
测试各个Agent的实际连接和响应
"""

import asyncio
import aiohttp
import sys
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
import json
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.config_manager import get_config_manager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgentConnectionTester:
    """Agent连接测试器"""
    
    def __init__(self):
        self.config_manager = get_config_manager()
        self.session: Optional[aiohttp.ClientSession] = None
        self.test_results: Dict[str, Dict[str, Any]] = {}
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def test_all_agents(self) -> Dict[str, Any]:
        """测试所有Agent的连接"""
        print("🔗 测试Agent连接状态")
        print("=" * 50)
        
        agents = ["plot_agent", "character_agent", "content_agent", "host_agent"]
        results = {}
        
        for agent_name in agents:
            print(f"\n🤖 测试 {agent_name}...")
            result = await self.test_agent_connection(agent_name)
            results[agent_name] = result
            
            if result["connected"]:
                print(f"  ✅ 连接成功 - {result['url']}")
                if result.get("card_info"):
                    print(f"  📋 Agent: {result['card_info'].get('name', 'Unknown')}")
                    print(f"  🔧 技能数: {len(result['card_info'].get('skills', []))}")
            else:
                print(f"  ❌ 连接失败 - {result.get('error', 'Unknown error')}")
        
        # 统计结果
        connected_count = sum(1 for r in results.values() if r["connected"])
        total_count = len(results)
        
        print(f"\n📊 连接测试结果: {connected_count}/{total_count} Agent可连接")
        
        return {
            "success": connected_count > 0,
            "connected_agents": connected_count,
            "total_agents": total_count,
            "results": results
        }
    
    async def test_agent_connection(self, agent_name: str) -> Dict[str, Any]:
        """测试单个Agent的连接"""
        config = self.config_manager.get_agent_config(agent_name)
        if not config:
            return {
                "connected": False,
                "error": "Agent配置不存在"
            }
        
        if not config.enabled:
            return {
                "connected": False,
                "error": "Agent已禁用"
            }
        
        url = f"http://{config.host}:{config.port}"
        
        try:
            # 测试基本连接
            async with self.session.get(f"{url}/") as response:
                if response.status == 200:
                    # 尝试获取Agent Card信息
                    card_info = await self.get_agent_card(url)
                    
                    return {
                        "connected": True,
                        "url": url,
                        "status_code": response.status,
                        "card_info": card_info,
                        "response_time": time.time()
                    }
                else:
                    return {
                        "connected": False,
                        "url": url,
                        "error": f"HTTP {response.status}"
                    }
        
        except aiohttp.ClientConnectorError:
            return {
                "connected": False,
                "url": url,
                "error": "连接被拒绝 - Agent可能未启动"
            }
        except asyncio.TimeoutError:
            return {
                "connected": False,
                "url": url,
                "error": "连接超时"
            }
        except Exception as e:
            return {
                "connected": False,
                "url": url,
                "error": f"连接异常: {str(e)}"
            }
    
    async def get_agent_card(self, base_url: str) -> Optional[Dict[str, Any]]:
        """获取Agent Card信息"""
        try:
            async with self.session.get(f"{base_url}/agent-card") as response:
                if response.status == 200:
                    return await response.json()
        except Exception as e:
            logger.debug(f"获取Agent Card失败: {e}")
        
        return None
    
    async def test_agent_functionality(self, agent_name: str) -> Dict[str, Any]:
        """测试Agent功能"""
        config = self.config_manager.get_agent_config(agent_name)
        if not config:
            return {"success": False, "error": "Agent配置不存在"}
        
        url = f"http://{config.host}:{config.port}"
        
        # 根据Agent类型选择测试请求
        test_requests = {
            "plot_agent": "帮我设计一个简单的故事大纲",
            "character_agent": "创建一个简单的角色",
            "content_agent": "写一个简短的场景描写",
            "host_agent": "你好，请介绍一下你的功能"
        }
        
        test_request = test_requests.get(agent_name, "测试请求")
        
        try:
            # 构建A2A请求
            request_data = {
                "message": {
                    "content": test_request
                },
                "context_id": f"test_{agent_name}_{int(time.time())}",
                "task_id": f"task_{int(time.time())}"
            }
            
            start_time = time.time()
            
            async with self.session.post(
                f"{url}/tasks",
                json=request_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                end_time = time.time()
                response_time = end_time - start_time
                
                if response.status == 200:
                    result = await response.json()
                    return {
                        "success": True,
                        "response_time": response_time,
                        "response": result,
                        "agent": agent_name
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status}",
                        "response_time": response_time
                    }
        
        except Exception as e:
            return {
                "success": False,
                "error": f"功能测试失败: {str(e)}"
            }
    
    async def test_all_agent_functionality(self) -> Dict[str, Any]:
        """测试所有Agent的功能"""
        print("\n🧪 测试Agent功能")
        print("=" * 50)
        
        agents = ["plot_agent", "character_agent", "content_agent", "host_agent"]
        results = {}
        
        for agent_name in agents:
            print(f"\n🔧 测试 {agent_name} 功能...")
            result = await self.test_agent_functionality(agent_name)
            results[agent_name] = result
            
            if result["success"]:
                response_time = result.get("response_time", 0)
                print(f"  ✅ 功能正常 - 响应时间: {response_time:.2f}s")
            else:
                print(f"  ❌ 功能异常 - {result.get('error', 'Unknown error')}")
        
        # 统计结果
        working_count = sum(1 for r in results.values() if r["success"])
        total_count = len(results)
        
        print(f"\n📊 功能测试结果: {working_count}/{total_count} Agent功能正常")
        
        return {
            "success": working_count > 0,
            "working_agents": working_count,
            "total_agents": total_count,
            "results": results
        }
    
    async def test_agent_performance(self, agent_name: str, iterations: int = 3) -> Dict[str, Any]:
        """测试Agent性能"""
        config = self.config_manager.get_agent_config(agent_name)
        if not config:
            return {"success": False, "error": "Agent配置不存在"}
        
        url = f"http://{config.host}:{config.port}"
        response_times = []
        
        test_request = f"性能测试请求 - {agent_name}"
        
        for i in range(iterations):
            try:
                request_data = {
                    "message": {"content": test_request},
                    "context_id": f"perf_test_{agent_name}_{i}",
                    "task_id": f"perf_task_{i}"
                }
                
                start_time = time.time()
                
                async with self.session.post(
                    f"{url}/tasks",
                    json=request_data,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    
                    end_time = time.time()
                    response_time = end_time - start_time
                    
                    if response.status == 200:
                        response_times.append(response_time)
                    
            except Exception as e:
                logger.warning(f"性能测试第{i+1}次失败: {e}")
        
        if response_times:
            avg_time = sum(response_times) / len(response_times)
            min_time = min(response_times)
            max_time = max(response_times)
            
            return {
                "success": True,
                "iterations": len(response_times),
                "avg_response_time": avg_time,
                "min_response_time": min_time,
                "max_response_time": max_time,
                "response_times": response_times
            }
        else:
            return {
                "success": False,
                "error": "所有性能测试都失败了"
            }
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行综合测试"""
        print("🚀 开始Agent综合测试")
        print("=" * 60)
        
        # 1. 连接测试
        connection_results = await self.test_all_agents()
        
        # 2. 功能测试
        functionality_results = await self.test_all_agent_functionality()
        
        # 3. 性能测试
        print("\n⚡ 测试Agent性能")
        print("=" * 50)
        
        performance_results = {}
        connected_agents = [
            name for name, result in connection_results["results"].items()
            if result["connected"]
        ]
        
        for agent_name in connected_agents:
            print(f"\n📈 测试 {agent_name} 性能...")
            perf_result = await self.test_agent_performance(agent_name)
            performance_results[agent_name] = perf_result
            
            if perf_result["success"]:
                avg_time = perf_result["avg_response_time"]
                print(f"  ✅ 平均响应时间: {avg_time:.2f}s")
            else:
                print(f"  ❌ 性能测试失败: {perf_result.get('error')}")
        
        # 综合评估
        overall_score = self.calculate_overall_score(
            connection_results,
            functionality_results,
            performance_results
        )
        
        return {
            "overall_score": overall_score,
            "connection_results": connection_results,
            "functionality_results": functionality_results,
            "performance_results": performance_results,
            "summary": self.generate_test_summary(
                connection_results,
                functionality_results,
                performance_results,
                overall_score
            )
        }
    
    def calculate_overall_score(self, connection_results, functionality_results, performance_results) -> float:
        """计算总体评分"""
        # 连接测试权重: 40%
        connection_score = (connection_results["connected_agents"] / connection_results["total_agents"]) * 40
        
        # 功能测试权重: 40%
        functionality_score = (functionality_results["working_agents"] / functionality_results["total_agents"]) * 40
        
        # 性能测试权重: 20%
        performance_score = 0
        if performance_results:
            good_performance_count = sum(
                1 for result in performance_results.values()
                if result.get("success") and result.get("avg_response_time", 10) < 5  # 5秒内算良好
            )
            performance_score = (good_performance_count / len(performance_results)) * 20
        
        return connection_score + functionality_score + performance_score
    
    def generate_test_summary(self, connection_results, functionality_results, performance_results, overall_score) -> str:
        """生成测试总结"""
        summary_lines = []
        
        summary_lines.append(f"📊 Agent系统测试总结")
        summary_lines.append(f"=" * 40)
        summary_lines.append(f"总体评分: {overall_score:.1f}/100")
        summary_lines.append("")
        
        # 连接状态
        conn_count = connection_results["connected_agents"]
        conn_total = connection_results["total_agents"]
        summary_lines.append(f"🔗 连接状态: {conn_count}/{conn_total} Agent可连接")
        
        # 功能状态
        func_count = functionality_results["working_agents"]
        func_total = functionality_results["total_agents"]
        summary_lines.append(f"🔧 功能状态: {func_count}/{func_total} Agent功能正常")
        
        # 性能状态
        if performance_results:
            perf_count = sum(1 for r in performance_results.values() if r.get("success"))
            perf_total = len(performance_results)
            summary_lines.append(f"⚡ 性能状态: {perf_count}/{perf_total} Agent性能良好")
        
        summary_lines.append("")
        
        # 评级
        if overall_score >= 80:
            summary_lines.append("🎉 系统状态: 优秀")
        elif overall_score >= 60:
            summary_lines.append("✅ 系统状态: 良好")
        elif overall_score >= 40:
            summary_lines.append("⚠️ 系统状态: 一般")
        else:
            summary_lines.append("❌ 系统状态: 需要改进")
        
        return "\n".join(summary_lines)

async def main():
    """主函数"""
    async with AgentConnectionTester() as tester:
        results = await tester.run_comprehensive_test()
        
        print("\n" + "=" * 60)
        print(results["summary"])
        
        # 保存测试结果
        try:
            results_file = project_root / "tests" / "agent_connection_test_results.json"
            results_file.parent.mkdir(exist_ok=True)
            
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"\n📄 测试结果已保存到: {results_file}")
            
        except Exception as e:
            logger.error(f"保存测试结果失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())