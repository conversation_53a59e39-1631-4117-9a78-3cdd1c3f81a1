#!/usr/bin/env python3
"""
测试Character Agent A2A服务器启动功能
"""

import asyncio
import sys
import time
import subprocess
import requests
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_character_agent_server():
    """测试Character Agent A2A服务器启动"""
    print("👤 测试Character Agent A2A服务器启动...")
    print("=" * 60)
    
    try:
        # 测试服务器组件
        print("📦 测试服务器组件...")
        from character_agent import __main__
        from character_agent.agent_executor import CharacterAgentExecutor
        print("✅ 服务器组件导入成功")
        
        # 测试Agent Card配置
        print("\n📋 测试Agent Card配置...")
        
        # 模拟创建Agent Card（不实际启动服务器）
        from a2a.types import AgentCapabilities, AgentCard, AgentSkill
        
        # 定义技能（复制自__main__.py）
        skills = [
            AgentSkill(
                id='create_character',
                name='创建角色',
                description='根据用户需求创建详细的角色档案，包括外貌、性格、背景等',
                tags=['角色创建', '角色设定', '人物塑造'],
                examples=['创建一个霸道总裁男主角色，适合现代都市言情小说']
            ),
            AgentSkill(
                id='get_character_archetypes',
                name='角色原型推荐',
                description='提供专业的角色原型模板和个性化角色设计建议',
                tags=['角色原型', '人物模板', '性格分析'],
                examples=['推荐适合现代都市小说的女主角原型']
            )
        ]
        
        agent_card = AgentCard(
            name='Character Agent - 角色设定专家',
            description='专业的网文角色设定Agent，集成AI智能分析和角色原型模板',
            url='http://localhost:10003/',
            version='1.0.0',
            defaultInputModes=['text'],
            defaultOutputModes=['text'],
            capabilities=AgentCapabilities(streaming=True),
            skills=skills,
        )
        
        print(f"✅ Agent名称: {agent_card.name}")
        print(f"✅ Agent版本: {agent_card.version}")
        print(f"✅ 服务器地址: {agent_card.url}")
        print(f"✅ 技能数量: {len(agent_card.skills)}")
        print("✅ 技能列表:")
        for skill in agent_card.skills:
            print(f"   - {skill.name}: {skill.description}")
        
        # 测试Executor
        print("\n🔧 测试Executor...")
        executor = CharacterAgentExecutor()
        print(f"✅ Executor创建成功")
        print(f"✅ 内部Agent: {executor.agent.name}")
        
        print("\n🎉 Character Agent A2A服务器组件测试完成！")
        print("\n💡 要启动实际服务器，请运行:")
        print("   python -m character_agent")
        print("   或")
        print("   python scripts/start_character_agent.py")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_character_agent_server())
    if not success:
        sys.exit(1)