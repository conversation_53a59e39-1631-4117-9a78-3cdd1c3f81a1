#!/usr/bin/env python3
"""
测试与真实Agent服务器的连接
需要先启动对应的Agent服务器
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_real_connections():
    """测试与真实Agent服务器的连接"""
    print("🔗 测试与真实Agent服务器的连接...")
    print("=" * 60)
    
    try:
        from host_agent.remote_agent_connection import (
            get_connection_manager,
            shutdown_connection_manager
        )
        
        # 获取连接管理器
        manager = await get_connection_manager()
        
        # 等待一段时间让连接尝试完成
        print("⏳ 等待连接尝试完成...")
        await asyncio.sleep(3)
        
        # 检查Agent状态
        print("\n📊 检查Agent连接状态...")
        agent_status = await manager.get_all_agent_status()
        
        connected_count = 0
        for agent_id, status in agent_status.items():
            status_icon = "✅" if status['status'] == 'connected' else "❌"
            print(f"{status_icon} {agent_id}:")
            print(f"   名称: {status['name']}")
            print(f"   状态: {status['status']}")
            print(f"   URL: {status['url']}")
            print(f"   技能数量: {status['skills_count']}")
            if status['last_error']:
                print(f"   错误: {status['last_error']}")
            print()
            
            if status['status'] == 'connected':
                connected_count += 1
        
        print(f"📈 连接统计: {connected_count}/{len(agent_status)} 个Agent已连接")
        
        # 如果有连接的Agent，测试发送任务
        if connected_count > 0:
            print("\n📤 测试发送任务到连接的Agent...")
            available_agents = await manager.get_available_agents()
            
            for agent_id in available_agents:
                print(f"\n🎯 测试发送任务到 {agent_id}...")
                
                # 构造测试任务
                test_task = {
                    "message": "这是一个测试任务",
                    "context": {
                        "test": True,
                        "agent_id": agent_id
                    }
                }
                
                # 发送任务
                result = await manager.send_task_to_agent(agent_id, test_task)
                if result:
                    print(f"✅ 任务发送成功: {result}")
                else:
                    print(f"❌ 任务发送失败")
        
        # 测试Agent技能查询
        print("\n🛠️ 查询Agent技能...")
        for agent_id in agent_status.keys():
            skills = await manager.get_agent_skills(agent_id)
            if skills:
                print(f"\n{agent_id} 的技能:")
                for skill in skills:
                    print(f"   - {skill.get('name', 'Unknown')}: {skill.get('description', 'No description')}")
        
        # 清理
        await shutdown_connection_manager()
        
        print(f"\n🎉 真实连接测试完成！")
        print(f"💡 提示:")
        if connected_count == 0:
            print("   - 没有Agent连接成功，请确保对应的Agent服务器已启动")
            print("   - 启动命令:")
            print("     python -m plot_agent")
            print("     python -m character_agent") 
            print("     python -m content_agent")
        else:
            print(f"   - 成功连接到 {connected_count} 个Agent")
            print("   - 连接管理器工作正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_agent_discovery():
    """测试Agent自动发现功能"""
    print("\n🔍 测试Agent自动发现功能...")
    print("=" * 40)
    
    try:
        from host_agent.remote_agent_connection import RemoteAgentConnections
        
        manager = RemoteAgentConnections()
        
        # 测试发现常见端口上的Agent
        discovery_urls = [
            "http://localhost:10001/",
            "http://localhost:10002/", 
            "http://localhost:10003/",
            "http://localhost:10004/",
            "http://localhost:10005/"
        ]
        
        print(f"🔍 扫描端口: {[url.split(':')[-1].rstrip('/') for url in discovery_urls]}")
        
        discovered = await manager.discover_agents(discovery_urls)
        
        print(f"✅ 发现 {len(discovered)} 个Agent:")
        for agent_id in discovered:
            print(f"   - {agent_id}")
        
        # 显示发现的Agent状态
        if discovered:
            print("\n📊 发现的Agent状态:")
            status = await manager.get_all_agent_status()
            for agent_id in discovered:
                if agent_id in status:
                    agent_status = status[agent_id]
                    print(f"   {agent_id}: {agent_status['status']} ({agent_status['name']})")
        
        await manager.shutdown()
        
        print("✅ Agent发现测试完成")
        return True
        
    except Exception as e:
        print(f"❌ Agent发现测试失败: {e}")
        return False

if __name__ == "__main__":
    async def main():
        print("🚀 开始真实Agent连接测试...")
        print("📋 测试前请确保已启动需要测试的Agent服务器")
        print()
        
        success1 = await test_real_connections()
        success2 = await test_agent_discovery()
        
        if success1 and success2:
            print("\n🎉 所有真实连接测试通过！")
        else:
            print("\n⚠️ 部分测试未通过（可能是因为Agent服务器未启动）")
    
    asyncio.run(main())