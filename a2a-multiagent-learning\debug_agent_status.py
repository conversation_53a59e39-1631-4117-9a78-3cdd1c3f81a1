#!/usr/bin/env python3
"""
调试Agent状态的脚本
"""

import subprocess
import time
import asyncio
import aiohttp
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def check_agent_status():
    """检查Agent状态"""
    print("🔍 启动单个Agent进行调试...")
    
    # 启动一个Agent
    cmd = [sys.executable, '-m', 'plot_agent.__main__', '--port', '10006']
    
    print(f"执行命令: {' '.join(cmd)}")
    
    process = subprocess.Popen(
        cmd,
        cwd=project_root,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        encoding='utf-8',
        errors='ignore'
    )
    
    print(f"进程已启动，PID: {process.pid}")
    
    # 等待启动
    print("等待15秒让服务器启动...")
    await asyncio.sleep(15)
    
    # 检查进程状态
    if process.poll() is not None:
        stdout, stderr = process.communicate()
        print("❌ 进程已退出")
        print(f"返回码: {process.returncode}")
        if stdout:
            print(f"STDOUT:\n{stdout}")
        if stderr:
            print(f"STDERR:\n{stderr}")
        return False
    else:
        print("✅ 进程仍在运行")
    
    # 尝试连接
    print("尝试连接服务器...")
    endpoints_to_try = [
        'http://127.0.0.1:10006/',
        'http://localhost:10006/',
        'http://127.0.0.1:10006/health',
        'http://127.0.0.1:10006/status'
    ]
    
    success = False
    try:
        async with aiohttp.ClientSession() as session:
            for endpoint in endpoints_to_try:
                try:
                    print(f"尝试连接: {endpoint}")
                    async with session.get(endpoint, timeout=aiohttp.ClientTimeout(total=5)) as response:
                        print(f"✅ 连接成功: {endpoint} -> HTTP {response.status}")
                        if response.status in [200, 404, 405]:
                            success = True
                            break
                except Exception as e:
                    print(f"❌ 连接失败: {endpoint} -> {e}")
    except Exception as e:
        print(f"❌ 会话创建失败: {e}")
    
    # 终止进程
    print("终止进程...")
    try:
        process.terminate()
        process.wait(timeout=5)
    except subprocess.TimeoutExpired:
        process.kill()
        process.wait()
    
    return success

if __name__ == "__main__":
    result = asyncio.run(check_agent_status())
    if result:
        print("🎉 Agent可以正常启动和连接！")
    else:
        print("❌ Agent启动或连接失败！")