{"version": "0.2.0", "configurations": [{"name": "Debug HelloWorld Agent", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/samples/python/agents/helloworld/__main__.py", "console": "integratedTerminal", "justMyCode": false, "env": {"PYTHONPATH": "${workspaceFolder}"}, "cwd": "${workspaceFolder}/samples/python/agents/helloworld", "args": ["--host", "localhost", "--port", "9999"]}]}