#!/usr/bin/env python3
"""
Plot Agent A2A客户端测试
使用A2A SDK的客户端测试Plot Agent服务器
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_a2a_client():
    """测试A2A客户端与Plot Agent服务器的通信"""
    print("🧪 开始测试Plot Agent A2A客户端...")
    print("=" * 50)
    
    try:
        # 导入A2A客户端
        from a2a.client import A2AClient
        from a2a.types import Message, TextPart
        
        print("📦 A2A客户端模块导入成功")
        
        # 创建客户端
        client = A2AClient("http://localhost:10001")
        print("🔗 A2A客户端创建成功")
        
        # 获取Agent卡片
        print("\n📋 获取Agent卡片...")
        try:
            agent_card = await client.get_agent_card()
            print(f"✅ Agent名称: {agent_card.name}")
            print(f"✅ Agent描述: {agent_card.description}")
            print(f"✅ Agent版本: {agent_card.version}")
            print(f"✅ 技能数量: {len(agent_card.skills)}")
            
            print("\n🛠️ 可用技能:")
            for skill in agent_card.skills:
                print(f"   - {skill.name}: {skill.description}")
        
        except Exception as e:
            print(f"❌ 获取Agent卡片失败: {e}")
            print("💡 请确保Plot Agent A2A服务器正在运行:")
            print("   python plot_agent/__main_a2a__.py")
            return False
        
        # 测试发送消息
        print("\n📝 测试发送消息...")
        test_message = Message(
            role="user",
            parts=[TextPart(text="帮我生成一个现代都市爱情故事的大纲")]
        )
        
        try:
            task = await client.send_message(test_message)
            print(f"✅ 任务创建成功，ID: {task.id}")
            print(f"✅ 任务状态: {task.status.state}")
            
            # 检查任务结果
            if task.artifacts:
                print(f"✅ 收到 {len(task.artifacts)} 个结果")
                for i, artifact in enumerate(task.artifacts):
                    if artifact.parts:
                        content = artifact.parts[0].text if hasattr(artifact.parts[0], 'text') else str(artifact.parts[0])
                        print(f"📄 结果 {i+1} 预览: {content[:200]}...")
            else:
                print("⚠️ 暂无结果，任务可能仍在处理中")
        
        except Exception as e:
            print(f"❌ 发送消息失败: {e}")
            return False
        
        print("\n🎉 A2A客户端测试完成！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入A2A SDK失败: {e}")
        print("💡 请确保已安装a2a-sdk:")
        print("   pip install a2a-sdk")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🚀 Plot Agent A2A客户端测试")
    print("=" * 50)
    
    success = await test_a2a_client()
    
    if success:
        print("\n✅ 所有测试通过！")
        print("\n💡 使用说明:")
        print("1. 启动Plot Agent A2A服务器: python plot_agent/__main_a2a__.py")
        print("2. 运行此测试: python tests/test_plot_a2a_client.py")
        print("3. 或使用CLI客户端: cd ../a2a-samples/samples/python/hosts/cli && uv run . --agent http://localhost:10001")
    else:
        print("\n❌ 测试失败")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())