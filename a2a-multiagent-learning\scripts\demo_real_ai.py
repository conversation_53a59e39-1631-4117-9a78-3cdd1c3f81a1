#!/usr/bin/env python3
"""
真实AI API演示脚本
展示如何使用真实的AI API与各个Agent交互
"""

import asyncio
import aiohttp
import json
import time
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class RealAIDemo:
    """真实AI API演示类"""
    
    def __init__(self):
        self.agent_urls = {
            'plot_agent': 'http://localhost:10002',
            'character_agent': 'http://localhost:10003', 
            'content_agent': 'http://localhost:10004',
            'host_agent': 'http://localhost:10001'
        }
    
    async def run_demo(self):
        """运行演示"""
        print("🎭 A2A多Agent系统真实AI API演示")
        print("=" * 60)
        print("本演示将展示如何使用真实的AI API与各个Agent交互")
        print("请确保所有Agent服务器正在运行并配置了有效的API密钥")
        print("=" * 60)
        
        # 检查Agent连接状态
        await self.check_agents()
        
        # 演示各个Agent的功能
        await self.demo_plot_agent()
        await self.demo_character_agent()
        await self.demo_content_agent()
        await self.demo_host_agent()
        
        print("\n🎉 演示完成！")
        print("您可以查看生成的内容，体验真实AI API的强大能力。")
    
    async def check_agents(self):
        """检查Agent状态"""
        print("\n🔍 检查Agent连接状态...")
        print("-" * 40)
        
        async with aiohttp.ClientSession() as session:
            for agent_name, url in self.agent_urls.items():
                try:
                    async with session.get(f"{url}/", timeout=aiohttp.ClientTimeout(total=3)) as response:
                        if response.status == 200:
                            agent_card = await response.json()
                            print(f"✅ {agent_name}: {agent_card.get('name', 'Unknown')}")
                        else:
                            print(f"❌ {agent_name}: HTTP {response.status}")
                except Exception as e:
                    print(f"🔴 {agent_name}: 离线 - {str(e)}")
    
    async def demo_plot_agent(self):
        """演示Plot Agent"""
        print("\n🎭 Plot Agent演示 - 情节规划")
        print("-" * 40)
        
        request = "帮我设计一个科幻爱情故事的大纲，背景设定在2050年的火星殖民地"
        
        print(f"📝 用户请求: {request}")
        print("⏳ Plot Agent正在思考...")
        
        try:
            result = await self.call_agent('plot_agent', request)
            
            if result.get('success'):
                print("✅ Plot Agent响应成功！")
                print("🤖 AI生成的故事大纲:")
                print("-" * 30)
                
                # 提取并格式化响应内容
                response_text = self.extract_response_text(result.get('response', {}))
                print(response_text[:500] + "..." if len(response_text) > 500 else response_text)
                
            else:
                print(f"❌ Plot Agent响应失败: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Plot Agent调用异常: {e}")
    
    async def demo_character_agent(self):
        """演示Character Agent"""
        print("\n👥 Character Agent演示 - 角色设定")
        print("-" * 40)
        
        request = "为我的科幻爱情故事创建一个女主角，她是火星殖民地的植物学家，聪明独立"
        
        print(f"📝 用户请求: {request}")
        print("⏳ Character Agent正在创建角色...")
        
        try:
            result = await self.call_agent('character_agent', request)
            
            if result.get('success'):
                print("✅ Character Agent响应成功！")
                print("🤖 AI创建的角色档案:")
                print("-" * 30)
                
                response_text = self.extract_response_text(result.get('response', {}))
                print(response_text[:500] + "..." if len(response_text) > 500 else response_text)
                
            else:
                print(f"❌ Character Agent响应失败: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Character Agent调用异常: {e}")
    
    async def demo_content_agent(self):
        """演示Content Agent"""
        print("\n📝 Content Agent演示 - 内容生成")
        print("-" * 40)
        
        request = "写一个科幻小说的开头场景，描写火星殖民地的实验室，女主角正在研究新的植物品种"
        
        print(f"📝 用户请求: {request}")
        print("⏳ Content Agent正在创作...")
        
        try:
            result = await self.call_agent('content_agent', request)
            
            if result.get('success'):
                print("✅ Content Agent响应成功！")
                print("🤖 AI生成的场景内容:")
                print("-" * 30)
                
                response_text = self.extract_response_text(result.get('response', {}))
                print(response_text[:600] + "..." if len(response_text) > 600 else response_text)
                
            else:
                print(f"❌ Content Agent响应失败: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Content Agent调用异常: {e}")
    
    async def demo_host_agent(self):
        """演示Host Agent"""
        print("\n🏠 Host Agent演示 - 智能协调")
        print("-" * 40)
        
        request = "帮我创作一个完整的科幻爱情小说开头，包括故事背景、主要角色和第一章内容"
        
        print(f"📝 用户请求: {request}")
        print("⏳ Host Agent正在协调多个Agent...")
        print("   (这可能需要较长时间，因为需要协调多个AI Agent)")
        
        try:
            result = await self.call_agent('host_agent', request, timeout=120)
            
            if result.get('success'):
                print("✅ Host Agent协调成功！")
                print("🤖 AI协调生成的完整内容:")
                print("-" * 30)
                
                response_text = self.extract_response_text(result.get('response', {}))
                print(response_text[:800] + "..." if len(response_text) > 800 else response_text)
                
            else:
                print(f"❌ Host Agent协调失败: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Host Agent调用异常: {e}")
    
    async def call_agent(self, agent_name: str, request: str, timeout: int = 60) -> dict:
        """调用指定Agent"""
        try:
            async with aiohttp.ClientSession() as session:
                request_data = {
                    "message": request,
                    "context_id": f"demo_{agent_name}_{int(time.time())}",
                    "task_id": f"demo_task_{int(time.time())}"
                }
                
                async with session.post(
                    f"{self.agent_urls[agent_name]}/execute",
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=timeout)
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        return {"success": True, "response": result}
                    else:
                        return {"success": False, "error": f"HTTP {response.status}"}
                        
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def extract_response_text(self, response_data: dict) -> str:
        """从响应数据中提取文本内容"""
        try:
            # 尝试不同的响应格式
            if isinstance(response_data, dict):
                # 检查是否有artifacts
                if 'artifacts' in response_data:
                    artifacts = response_data['artifacts']
                    if artifacts and len(artifacts) > 0:
                        return artifacts[0].get('text', str(response_data))
                
                # 检查是否有直接的文本内容
                if 'text' in response_data:
                    return response_data['text']
                
                # 检查是否有message
                if 'message' in response_data:
                    return response_data['message']
            
            # 如果都没有，返回整个响应的字符串表示
            return str(response_data)
            
        except Exception:
            return str(response_data)

async def main():
    """主函数"""
    demo = RealAIDemo()
    
    print("🚀 准备开始真实AI API演示...")
    
    # 询问用户确认
    try:
        confirm = input("\n是否继续演示? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("❌ 用户取消演示")
            return
    except KeyboardInterrupt:
        print("\n❌ 用户中断")
        return
    
    await demo.run_demo()

if __name__ == "__main__":
    asyncio.run(main())