#!/usr/bin/env python3
"""
Plot Agent A2A服务器
基于A2A SDK的标准实现
"""

# 抑制websockets相关的DeprecationWarning
import warnings
warnings.filterwarnings("ignore", message="websockets.legacy is deprecated.*", category=DeprecationWarning)
warnings.filterwarnings("ignore", message="websockets.server.WebSocketServerProtocol is deprecated.*", category=DeprecationWarning)

import argparse
import os
import sys
from pathlib import Path
import uvicorn

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入配置管理器
try:
    from config.config_manager import get_config_manager
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False
from a2a.server.apps import A2AStarletteApplication
from a2a.server.request_handlers import DefaultRequestHandler
from a2a.server.tasks import InMemoryTaskStore
from a2a.types import (
    AgentCapabilities,
    AgentCard,
    AgentSkill,
)
from .agent_executor import PlotAgentExecutor


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="Plot Agent A2A服务器")
    parser.add_argument('--host', type=str, default='localhost', help='服务器主机地址')
    parser.add_argument('--port', type=int, default=10002, help='服务器端口号')
    parser.add_argument('--model', type=str, help='AI模型名称')
    parser.add_argument('--writing-mode', type=str, default='ai', help='写作模式')
    parser.add_argument('--config', type=str, help='配置文件路径')
    return parser.parse_args()


if __name__ == '__main__':
    # 解析命令行参数
    args = parse_arguments()
    
    # 加载配置
    config = None
    if CONFIG_AVAILABLE:
        config_manager = get_config_manager()
        config = config_manager.get_agent_config('plot_agent')
        
        # 应用命令行覆盖
        if config:
            if args.host != 'localhost':
                config.host = args.host
            if args.port != 10002:
                config.port = args.port
            if args.model:
                config.model_name = args.model
            if args.writing_mode != 'ai':
                config.writing_mode = args.writing_mode
    
    # 确定最终配置
    host = config.host if config else args.host
    port = config.port if config else args.port
    model = config.model_name if config else (args.model or 'gemini-2.5-pro-preview-06-05')
    
    # 定义Plot Agent的技能
    story_outline_skill = AgentSkill(
        id='generate_story_outline',
        name='生成故事大纲',
        description='根据题材、主题等要求生成完整的故事大纲',
        tags=['故事大纲', '情节规划', '创作'],
        examples=[
            '帮我生成一个现代都市爱情故事的大纲',
            '创建一个科幻冒险小说的故事结构',
            '设计一个古代宫廷权谋故事的情节'
        ],
    )
    
    chapter_structure_skill = AgentSkill(
        id='create_chapter_structure',
        name='创建章节结构',
        description='基于故事大纲创建详细的章节结构和节奏安排',
        tags=['章节结构', '情节节奏', '创作规划'],
        examples=[
            '为我的故事大纲创建20章的章节结构',
            '设计章节的情节节奏和转折点',
            '规划每章的主要情节和字数分配'
        ],
    )
    
    plot_conflict_skill = AgentSkill(
        id='analyze_plot_conflicts',
        name='分析情节冲突',
        description='分析故事中的各种冲突类型，提供冲突升级建议',
        tags=['情节冲突', '戏剧张力', '故事分析'],
        examples=[
            '分析我的故事中的主要冲突类型',
            '如何增强故事的戏剧张力',
            '设计人物之间的冲突关系'
        ],
    )
    
    plot_twist_skill = AgentSkill(
        id='suggest_plot_twists',
        name='建议情节转折',
        description='为故事情节提供转折点和惊喜元素的建议',
        tags=['情节转折', '惊喜元素', '故事创新'],
        examples=[
            '为我的故事设计一个意外的转折',
            '在第10章加入什么样的反转比较好',
            '如何让读者感到惊喜而不是突兀'
        ],
    )
    
    classic_plot_skill = AgentSkill(
        id='analyze_classic_plot',
        name='分析经典情节',
        description='分析经典情节模式，提供改编和创新建议',
        tags=['经典情节', '情节模式', '创作参考'],
        examples=[
            '分析英雄之旅的情节模式',
            '如何将经典的复仇故事现代化',
            '参考经典爱情故事的情节结构'
        ],
    )

    # 创建Agent卡片
    agent_card = AgentCard(
        name='Plot Agent - 情节规划专家',
        description='专业的网文情节规划Agent，提供故事大纲生成、章节结构设计、情节冲突分析、转折点建议等全方位的情节规划服务',
        url=f'http://{host}:{port}/',
        version='1.0.0',
        defaultInputModes=['text'],
        defaultOutputModes=['text'],
        capabilities=AgentCapabilities(streaming=True),
        skills=[
            story_outline_skill,
            chapter_structure_skill,
            plot_conflict_skill,
            plot_twist_skill,
            classic_plot_skill
        ],
    )

    # 创建请求处理器
    request_handler = DefaultRequestHandler(
        agent_executor=PlotAgentExecutor(),
        task_store=InMemoryTaskStore(),
    )

    # 创建A2A服务器应用
    server = A2AStarletteApplication(
        agent_card=agent_card,
        http_handler=request_handler
    )

    # 启动服务器
    print("启动Plot Agent A2A服务器...")
    print(f"服务器地址: http://{host}:{port}")
    print(f"AI模型: {model}")
    print(f"写作模式: {config.writing_mode if config else args.writing_mode}")
    print("Agent技能:")
    for skill in agent_card.skills:
        print(f"   - {skill.name}: {skill.description}")
    
    uvicorn.run(server.build(), host=host, port=port)