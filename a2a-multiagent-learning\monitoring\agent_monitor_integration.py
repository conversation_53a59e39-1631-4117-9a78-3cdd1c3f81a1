#!/usr/bin/env python3
"""
Agent监控集成模块
将通信监控和质量评估集成到现有的Agent中
"""

import asyncio
import time
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
import functools
import inspect

from .communication_monitor import get_communication_monitor, CommunicationEvent
from .quality_assessor import get_quality_assessor

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgentMonitorMixin:
    """Agent监控混入类"""
    
    def __init__(self, agent_name: str):
        self.agent_name = agent_name
        self.monitor = get_communication_monitor()
        self.quality_assessor = get_quality_assessor()
        self._active_tasks: Dict[str, Dict[str, Any]] = {}
        
        logger.info(f"📊 Agent {agent_name} 监控已启用")
    
    def start_task_monitoring(self, task_id: str, context_id: str, task_type: str,
                            total_steps: int = 1, metadata: Optional[Dict[str, Any]] = None):
        """开始任务监控"""
        self._active_tasks[task_id] = {
            "context_id": context_id,
            "task_type": task_type,
            "start_time": datetime.now(),
            "total_steps": total_steps,
            "completed_steps": 0,
            "metadata": metadata or {}
        }
        
        self.monitor.log_task_start(
            task_id=task_id,
            context_id=context_id,
            task_type=task_type,
            involved_agents=[self.agent_name],
            total_steps=total_steps,
            metadata=metadata
        )
    
    def update_task_progress(self, task_id: str, steps_completed: int, 
                           status: str = "working", metadata: Optional[Dict[str, Any]] = None):
        """更新任务进度"""
        if task_id in self._active_tasks:
            self._active_tasks[task_id]["completed_steps"] = steps_completed
            self._active_tasks[task_id]["status"] = status
            
            self.monitor.log_task_progress(
                task_id=task_id,
                steps_completed=steps_completed,
                status=status,
                metadata=metadata
            )
    
    def complete_task_monitoring(self, task_id: str, success: bool = True,
                               result_content: Optional[str] = None,
                               user_feedback: Optional[str] = None,
                               metadata: Optional[Dict[str, Any]] = None):
        """完成任务监控"""
        quality_score = None
        
        # 如果有结果内容，进行质量评估
        if result_content and success:
            try:
                task_info = self._active_tasks.get(task_id, {})
                task_type = task_info.get("task_type", "general")
                
                quality_metrics = self.quality_assessor.assess_content(
                    content=result_content,
                    content_type=task_type,
                    context=task_info.get("metadata")
                )
                
                quality_score = quality_metrics.overall_score
                
                # 记录质量评估结果
                logger.info(f"📊 任务 {task_id} 质量评分: {quality_score:.1f}")
                if quality_metrics.suggestions:
                    logger.info(f"💡 改进建议: {'; '.join(quality_metrics.suggestions[:3])}")
                
            except Exception as e:
                logger.error(f"质量评估失败: {e}")
        
        self.monitor.log_task_complete(
            task_id=task_id,
            success=success,
            quality_score=quality_score,
            user_feedback=user_feedback,
            metadata=metadata
        )
        
        # 清理任务记录
        if task_id in self._active_tasks:
            del self._active_tasks[task_id]
    
    def log_agent_request(self, target_agent: str, task_id: str, context_id: str,
                         request_data: Dict[str, Any], metadata: Optional[Dict[str, Any]] = None):
        """记录Agent请求"""
        self.monitor.log_request(
            source_agent=self.agent_name,
            target_agent=target_agent,
            task_id=task_id,
            context_id=context_id,
            request_data=request_data,
            metadata=metadata
        )
    
    def log_agent_response(self, source_agent: str, task_id: str, context_id: str,
                          response_data: Dict[str, Any], duration_ms: float,
                          success: bool = True, metadata: Optional[Dict[str, Any]] = None):
        """记录Agent响应"""
        self.monitor.log_response(
            source_agent=source_agent,
            target_agent=self.agent_name,
            task_id=task_id,
            context_id=context_id,
            response_data=response_data,
            duration_ms=duration_ms,
            success=success,
            metadata=metadata
        )
    
    def log_agent_error(self, target_agent: Optional[str], task_id: str, context_id: str,
                       error_message: str, metadata: Optional[Dict[str, Any]] = None):
        """记录Agent错误"""
        self.monitor.log_error(
            source_agent=self.agent_name,
            target_agent=target_agent,
            task_id=task_id,
            context_id=context_id,
            error_message=error_message,
            metadata=metadata
        )

def monitor_agent_method(agent_name: str, method_type: str = "general"):
    """Agent方法监控装饰器"""
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 生成任务ID
            task_id = f"{agent_name}_{method_type}_{int(time.time() * 1000)}"
            context_id = kwargs.get('context_id', f"ctx_{int(time.time())}")
            
            monitor = get_communication_monitor()
            start_time = time.time()
            
            # 记录任务开始
            monitor.log_task_start(
                task_id=task_id,
                context_id=context_id,
                task_type=method_type,
                involved_agents=[agent_name],
                total_steps=1
            )
            
            try:
                # 执行原方法
                result = await func(*args, **kwargs)
                
                # 计算执行时间
                duration_ms = (time.time() - start_time) * 1000
                
                # 质量评估（如果结果是文本内容）
                quality_score = None
                if isinstance(result, dict) and 'content' in result:
                    try:
                        assessor = get_quality_assessor()
                        quality_metrics = assessor.assess_content(
                            content=str(result['content']),
                            content_type=method_type
                        )
                        quality_score = quality_metrics.overall_score
                    except Exception as e:
                        logger.error(f"质量评估失败: {e}")
                
                # 记录任务完成
                monitor.log_task_complete(
                    task_id=task_id,
                    success=True,
                    quality_score=quality_score
                )
                
                # 记录响应
                monitor.log_response(
                    source_agent=agent_name,
                    target_agent="client",
                    task_id=task_id,
                    context_id=context_id,
                    response_data={"status": "success", "type": method_type},
                    duration_ms=duration_ms,
                    success=True
                )
                
                return result
                
            except Exception as e:
                # 计算执行时间
                duration_ms = (time.time() - start_time) * 1000
                
                # 记录错误
                monitor.log_error(
                    source_agent=agent_name,
                    target_agent="client",
                    task_id=task_id,
                    context_id=context_id,
                    error_message=str(e)
                )
                
                # 记录任务失败
                monitor.log_task_complete(
                    task_id=task_id,
                    success=False
                )
                
                raise
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            # 同步方法的监控逻辑
            task_id = f"{agent_name}_{method_type}_{int(time.time() * 1000)}"
            context_id = kwargs.get('context_id', f"ctx_{int(time.time())}")
            
            monitor = get_communication_monitor()
            start_time = time.time()
            
            # 记录任务开始
            monitor.log_task_start(
                task_id=task_id,
                context_id=context_id,
                task_type=method_type,
                involved_agents=[agent_name],
                total_steps=1
            )
            
            try:
                # 执行原方法
                result = func(*args, **kwargs)
                
                # 计算执行时间
                duration_ms = (time.time() - start_time) * 1000
                
                # 记录任务完成
                monitor.log_task_complete(
                    task_id=task_id,
                    success=True
                )
                
                # 记录响应
                monitor.log_response(
                    source_agent=agent_name,
                    target_agent="client",
                    task_id=task_id,
                    context_id=context_id,
                    response_data={"status": "success", "type": method_type},
                    duration_ms=duration_ms,
                    success=True
                )
                
                return result
                
            except Exception as e:
                # 记录错误
                monitor.log_error(
                    source_agent=agent_name,
                    target_agent="client",
                    task_id=task_id,
                    context_id=context_id,
                    error_message=str(e)
                )
                
                # 记录任务失败
                monitor.log_task_complete(
                    task_id=task_id,
                    success=False
                )
                
                raise
        
        # 根据函数类型选择包装器
        if inspect.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

def monitor_a2a_executor(agent_name: str):
    """A2A执行器监控装饰器"""
    def decorator(cls):
        original_execute = cls.execute
        original_cancel = cls.cancel
        
        @functools.wraps(original_execute)
        async def monitored_execute(self, context, event_queue):
            task_id = context.task_id or f"task_{int(time.time() * 1000)}"
            context_id = context.context_id or f"ctx_{int(time.time())}"
            
            monitor = get_communication_monitor()
            start_time = time.time()
            
            # 记录任务开始
            monitor.log_task_start(
                task_id=task_id,
                context_id=context_id,
                task_type="a2a_execution",
                involved_agents=[agent_name],
                total_steps=1
            )
            
            try:
                # 执行原方法
                result = await original_execute(self, context, event_queue)
                
                # 计算执行时间
                duration_ms = (time.time() - start_time) * 1000
                
                # 记录任务完成
                monitor.log_task_complete(
                    task_id=task_id,
                    success=True
                )
                
                return result
                
            except Exception as e:
                # 记录错误
                monitor.log_error(
                    source_agent=agent_name,
                    target_agent=None,
                    task_id=task_id,
                    context_id=context_id,
                    error_message=str(e)
                )
                
                # 记录任务失败
                monitor.log_task_complete(
                    task_id=task_id,
                    success=False
                )
                
                raise
        
        @functools.wraps(original_cancel)
        async def monitored_cancel(self, context, event_queue):
            task_id = context.task_id or f"task_{int(time.time() * 1000)}"
            context_id = context.context_id or f"ctx_{int(time.time())}"
            
            monitor = get_communication_monitor()
            
            try:
                result = await original_cancel(self, context, event_queue)
                
                # 记录任务取消
                monitor.log_task_complete(
                    task_id=task_id,
                    success=False,
                    metadata={"cancelled": True}
                )
                
                return result
                
            except Exception as e:
                monitor.log_error(
                    source_agent=agent_name,
                    target_agent=None,
                    task_id=task_id,
                    context_id=context_id,
                    error_message=f"取消任务时出错: {str(e)}"
                )
                raise
        
        # 替换方法
        cls.execute = monitored_execute
        cls.cancel = monitored_cancel
        
        return cls
    
    return decorator

class MonitoredAgentClient:
    """带监控的Agent客户端"""
    
    def __init__(self, agent_name: str, base_url: str):
        self.agent_name = agent_name
        self.base_url = base_url
        self.monitor = get_communication_monitor()
        
    async def send_request(self, endpoint: str, data: Dict[str, Any], 
                          task_id: Optional[str] = None, 
                          context_id: Optional[str] = None) -> Dict[str, Any]:
        """发送监控的请求"""
        import aiohttp
        
        task_id = task_id or f"req_{int(time.time() * 1000)}"
        context_id = context_id or f"ctx_{int(time.time())}"
        
        # 记录请求
        self.monitor.log_request(
            source_agent="client",
            target_agent=self.agent_name,
            task_id=task_id,
            context_id=context_id,
            request_data=data
        )
        
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/{endpoint}",
                    json=data,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    
                    duration_ms = (time.time() - start_time) * 1000
                    response_data = await response.json()
                    success = response.status == 200
                    
                    # 记录响应
                    self.monitor.log_response(
                        source_agent="client",
                        target_agent=self.agent_name,
                        task_id=task_id,
                        context_id=context_id,
                        response_data=response_data,
                        duration_ms=duration_ms,
                        success=success
                    )
                    
                    if not success:
                        self.monitor.log_error(
                            source_agent="client",
                            target_agent=self.agent_name,
                            task_id=task_id,
                            context_id=context_id,
                            error_message=f"HTTP {response.status}: {response_data}"
                        )
                    
                    return response_data
        
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            
            # 记录错误
            self.monitor.log_error(
                source_agent="client",
                target_agent=self.agent_name,
                task_id=task_id,
                context_id=context_id,
                error_message=str(e)
            )
            
            raise

def create_monitoring_report(output_file: str = None) -> Dict[str, Any]:
    """创建监控报告"""
    monitor = get_communication_monitor()
    
    # 生成报告
    report = {
        "timestamp": datetime.now().isoformat(),
        "system_stats": monitor.get_system_stats(),
        "agent_metrics": {
            name: {
                "name": metrics.agent_name,
                "status": metrics.status,
                "total_requests": metrics.total_requests,
                "successful_requests": metrics.successful_requests,
                "failed_requests": metrics.failed_requests,
                "error_count": metrics.error_count,
                "avg_response_time": metrics.avg_response_time,
                "last_activity": metrics.last_activity.isoformat() if metrics.last_activity else None
            }
            for name, metrics in monitor.get_agent_metrics().items()
        },
        "recent_events": [
            {
                "timestamp": event.timestamp.isoformat(),
                "event_type": event.event_type,
                "source_agent": event.source_agent,
                "target_agent": event.target_agent,
                "task_id": event.task_id,
                "status": event.status,
                "duration_ms": event.duration_ms,
                "error_message": event.error_message
            }
            for event in monitor.get_recent_events(50)
        ]
    }
    
    # 保存报告
    if output_file:
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                import json
                json.dump(report, f, indent=2, ensure_ascii=False)
            logger.info(f"📊 监控报告已保存到: {output_file}")
        except Exception as e:
            logger.error(f"保存监控报告失败: {e}")
    
    return report