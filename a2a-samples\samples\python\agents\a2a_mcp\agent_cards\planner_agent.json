{"name": "Langraph Planner Agent", "description": "Helps breakdown a request in to actionable tasks", "url": "http://localhost:10102/", "provider": null, "version": "1.0.0", "documentationUrl": null, "authentication": {"credentials": null, "schemes": ["public"]}, "capabilities": {"streaming": "True", "pushNotifications": "True", "stateTransitionHistory": "False"}, "defaultInputModes": ["text", "text/plain"], "defaultOutputModes": ["text", "text/plain"], "skills": [{"id": "planner", "name": "Task Planner", "description": "Helps breakdown a request in to actionable tasks", "tags": ["planner"], "examples": ["Plan my business trip from San Francisco to London, submit an expense report"], "inputModes": null, "outputModes": null}]}