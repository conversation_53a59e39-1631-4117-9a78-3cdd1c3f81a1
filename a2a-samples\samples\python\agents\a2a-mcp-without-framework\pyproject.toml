[project]
name = "no-llm-framework"
version = "0.1.0"
description = "Use A2A without any agent framework"
readme = "README.md"
authors = [{ name = "prem", email = "<EMAIL>" }]
requires-python = ">=3.13"
dependencies = [
    "a2a-sdk>=0.2.8",
    "asyncclick>=8.1.8",
    "colorama>=0.4.6",
    "fastmcp>=2.3.4",
    "google-genai",
    "jinja2>=3.1.6",
    "rich>=14.0.0",
]
[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project.scripts]
a2a-server = "no_llm_framework.server.__main__:main"
a2a-client = "no_llm_framework.client.__main__:main"
