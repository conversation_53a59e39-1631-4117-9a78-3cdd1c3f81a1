#!/usr/bin/env python3
"""
检查项目设置状态的脚本
"""
import os
import sys
from pathlib import Path
import importlib.util


def check_file_exists(file_path: str, description: str) -> bool:
    """检查文件是否存在"""
    if Path(file_path).exists():
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (不存在)")
        return False


def check_directory_structure():
    """检查项目目录结构"""
    print("📁 检查项目目录结构:")
    
    required_dirs = [
        ("plot_agent", "Plot Agent目录"),
        ("character_agent", "Character Agent目录"),
        ("content_agent", "Content Agent目录"),
        ("host_agent", "Host Agent目录"),
        ("templates", "写作模板目录"),
        ("templates/story_structures", "故事结构模板"),
        ("templates/character_archetypes", "角色原型模板"),
        ("templates/writing_styles", "写作风格模板"),
        ("tests", "测试用例目录"),
        ("scripts", "脚本目录"),
        ("data", "数据存储目录"),
        ("data/characters", "角色档案目录"),
        ("data/projects", "项目存储目录")
    ]
    
    all_exist = True
    for dir_path, description in required_dirs:
        if Path(dir_path).exists():
            print(f"✅ {description}: {dir_path}/")
        else:
            print(f"❌ {description}: {dir_path}/ (不存在)")
            all_exist = False
    
    return all_exist


def check_config_files():
    """检查配置文件"""
    print("\n⚙️  检查配置文件:")
    
    config_files = [
        (".env", "环境变量配置"),
        (".env.example", "环境变量模板"),
        ("requirements.txt", "Python依赖列表")
    ]
    
    all_exist = True
    for file_path, description in config_files:
        if not check_file_exists(file_path, description):
            all_exist = False
    
    return all_exist


def check_template_files():
    """检查模板文件"""
    print("\n📄 检查模板文件:")
    
    template_files = [
        ("templates/story_structures/three_act.yaml", "三幕式结构模板"),
        ("templates/story_structures/hero_journey.yaml", "英雄之旅模板"),
        ("templates/character_archetypes/protagonist_types.yaml", "主角原型模板"),
        ("templates/writing_styles/modern_urban.yaml", "现代都市风格模板")
    ]
    
    all_exist = True
    for file_path, description in template_files:
        if not check_file_exists(file_path, description):
            all_exist = False
    
    return all_exist


def check_script_files():
    """检查脚本文件"""
    print("\n🔧 检查脚本文件:")
    
    script_files = [
        ("scripts/start_all.py", "Python启动脚本"),
        ("scripts/start_all.bat", "Windows启动脚本"),
        ("scripts/start_all.sh", "Linux启动脚本"),
        ("scripts/test_writing_system.py", "系统测试脚本"),
        ("scripts/install_dependencies.py", "依赖安装脚本")
    ]
    
    all_exist = True
    for file_path, description in script_files:
        if not check_file_exists(file_path, description):
            all_exist = False
    
    return all_exist


def check_env_variables():
    """检查环境变量配置"""
    print("\n🔑 检查环境变量配置:")
    
    if not Path(".env").exists():
        print("❌ .env 文件不存在")
        return False
    
    # 读取.env文件
    try:
        with open(".env", "r", encoding="utf-8") as f:
            env_content = f.read()
        
        required_vars = [
            "OPENAI_API_KEY",
            "OPENAI_BASE_URL", 
            "OPENAI_MODEL",
            "AI_PROVIDER",
            "PLOT_AGENT_PORT",
            "CHARACTER_AGENT_PORT",
            "CONTENT_AGENT_PORT",
            "HOST_AGENT_PORT"
        ]
        
        all_configured = True
        for var in required_vars:
            if var in env_content and not env_content.split(f"{var}=")[1].split("\n")[0].strip() in ["", "your_api_key_here", "your_project_id"]:
                print(f"✅ {var}: 已配置")
            else:
                print(f"❌ {var}: 未配置或使用默认值")
                all_configured = False
        
        return all_configured
        
    except Exception as e:
        print(f"❌ 读取.env文件失败: {e}")
        return False


def check_python_packages():
    """检查Python包安装状态"""
    print("\n📦 检查Python包安装状态:")
    
    key_packages = [
        ("a2a", "A2A SDK"),
        ("google.adk", "Google ADK"),
        ("langgraph", "LangGraph"),
        ("httpx", "HTTP客户端"),
        ("pydantic", "数据验证"),
        ("python_dotenv", "环境变量加载"),
        ("yaml", "YAML解析"),
        ("openai", "OpenAI API")
    ]
    
    all_installed = True
    for package_name, description in key_packages:
        try:
            importlib.import_module(package_name.replace("-", "_"))
            print(f"✅ {description}: {package_name}")
        except ImportError:
            print(f"❌ {description}: {package_name} (未安装)")
            all_installed = False
    
    return all_installed


def main():
    """主函数"""
    print("🔍 A2A网文写作助手 - 项目设置检查")
    print("=" * 50)
    
    checks = [
        ("目录结构", check_directory_structure),
        ("配置文件", check_config_files),
        ("模板文件", check_template_files),
        ("脚本文件", check_script_files),
        ("环境变量", check_env_variables),
        ("Python包", check_python_packages)
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name}检查失败: {e}")
            results.append((check_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 检查结果总结:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {check_name}: {status}")
    
    print(f"\n🎯 总体状态: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("\n🎉 项目设置完成！可以开始开发了。")
        print("\n📋 下一步建议:")
        print("   1. 运行: python scripts/start_all.py")
        print("   2. 测试: python scripts/test_writing_system.py")
        return 0
    else:
        print(f"\n⚠️  还有 {total - passed} 项需要完善，请根据上述检查结果进行修复。")
        return 1


if __name__ == "__main__":
    sys.exit(main())