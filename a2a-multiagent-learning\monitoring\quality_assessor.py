#!/usr/bin/env python3
"""
写作质量评估和反馈系统
评估Agent生成内容的质量并提供改进建议
"""

import re
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import jieba
import jieba.posseg as pseg

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class QualityMetrics:
    """质量评估指标"""
    overall_score: float  # 总体评分 (0-100)
    content_length: int   # 内容长度
    readability_score: float  # 可读性评分
    creativity_score: float   # 创意性评分
    coherence_score: float    # 连贯性评分
    grammar_score: float      # 语法评分
    style_consistency: float  # 风格一致性
    character_development: float  # 角色发展度
    plot_logic: float        # 情节逻辑性
    dialogue_quality: float  # 对话质量
    description_quality: float  # 描写质量
    
    # 详细分析
    strengths: List[str]     # 优点
    weaknesses: List[str]    # 缺点
    suggestions: List[str]   # 改进建议
    
    # 元数据
    assessment_time: datetime
    assessor_version: str = "1.0"

class QualityAssessor:
    """写作质量评估器"""
    
    def __init__(self):
        # 初始化jieba分词
        jieba.initialize()
        
        # 质量评估规则
        self.grammar_patterns = self._load_grammar_patterns()
        self.style_keywords = self._load_style_keywords()
        self.creativity_indicators = self._load_creativity_indicators()
        
        logger.info("📊 写作质量评估器已初始化")
    
    def assess_content(self, content: str, content_type: str = "general", 
                      context: Optional[Dict[str, Any]] = None) -> QualityMetrics:
        """评估内容质量"""
        try:
            # 基础指标
            content_length = len(content)
            
            # 各项评分
            readability = self._assess_readability(content)
            creativity = self._assess_creativity(content, content_type)
            coherence = self._assess_coherence(content)
            grammar = self._assess_grammar(content)
            style_consistency = self._assess_style_consistency(content, content_type)
            
            # 内容特定评分
            character_dev = self._assess_character_development(content, content_type)
            plot_logic = self._assess_plot_logic(content, content_type)
            dialogue_quality = self._assess_dialogue_quality(content)
            description_quality = self._assess_description_quality(content)
            
            # 计算总体评分
            overall_score = self._calculate_overall_score({
                "readability": readability,
                "creativity": creativity,
                "coherence": coherence,
                "grammar": grammar,
                "style_consistency": style_consistency,
                "character_development": character_dev,
                "plot_logic": plot_logic,
                "dialogue_quality": dialogue_quality,
                "description_quality": description_quality
            })
            
            # 生成分析结果
            strengths, weaknesses, suggestions = self._generate_feedback(
                content, content_type, {
                    "readability": readability,
                    "creativity": creativity,
                    "coherence": coherence,
                    "grammar": grammar,
                    "style_consistency": style_consistency,
                    "character_development": character_dev,
                    "plot_logic": plot_logic,
                    "dialogue_quality": dialogue_quality,
                    "description_quality": description_quality
                }
            )
            
            return QualityMetrics(
                overall_score=overall_score,
                content_length=content_length,
                readability_score=readability,
                creativity_score=creativity,
                coherence_score=coherence,
                grammar_score=grammar,
                style_consistency=style_consistency,
                character_development=character_dev,
                plot_logic=plot_logic,
                dialogue_quality=dialogue_quality,
                description_quality=description_quality,
                strengths=strengths,
                weaknesses=weaknesses,
                suggestions=suggestions,
                assessment_time=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"质量评估失败: {e}")
            # 返回默认评估结果
            return QualityMetrics(
                overall_score=50.0,
                content_length=len(content),
                readability_score=50.0,
                creativity_score=50.0,
                coherence_score=50.0,
                grammar_score=50.0,
                style_consistency=50.0,
                character_development=50.0,
                plot_logic=50.0,
                dialogue_quality=50.0,
                description_quality=50.0,
                strengths=["内容已生成"],
                weaknesses=["评估过程中出现错误"],
                suggestions=["建议人工检查内容质量"],
                assessment_time=datetime.now()
            )
    
    def _assess_readability(self, content: str) -> float:
        """评估可读性"""
        try:
            # 基于句子长度和复杂度
            sentences = re.split(r'[。！？]', content)
            sentences = [s.strip() for s in sentences if s.strip()]
            
            if not sentences:
                return 50.0
            
            # 平均句子长度
            avg_sentence_length = sum(len(s) for s in sentences) / len(sentences)
            
            # 理想句子长度为15-25字
            length_score = 100 - abs(avg_sentence_length - 20) * 2
            length_score = max(0, min(100, length_score))
            
            # 标点符号使用
            punctuation_count = len(re.findall(r'[，。！？；：""''（）]', content))
            punctuation_ratio = punctuation_count / len(content) if content else 0
            punctuation_score = min(100, punctuation_ratio * 1000)  # 适当的标点密度
            
            return (length_score * 0.7 + punctuation_score * 0.3)
            
        except Exception as e:
            logger.error(f"可读性评估失败: {e}")
            return 50.0
    
    def _assess_creativity(self, content: str, content_type: str) -> float:
        """评估创意性"""
        try:
            score = 50.0
            
            # 词汇丰富度
            words = list(jieba.cut(content))
            unique_words = set(words)
            if words:
                vocabulary_richness = len(unique_words) / len(words)
                score += vocabulary_richness * 30
            
            # 创意词汇使用
            creative_words = 0
            for indicator in self.creativity_indicators:
                if indicator in content:
                    creative_words += 1
            
            score += min(20, creative_words * 2)
            
            # 比喻和修辞手法
            metaphor_patterns = [r'如同', r'仿佛', r'好像', r'犹如', r'宛如']
            metaphor_count = sum(len(re.findall(pattern, content)) for pattern in metaphor_patterns)
            score += min(15, metaphor_count * 3)
            
            return min(100, score)
            
        except Exception as e:
            logger.error(f"创意性评估失败: {e}")
            return 50.0
    
    def _assess_coherence(self, content: str) -> float:
        """评估连贯性"""
        try:
            # 基于连接词和逻辑关系
            coherence_words = ['然后', '接着', '随后', '于是', '因此', '所以', '但是', '然而', '不过', '同时', '此时', '这时']
            
            coherence_count = sum(content.count(word) for word in coherence_words)
            sentences = len(re.split(r'[。！？]', content))
            
            if sentences > 0:
                coherence_ratio = coherence_count / sentences
                score = min(100, coherence_ratio * 200 + 50)
            else:
                score = 50.0
            
            return score
            
        except Exception as e:
            logger.error(f"连贯性评估失败: {e}")
            return 50.0
    
    def _assess_grammar(self, content: str) -> float:
        """评估语法质量"""
        try:
            score = 90.0  # 基础分数
            
            # 检查常见语法错误
            for pattern, penalty in self.grammar_patterns.items():
                matches = len(re.findall(pattern, content))
                score -= matches * penalty
            
            # 检查标点符号使用
            # 连续标点
            consecutive_punct = len(re.findall(r'[，。！？]{2,}', content))
            score -= consecutive_punct * 5
            
            # 引号配对
            quote_count = content.count('"') + content.count('"')
            if quote_count % 2 != 0:
                score -= 10
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"语法评估失败: {e}")
            return 70.0
    
    def _assess_style_consistency(self, content: str, content_type: str) -> float:
        """评估风格一致性"""
        try:
            score = 70.0
            
            # 根据内容类型检查风格关键词
            if content_type in self.style_keywords:
                expected_keywords = self.style_keywords[content_type]
                found_keywords = sum(1 for keyword in expected_keywords if keyword in content)
                keyword_ratio = found_keywords / len(expected_keywords) if expected_keywords else 0
                score += keyword_ratio * 30
            
            # 检查语言风格一致性（现代/古代）
            modern_indicators = ['电脑', '手机', '汽车', '网络', '公司', '办公室']
            ancient_indicators = ['宫廷', '皇上', '娘娘', '公子', '小姐', '府邸']
            
            modern_count = sum(1 for word in modern_indicators if word in content)
            ancient_count = sum(1 for word in ancient_indicators if word in content)
            
            # 风格混乱惩罚
            if modern_count > 0 and ancient_count > 0:
                score -= 20
            
            return min(100, score)
            
        except Exception as e:
            logger.error(f"风格一致性评估失败: {e}")
            return 70.0
    
    def _assess_character_development(self, content: str, content_type: str) -> float:
        """评估角色发展度"""
        try:
            if content_type not in ['character', 'story', 'chapter']:
                return 50.0
            
            score = 40.0
            
            # 角色描述要素
            character_elements = [
                r'[姓名|名字|叫做]',
                r'[年龄|岁]',
                r'[性格|脾气|个性]',
                r'[外貌|长相|容貌]',
                r'[职业|工作|身份]',
                r'[背景|出身|经历]'
            ]
            
            for element in character_elements:
                if re.search(element, content):
                    score += 10
            
            return min(100, score)
            
        except Exception as e:
            logger.error(f"角色发展度评估失败: {e}")
            return 50.0
    
    def _assess_plot_logic(self, content: str, content_type: str) -> float:
        """评估情节逻辑性"""
        try:
            if content_type not in ['plot', 'story', 'chapter']:
                return 50.0
            
            score = 50.0
            
            # 情节要素
            plot_elements = [
                r'[开始|起初|首先]',
                r'[然后|接着|随后]',
                r'[冲突|矛盾|问题]',
                r'[解决|处理|应对]',
                r'[结果|结局|最终]'
            ]
            
            for element in plot_elements:
                if re.search(element, content):
                    score += 10
            
            return min(100, score)
            
        except Exception as e:
            logger.error(f"情节逻辑性评估失败: {e}")
            return 50.0
    
    def _assess_dialogue_quality(self, content: str) -> float:
        """评估对话质量"""
        try:
            # 检测对话
            dialogue_patterns = [r'"[^"]*"', r'"[^"]*"', r'「[^」]*」']
            dialogue_count = sum(len(re.findall(pattern, content)) for pattern in dialogue_patterns)
            
            if dialogue_count == 0:
                return 50.0  # 无对话内容
            
            score = 60.0
            
            # 对话标签
            dialogue_tags = ['说道', '回答', '问道', '喊道', '轻声说', '大声说']
            tag_count = sum(content.count(tag) for tag in dialogue_tags)
            
            if tag_count > 0:
                score += min(20, tag_count * 5)
            
            # 对话自然度（基于长度和复杂度）
            dialogues = []
            for pattern in dialogue_patterns:
                dialogues.extend(re.findall(pattern, content))
            
            if dialogues:
                avg_dialogue_length = sum(len(d) for d in dialogues) / len(dialogues)
                # 理想对话长度为10-30字
                if 10 <= avg_dialogue_length <= 30:
                    score += 20
                elif 5 <= avg_dialogue_length <= 50:
                    score += 10
            
            return min(100, score)
            
        except Exception as e:
            logger.error(f"对话质量评估失败: {e}")
            return 50.0
    
    def _assess_description_quality(self, content: str) -> float:
        """评估描写质量"""
        try:
            score = 50.0
            
            # 感官描写
            sensory_words = {
                '视觉': ['看到', '望见', '瞥见', '注视', '凝视', '色彩', '光线', '阴影'],
                '听觉': ['听到', '声音', '响声', '音乐', '歌声', '噪音', '寂静'],
                '触觉': ['触摸', '感受', '温暖', '冰冷', '柔软', '粗糙', '光滑'],
                '嗅觉': ['闻到', '香味', '气味', '芳香', '臭味'],
                '味觉': ['品尝', '甜味', '苦味', '酸味', '辣味']
            }
            
            sensory_count = 0
            for category, words in sensory_words.items():
                for word in words:
                    if word in content:
                        sensory_count += 1
                        break  # 每个类别最多计算一次
            
            score += sensory_count * 10
            
            # 环境描写
            environment_words = ['环境', '场景', '地点', '房间', '街道', '花园', '山峰', '河流']
            env_count = sum(1 for word in environment_words if word in content)
            score += min(20, env_count * 5)
            
            return min(100, score)
            
        except Exception as e:
            logger.error(f"描写质量评估失败: {e}")
            return 50.0
    
    def _calculate_overall_score(self, scores: Dict[str, float]) -> float:
        """计算总体评分"""
        # 权重配置
        weights = {
            "readability": 0.15,
            "creativity": 0.15,
            "coherence": 0.15,
            "grammar": 0.15,
            "style_consistency": 0.10,
            "character_development": 0.10,
            "plot_logic": 0.10,
            "dialogue_quality": 0.05,
            "description_quality": 0.05
        }
        
        weighted_score = sum(scores[key] * weights[key] for key in weights if key in scores)
        return min(100, max(0, weighted_score))
    
    def _generate_feedback(self, content: str, content_type: str, 
                          scores: Dict[str, float]) -> Tuple[List[str], List[str], List[str]]:
        """生成反馈意见"""
        strengths = []
        weaknesses = []
        suggestions = []
        
        # 基于评分生成反馈
        for metric, score in scores.items():
            if score >= 80:
                strengths.append(self._get_strength_message(metric, score))
            elif score < 60:
                weaknesses.append(self._get_weakness_message(metric, score))
                suggestions.append(self._get_suggestion_message(metric, score))
        
        # 内容特定反馈
        if len(content) < 100:
            weaknesses.append("内容长度较短")
            suggestions.append("建议增加更多细节描述")
        elif len(content) > 2000:
            suggestions.append("内容较长，建议检查是否有冗余部分")
        
        # 确保至少有一些反馈
        if not strengths:
            strengths.append("内容已成功生成")
        if not suggestions:
            suggestions.append("继续保持当前的写作水平")
        
        return strengths, weaknesses, suggestions
    
    def _get_strength_message(self, metric: str, score: float) -> str:
        """获取优点描述"""
        messages = {
            "readability": "文本可读性良好，句子长度适中",
            "creativity": "内容富有创意，词汇使用丰富",
            "coherence": "逻辑连贯，结构清晰",
            "grammar": "语法规范，表达准确",
            "style_consistency": "风格统一，符合类型特点",
            "character_development": "角色塑造生动，特点鲜明",
            "plot_logic": "情节发展合理，逻辑清晰",
            "dialogue_quality": "对话自然流畅，符合角色特点",
            "description_quality": "描写生动细腻，画面感强"
        }
        return messages.get(metric, f"{metric}表现优秀")
    
    def _get_weakness_message(self, metric: str, score: float) -> str:
        """获取缺点描述"""
        messages = {
            "readability": "句子结构需要优化，可读性有待提高",
            "creativity": "创意表达不足，词汇使用较为单一",
            "coherence": "逻辑连接不够紧密，结构需要改进",
            "grammar": "存在语法错误，需要仔细检查",
            "style_consistency": "风格不够统一，需要保持一致性",
            "character_development": "角色塑造不够深入，特点不够鲜明",
            "plot_logic": "情节发展逻辑性不强，需要完善",
            "dialogue_quality": "对话不够自然，需要改进表达方式",
            "description_quality": "描写不够生动，缺乏细节"
        }
        return messages.get(metric, f"{metric}需要改进")
    
    def _get_suggestion_message(self, metric: str, score: float) -> str:
        """获取改进建议"""
        suggestions = {
            "readability": "建议调整句子长度，增加适当的标点符号",
            "creativity": "建议使用更多样化的词汇和修辞手法",
            "coherence": "建议增加逻辑连接词，完善段落间的过渡",
            "grammar": "建议仔细检查语法，确保表达准确",
            "style_consistency": "建议保持统一的语言风格和表达方式",
            "character_development": "建议增加角色的背景描述和性格特点",
            "plot_logic": "建议完善情节的因果关系和发展逻辑",
            "dialogue_quality": "建议让对话更贴近角色性格，增加对话标签",
            "description_quality": "建议增加感官描写，丰富场景细节"
        }
        return suggestions.get(metric, f"建议改进{metric}相关内容")
    
    def _load_grammar_patterns(self) -> Dict[str, float]:
        """加载语法检查模式"""
        return {
            r'的的': 5.0,  # 重复的"的"
            r'了了': 5.0,  # 重复的"了"
            r'在在': 5.0,  # 重复词汇
            r'[，。！？]{3,}': 3.0,  # 过多标点
            r'[\s]{2,}': 2.0,  # 多余空格
        }
    
    def _load_style_keywords(self) -> Dict[str, List[str]]:
        """加载风格关键词"""
        return {
            "modern": ["现代", "都市", "公司", "办公室", "电脑", "手机", "汽车", "咖啡厅"],
            "ancient": ["古代", "宫廷", "皇上", "娘娘", "公子", "小姐", "府邸", "轿子"],
            "fantasy": ["玄幻", "修仙", "法术", "灵气", "境界", "丹药", "宗门", "仙人"],
            "romance": ["爱情", "恋人", "心动", "温柔", "浪漫", "甜蜜", "思念", "深情"]
        }
    
    def _load_creativity_indicators(self) -> List[str]:
        """加载创意指示词"""
        return [
            "独特", "新颖", "创新", "别致", "奇妙", "神奇", "惊艳", "绝美",
            "如诗如画", "美轮美奂", "令人惊叹", "别具一格", "匠心独运"
        ]

# 全局质量评估器实例
_global_assessor: Optional[QualityAssessor] = None

def get_quality_assessor() -> QualityAssessor:
    """获取全局质量评估器实例"""
    global _global_assessor
    if _global_assessor is None:
        _global_assessor = QualityAssessor()
    return _global_assessor