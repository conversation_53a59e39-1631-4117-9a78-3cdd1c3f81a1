#!/usr/bin/env python3
"""
启动Host Agent连接管理器的便捷脚本
"""

import asyncio
import sys
import signal
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class HostAgentManager:
    """Host Agent管理器"""
    
    def __init__(self):
        self.connection_manager = None
        self.running = False
    
    async def start(self):
        """启动Host Agent管理器"""
        print("🚀 启动Host Agent连接管理器...")
        print("=" * 50)
        
        try:
            from host_agent.remote_agent_connection import get_connection_manager
            
            # 获取连接管理器
            self.connection_manager = await get_connection_manager()
            self.running = True
            
            print("✅ Host Agent连接管理器启动成功")
            print("\n📊 Agent连接状态:")
            
            # 显示初始状态
            await self.show_status()
            
            # 启动状态监控循环
            await self.monitor_loop()
            
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            import traceback
            traceback.print_exc()
    
    async def show_status(self):
        """显示Agent状态"""
        if not self.connection_manager:
            return
        
        agent_status = await self.connection_manager.get_all_agent_status()
        
        for agent_id, status in agent_status.items():
            status_icon = "✅" if status['status'] == 'connected' else "❌"
            print(f"{status_icon} {agent_id}: {status['name']} ({status['status']})")
            if status['status'] == 'connected':
                print(f"   📍 URL: {status['url']}")
                print(f"   🛠️ 技能: {status['skills_count']}个")
            elif status['last_error']:
                print(f"   ⚠️ 错误: {status['last_error']}")
    
    async def monitor_loop(self):
        """监控循环"""
        print("\n🔄 开始监控Agent连接状态...")
        print("💡 按 Ctrl+C 停止监控")
        print("-" * 50)
        
        last_status = {}
        
        while self.running:
            try:
                await asyncio.sleep(10)  # 每10秒检查一次
                
                if not self.running:
                    break
                
                current_status = await self.connection_manager.get_all_agent_status()
                
                # 检查状态变化
                for agent_id, status in current_status.items():
                    last_agent_status = last_status.get(agent_id, {}).get('status', 'unknown')
                    current_agent_status = status['status']
                    
                    if last_agent_status != current_agent_status:
                        timestamp = asyncio.get_event_loop().time()
                        if current_agent_status == 'connected':
                            print(f"✅ [{timestamp:.0f}] {agent_id} 已连接")
                        elif current_agent_status == 'error':
                            print(f"❌ [{timestamp:.0f}] {agent_id} 连接失败")
                        elif current_agent_status == 'disconnected':
                            print(f"🔌 [{timestamp:.0f}] {agent_id} 已断开")
                
                last_status = current_status
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"⚠️ 监控异常: {e}")
    
    async def stop(self):
        """停止Host Agent管理器"""
        print("\n🔄 停止Host Agent连接管理器...")
        
        self.running = False
        
        if self.connection_manager:
            from host_agent.remote_agent_connection import shutdown_connection_manager
            await shutdown_connection_manager()
        
        print("✅ Host Agent连接管理器已停止")

# 全局管理器实例
manager = HostAgentManager()

def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n📡 收到信号 {signum}，准备停止...")
    asyncio.create_task(manager.stop())

async def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        await manager.start()
    except KeyboardInterrupt:
        print("\n👋 用户中断，正在停止...")
    except Exception as e:
        print(f"❌ 运行异常: {e}")
    finally:
        await manager.stop()

if __name__ == "__main__":
    print("🏠 Host Agent连接管理器")
    print("📝 管理与写作相关Agent的连接")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)