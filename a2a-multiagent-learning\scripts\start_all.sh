#!/bin/bash

echo "🏠 A2A多Agent写作助手启动器"
echo "================================"

# 切换到项目根目录
cd "$(dirname "$0")/.."

echo "📋 检查Python环境..."
python3 --version
if [ $? -ne 0 ]; then
    echo "❌ Python3未安装或不在PATH中"
    exit 1
fi

echo "📋 检查依赖包..."
python3 -c "import a2a; print('✅ A2A SDK已安装')" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ A2A SDK未安装，请运行: pip install a2a-sdk"
    exit 1
fi

echo "🚀 启动所有Agent..."
python3 scripts/start_all_agents.py