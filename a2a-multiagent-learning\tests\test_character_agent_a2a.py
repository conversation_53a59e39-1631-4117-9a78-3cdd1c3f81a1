#!/usr/bin/env python3
"""
测试Character Agent的A2A服务器功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_character_agent_a2a():
    """测试Character Agent A2A服务器功能"""
    print("👤 测试Character Agent A2A服务器功能...")
    print("=" * 60)
    
    try:
        # 测试导入
        print("📦 测试模块导入...")
        from character_agent.agent_executor import CharacterAgentExecutor
        from character_agent.agent import CharacterAgent
        print("✅ 模块导入成功")
        
        # 测试Agent实例化
        print("\n👤 测试Agent实例化...")
        agent = CharacterAgent()
        print(f"✅ Agent名称: {agent.name}")
        print(f"✅ Agent描述: {agent.description}")
        
        # 测试Executor实例化
        print("\n🔧 测试Executor实例化...")
        executor = CharacterAgentExecutor()
        print(f"✅ Executor创建成功")
        print(f"✅ 内部Agent: {executor.agent.name}")
        
        # 测试技能映射
        print("\n🎯 测试技能映射...")
        skills = list(agent.skills_map.keys())
        print(f"✅ 可用技能 ({len(skills)}个):")
        for skill in skills:
            print(f"   - {skill}")
        
        # 测试流式响应
        print("\n" + "="*60)
        print("🎯 测试流式响应功能")
        print("="*60)
        
        test_query = "创建一个现代都市小说的女主角"
        print(f"📝 测试查询: {test_query}")
        print("\n📄 响应:")
        print("-" * 40)
        
        result_parts = []
        async for event in agent.stream(test_query):
            if event['content']:
                print(event['content'], end='', flush=True)
                result_parts.append(event['content'])
            if event['done']:
                break
        
        result = ''.join(result_parts)
        print(f"\n\n✅ 流式响应测试完成 ({len(result)} 字符)")
        
        print("\n🎉 Character Agent A2A服务器功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_character_agent_a2a())
    if not success:
        sys.exit(1)