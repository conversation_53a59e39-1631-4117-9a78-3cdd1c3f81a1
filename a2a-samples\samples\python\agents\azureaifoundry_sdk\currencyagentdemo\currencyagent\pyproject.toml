[project]
name = "currencyagent"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "a2a-sdk>=0.2.5",
    "azure-ai-agents>=1.0.0",
    "azure-ai-projects>=1.0.0b11",
    "azure-identity>=1.23.0",
    "click>=8.2.1",
    "httpx>=0.28.1",
    "mcp>=1.9.2",
    "nest-asyncio>=1.6.0",
    "python-dotenv>=1.1.0",
    "starlette>=0.47.0",
    "uvicorn>=0.34.3",
]
