#!/usr/bin/env python3
"""
Character Agent数据模型
定义角色相关的数据结构
"""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from enum import Enum


class GenderType(str, Enum):
    """性别类型"""
    MALE = "男"
    FEMALE = "女"
    OTHER = "其他"


class AgeGroup(str, Enum):
    """年龄组"""
    CHILD = "儿童"
    TEENAGER = "青少年"
    YOUNG_ADULT = "青年"
    MIDDLE_AGED = "中年"
    ELDERLY = "老年"


class PersonalityTrait(BaseModel):
    """性格特征"""
    name: str = Field(description="特征名称")
    description: str = Field(description="特征描述")
    intensity: int = Field(ge=1, le=10, description="强度等级(1-10)")


class Appearance(BaseModel):
    """外貌描述"""
    height: Optional[str] = Field(None, description="身高")
    build: Optional[str] = Field(None, description="体型")
    hair_color: Optional[str] = Field(None, description="发色")
    hair_style: Optional[str] = Field(None, description="发型")
    eye_color: Optional[str] = Field(None, description="眼色")
    skin_tone: Optional[str] = Field(None, description="肤色")
    distinctive_features: List[str] = Field(default_factory=list, description="显著特征")
    clothing_style: Optional[str] = Field(None, description="穿衣风格")


class Background(BaseModel):
    """角色背景"""
    family: Optional[str] = Field(None, description="家庭背景")
    education: Optional[str] = Field(None, description="教育背景")
    occupation: Optional[str] = Field(None, description="职业")
    social_status: Optional[str] = Field(None, description="社会地位")
    hometown: Optional[str] = Field(None, description="家乡")
    key_experiences: List[str] = Field(default_factory=list, description="关键经历")
    secrets: List[str] = Field(default_factory=list, description="秘密")


class Relationship(BaseModel):
    """角色关系"""
    character_name: str = Field(description="关联角色名称")
    relationship_type: str = Field(description="关系类型")
    description: str = Field(description="关系描述")
    intimacy_level: int = Field(ge=1, le=10, description="亲密度(1-10)")
    conflict_potential: int = Field(ge=1, le=10, description="冲突潜力(1-10)")


class CharacterArc(BaseModel):
    """角色成长弧线"""
    starting_point: str = Field(description="起始状态")
    growth_goal: str = Field(description="成长目标")
    key_challenges: List[str] = Field(description="关键挑战")
    transformation_moments: List[str] = Field(description="转变时刻")
    ending_point: str = Field(description="结束状态")


class Character(BaseModel):
    """完整角色模型"""
    # 基本信息
    name: str = Field(description="角色姓名")
    nickname: Optional[str] = Field(None, description="昵称/外号")
    age: int = Field(ge=0, le=200, description="年龄")
    age_group: AgeGroup = Field(description="年龄组")
    gender: GenderType = Field(description="性别")
    
    # 角色定位
    role_type: str = Field(description="角色类型(主角/配角/反派等)")
    importance: int = Field(ge=1, le=10, description="重要程度(1-10)")
    
    # 详细信息
    appearance: Appearance = Field(description="外貌描述")
    personality_traits: List[PersonalityTrait] = Field(description="性格特征")
    background: Background = Field(description="角色背景")
    
    # 关系网络
    relationships: List[Relationship] = Field(default_factory=list, description="角色关系")
    
    # 成长弧线
    character_arc: Optional[CharacterArc] = Field(None, description="角色成长弧线")
    
    # 创作相关
    dialogue_style: Optional[str] = Field(None, description="对话风格")
    catchphrase: Optional[str] = Field(None, description="口头禅")
    internal_motivation: Optional[str] = Field(None, description="内在动机")
    external_goal: Optional[str] = Field(None, description="外在目标")
    
    # 元数据
    tags: List[str] = Field(default_factory=list, description="标签")
    notes: Optional[str] = Field(None, description="备注")
    created_at: Optional[str] = Field(None, description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")


class CharacterResponseFormat(BaseModel):
    """角色创建响应格式"""
    character: Character = Field(description="创建的角色")
    creation_notes: List[str] = Field(default_factory=list, description="创建说明")
    suggestions: List[str] = Field(default_factory=list, description="优化建议")


class RelationshipNetwork(BaseModel):
    """角色关系网络"""
    characters: List[str] = Field(description="角色列表")
    relationships: List[Relationship] = Field(description="关系列表")
    network_analysis: Dict[str, Any] = Field(description="网络分析")
    conflict_points: List[str] = Field(description="冲突点")
    story_potential: List[str] = Field(description="故事潜力")


class CharacterDevelopmentPlan(BaseModel):
    """角色发展计划"""
    character_name: str = Field(description="角色名称")
    current_state: str = Field(description="当前状态")
    development_stages: List[Dict[str, str]] = Field(description="发展阶段")
    key_events: List[str] = Field(description="关键事件")
    growth_metrics: Dict[str, int] = Field(description="成长指标")