#!/usr/bin/env python3
"""
配置管理器
统一管理所有Agent的配置和环境变量
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
import logging

@dataclass
class AgentConfig:
    """单个Agent的配置"""
    name: str
    host: str = "localhost"
    port: int = 10000
    writing_mode: str = "ai"  # ai, mcp, hybrid
    api_key: Optional[str] = None
    model_name: str = "gemini-2.5-pro-preview-06-05"
    max_retries: int = 3
    timeout: int = 30
    enabled: bool = True

@dataclass
class SystemConfig:
    """系统整体配置"""
    project_name: str = "A2A多Agent写作助手"
    version: str = "1.0.0"
    log_level: str = "INFO"
    data_dir: str = "data"
    templates_dir: str = "templates"
    agents: Dict[str, AgentConfig] = None
    
    def __post_init__(self):
        if self.agents is None:
            self.agents = {}

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.project_root = Path(__file__).parent.parent
        self.config_file = config_file or self.project_root / "config" / "system_config.json"
        self.env_file = self.project_root / ".env"
        self.config = SystemConfig()
        self.logger = self._setup_logger()
        
        # 加载配置
        self._load_env_variables()
        self._load_config_file()
        self._setup_default_agents()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger("ConfigManager")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger
    
    def _load_env_variables(self):
        """加载环境变量"""
        # 从.env文件加载
        if self.env_file.exists():
            with open(self.env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
        
        # 验证必要的环境变量 (支持OpenAI兼容API)
        required_vars = ['OPENAI_API_KEY']
        missing_vars = []
        
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            self.logger.warning(f"缺少环境变量: {', '.join(missing_vars)}")
            self.logger.warning("请在.env文件中设置这些变量")
    
    def _load_config_file(self):
        """加载配置文件"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 更新系统配置
                for key, value in config_data.items():
                    if key != 'agents' and hasattr(self.config, key):
                        setattr(self.config, key, value)
                
                # 加载Agent配置
                if 'agents' in config_data:
                    for agent_name, agent_data in config_data['agents'].items():
                        # 移除name字段，因为它已经在构造函数中设置
                        agent_config_data = {k: v for k, v in agent_data.items() if k != 'name'}
                        self.config.agents[agent_name] = AgentConfig(
                            name=agent_name,
                            **agent_config_data
                        )
                
                self.logger.info(f"配置文件加载成功: {self.config_file}")
                
            except Exception as e:
                self.logger.error(f"加载配置文件失败: {e}")
    
    def _setup_default_agents(self):
        """设置默认的Agent配置"""
        default_agents = {
            "plot_agent": AgentConfig(
                name="plot_agent",
                port=10002,
                writing_mode="ai"
            ),
            "character_agent": AgentConfig(
                name="character_agent", 
                port=10003,
                writing_mode="ai"
            ),
            "content_agent": AgentConfig(
                name="content_agent",
                port=10004,
                writing_mode="ai"
            ),
            "host_agent": AgentConfig(
                name="host_agent",
                port=10001,
                writing_mode="ai"
            )
        }
        
        # 只添加不存在的Agent配置
        for agent_name, agent_config in default_agents.items():
            if agent_name not in self.config.agents:
                self.config.agents[agent_name] = agent_config
    
    def get_agent_config(self, agent_name: str) -> Optional[AgentConfig]:
        """获取指定Agent的配置"""
        return self.config.agents.get(agent_name)
    
    def get_system_config(self) -> SystemConfig:
        """获取系统配置"""
        return self.config
    
    def update_agent_config(self, agent_name: str, **kwargs):
        """更新Agent配置"""
        if agent_name in self.config.agents:
            agent_config = self.config.agents[agent_name]
            for key, value in kwargs.items():
                if hasattr(agent_config, key):
                    setattr(agent_config, key, value)
        else:
            self.config.agents[agent_name] = AgentConfig(name=agent_name, **kwargs)
    
    def save_config(self):
        """保存配置到文件"""
        try:
            # 确保配置目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 转换为可序列化的字典
            config_dict = {
                "project_name": self.config.project_name,
                "version": self.config.version,
                "log_level": self.config.log_level,
                "data_dir": self.config.data_dir,
                "templates_dir": self.config.templates_dir,
                "agents": {
                    name: asdict(config) 
                    for name, config in self.config.agents.items()
                }
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"配置已保存到: {self.config_file}")
            
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
    
    def validate_config(self) -> List[str]:
        """验证配置的有效性"""
        errors = []
        
        # 验证API密钥 (支持OpenAI兼容API)
        if not os.getenv('OPENAI_API_KEY'):
            errors.append("缺少OPENAI_API_KEY环境变量")
        
        # 验证端口冲突
        ports = [config.port for config in self.config.agents.values() if config.enabled]
        if len(ports) != len(set(ports)):
            errors.append("存在端口冲突")
        
        # 验证目录
        data_dir = self.project_root / self.config.data_dir
        templates_dir = self.project_root / self.config.templates_dir
        
        if not data_dir.exists():
            errors.append(f"数据目录不存在: {data_dir}")
        
        if not templates_dir.exists():
            errors.append(f"模板目录不存在: {templates_dir}")
        
        return errors
    
    def get_enabled_agents(self) -> List[str]:
        """获取启用的Agent列表"""
        return [
            name for name, config in self.config.agents.items() 
            if config.enabled
        ]
    
    def print_config_summary(self):
        """打印配置摘要"""
        print("🔧 系统配置摘要")
        print("=" * 50)
        print(f"项目名称: {self.config.project_name}")
        print(f"版本: {self.config.version}")
        print(f"日志级别: {self.config.log_level}")
        print(f"数据目录: {self.config.data_dir}")
        print(f"模板目录: {self.config.templates_dir}")
        print()
        
        print("🤖 Agent配置:")
        for name, config in self.config.agents.items():
            status = "✅ 启用" if config.enabled else "❌ 禁用"
            print(f"  {name}: {config.host}:{config.port} ({config.writing_mode}模式) {status}")
        
        # 验证配置
        errors = self.validate_config()
        if errors:
            print("\n⚠️ 配置问题:")
            for error in errors:
                print(f"  - {error}")
        else:
            print("\n✅ 配置验证通过")

# 全局配置管理器实例
config_manager = ConfigManager()

def get_config_manager() -> ConfigManager:
    """获取配置管理器实例"""
    return config_manager

if __name__ == "__main__":
    # 测试配置管理器
    cm = ConfigManager()
    cm.print_config_summary()
    
    # 保存默认配置
    cm.save_config()